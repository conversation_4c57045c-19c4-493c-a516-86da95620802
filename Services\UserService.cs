using System.Linq;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// User service - uses DatabaseService for all database operations
    /// </summary>
    public class UserService
    {
        private readonly IDatabaseService _databaseService;
        private readonly ILoggerService? _logger;

        public UserService(IDatabaseService databaseService)
        {
            _databaseService = databaseService;

            // Get logger if available
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
            }
        }

        public async Task<User?> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                _logger?.LogInfo($"UserService: Attempting to authenticate user '{username}'");

                var user = await _databaseService.AuthenticateUserAsync(username, password);

                if (user != null)
                {
                    _logger?.LogInfo("UserService: User authenticated successfully");
                }
                else
                {
                    _logger?.LogWarning($"UserService: Authentication failed for user '{username}'");
                }

                return user;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error authenticating user: {ex.Message}", ex);
                return null;
            }
        }

        public async Task<User?> AuthenticateRfidAsync(string rfidCode)
        {
            try
            {
                _logger?.LogInfo($"UserService: Authenticating RFID code: '{rfidCode}'");

                var user = await _databaseService.AuthenticateUserByRfidAsync(rfidCode.ToUpper());

                if (user != null)
                {
                    _logger?.LogInfo("UserService: RFID authentication successful");
                }
                else
                {
                    _logger?.LogWarning($"UserService: RFID authentication failed for code '{rfidCode}'");
                }

                return user;
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error authenticating RFID: {ex.Message}", ex);
                return null;
            }
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                _logger?.LogInfo("UserService: Getting all users");
                return await _databaseService.GetAllUsersAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting users: {ex.Message}", ex);
                return new List<User>();
            }
        }

        public async Task<bool> CreateUserAsync(User user)
        {
            try
            {
                _logger?.LogInfo($"UserService: Creating user '{user.Username}'");
                user.Id = 0; // Ensure it's treated as new user
                return await _databaseService.SaveUserAsync(user);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error creating user: {ex.Message}", ex);
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                _logger?.LogInfo($"UserService: Updating user '{user.Username}' (ID: {user.Id})");
                return await _databaseService.SaveUserAsync(user);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error updating user: {ex.Message}", ex);
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                _logger?.LogInfo($"UserService: Deleting user with ID: {userId}");
                return await _databaseService.DeleteUserAsync(userId);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error deleting user: {ex.Message}", ex);
                return false;
            }
        }

        public async Task<User?> GetUserByIdAsync(int userId)
        {
            try
            {
                _logger?.LogInfo($"UserService: Getting user by ID: {userId}");
                var users = await _databaseService.GetAllUsersAsync();
                return users.FirstOrDefault(u => u.Id == userId);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error getting user by ID: {ex.Message}", ex);
                return null;
            }
        }

        public async Task<bool> IsUsernameExistsAsync(string username, int excludeUserId = 0)
        {
            try
            {
                _logger?.LogInfo($"UserService: Checking if username '{username}' exists (excluding ID: {excludeUserId})");
                var users = await _databaseService.GetAllUsersAsync();
                return users.Any(u => u.Username.Equals(username, StringComparison.OrdinalIgnoreCase) && u.Id != excludeUserId);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error checking username exists: {ex.Message}", ex);
                return false;
            }
        }
    }
}
