﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZoomableApp.Models;

namespace ZoomableApp.PLC
{
    public class PlcRegisterInfo
    {
        public PlcDeviceAddress LogicalName { get; } // Tên logic từ Enum
        public string PhysicalAddress { get; }    // Đ<PERSON>a chỉ thực trên PLC (VD: "D100", "M0", "X0")
        public PlcDataType DataType { get; }
        public int Length { get; } // Đối với STRING (số WORD) hoặc mảng

        public PlcRegisterInfo(PlcDeviceAddress logicalName, string physicalAddress, PlcDataType dataType, int length = 1)
        {
            LogicalName = logicalName;
            PhysicalAddress = physicalAddress;
            DataType = dataType;
            Length = length; // Mặc định là 1 (cho BIT, WORD, DWORD)
        }

        public override string ToString()
        {
            return $"{LogicalName} ({PhysicalAddress}, {DataType}, Len:{Length})";
        }
    }
}
