using System;
using System.Data;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace ZoomableApp.Converters
{
    public class CurrentTimeRowHighlightConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DataRowView rowView)
            {
                var row = rowView.Row;
                var currentTime = DateTime.Now;
                
                // Tìm cột thời gian trong dữ liệu
                string timeValue = FindTimeValue(row);
                
                if (!string.IsNullOrEmpty(timeValue))
                {
                    // Thử parse thời gian từ dữ liệu
                    if (TryParseTimeFromData(timeValue, out DateTime scheduleTime))
                    {
                        // Kiểm tra nếu thời gian hiện tại nằm trong khoảng thời gian của dòng này
                        if (IsCurrentTimeInSchedule(currentTime, scheduleTime))
                        {
                            return new SolidColorBrush(Color.FromRgb(255, 235, 59)); // Màu vàng Material Design
                        }
                    }
                }
            }
            
            return new SolidColorBrush(Colors.Transparent);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private string FindTimeValue(DataRow row)
        {
            // Tìm cột có chứa thông tin thời gian
            foreach (DataColumn column in row.Table.Columns)
            {
                string columnName = column.ColumnName.ToLower();
                
                // Kiểm tra tên cột có chứa từ khóa thời gian
                if (columnName.Contains("time") || columnName.Contains("thời gian") || 
                    columnName.Contains("giờ") || columnName.Contains("hour") ||
                    columnName.Contains("start") || columnName.Contains("bắt đầu") ||
                    columnName.Contains("schedule") || columnName.Contains("lịch"))
                {
                    return row[column].ToString();
                }
            }
            
            // Nếu không tìm thấy cột thời gian, thử tìm trong các cột đầu tiên
            if (row.Table.Columns.Count > 0)
            {
                for (int i = 0; i < Math.Min(3, row.Table.Columns.Count); i++)
                {
                    string value = row[i].ToString();
                    if (ContainsTimePattern(value))
                    {
                        return value;
                    }
                }
            }
            
            return string.Empty;
        }

        private bool ContainsTimePattern(string value)
        {
            if (string.IsNullOrEmpty(value)) return false;
            
            // Kiểm tra pattern thời gian như HH:mm, HH:mm:ss
            return System.Text.RegularExpressions.Regex.IsMatch(value, @"\d{1,2}:\d{2}");
        }

        private bool TryParseTimeFromData(string timeValue, out DateTime scheduleTime)
        {
            scheduleTime = DateTime.MinValue;
            
            if (string.IsNullOrEmpty(timeValue)) return false;
            
            try
            {
                // Thử parse với nhiều format khác nhau
                string[] timeFormats = {
                    "HH:mm",
                    "H:mm", 
                    "HH:mm:ss",
                    "H:mm:ss",
                    "hh:mm tt",
                    "h:mm tt"
                };
                
                foreach (string format in timeFormats)
                {
                    if (DateTime.TryParseExact(timeValue.Trim(), format, null, DateTimeStyles.None, out DateTime parsedTime))
                    {
                        // Tạo DateTime với ngày hôm nay và thời gian từ dữ liệu
                        scheduleTime = DateTime.Today.Add(parsedTime.TimeOfDay);
                        return true;
                    }
                }
                
                // Thử parse tự do
                if (DateTime.TryParse(timeValue, out DateTime generalParsed))
                {
                    scheduleTime = DateTime.Today.Add(generalParsed.TimeOfDay);
                    return true;
                }
            }
            catch
            {
                // Ignore parsing errors
            }
            
            return false;
        }

        private bool IsCurrentTimeInSchedule(DateTime currentTime, DateTime scheduleTime)
        {
            // Tạo khoảng thời gian ±15 phút xung quanh thời gian lên lịch để chính xác hơn
            var startWindow = scheduleTime.AddMinutes(-15);
            var endWindow = scheduleTime.AddMinutes(15);

            // Kiểm tra nếu thời gian hiện tại nằm trong khoảng này
            return currentTime >= startWindow && currentTime <= endWindow;
        }
    }
}
