using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;
using ZoomableApp.Models;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Converters
{
    /// <summary>
    /// Converter to highlight daily plan items where current time falls between Start time and Stop time
    /// </summary>
    public class DailyPlanItemHighlightConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DailyPlanItem planItem)
            {
                var currentTime = DateTime.Now;

                // Parse start and stop times
                if (TryParseTime(planItem.StartTime, out DateTime startTime) &&
                    TryParseTime(planItem.StopTime, out DateTime stopTime))
                {
                    // Check if current time falls between start and stop time (highest priority)
                    if (IsCurrentTimeInRange(currentTime, startTime, stopTime))
                    {
                        return new SolidColorBrush(Color.FromRgb(255, 235, 59)); // Yellow highlight for current time
                    }
                }

                // Fallback to status-based coloring
                return GetStatusBasedColor(planItem.Status);
            }

            return new SolidColorBrush(Colors.Transparent);
        }

        private SolidColorBrush GetStatusBasedColor(PlanItemStatus status)
        {
            switch (status)
            {
                case PlanItemStatus.NotStarted:
                    return new SolidColorBrush(Colors.Transparent);
                case PlanItemStatus.InProgress:
                    return new SolidColorBrush(Color.FromRgb(33, 150, 243)); // Blue
                case PlanItemStatus.Completed:
                    return new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                default:
                    return new SolidColorBrush(Colors.Transparent);
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private bool TryParseTime(string timeString, out DateTime time)
        {
            time = DateTime.MinValue;
            
            if (string.IsNullOrEmpty(timeString)) return false;
            
            try
            {
                // Try different time formats
                string[] timeFormats = {
                    "HH:mm",
                    "H:mm", 
                    "HH:mm:ss",
                    "H:mm:ss",
                    "hh:mm tt",
                    "h:mm tt"
                };

                foreach (string format in timeFormats)
                {
                    if (DateTime.TryParseExact(timeString.Trim(), format, null, DateTimeStyles.None, out time))
                    {
                        // Set the date to today for comparison
                        time = DateTime.Today.Add(time.TimeOfDay);
                        return true;
                    }
                }

                // Try general parsing as fallback
                if (DateTime.TryParse(timeString, out time))
                {
                    time = DateTime.Today.Add(time.TimeOfDay);
                    return true;
                }
            }
            catch
            {
                // Ignore parsing errors
            }
            
            return false;
        }

        private bool IsCurrentTimeInRange(DateTime currentTime, DateTime startTime, DateTime stopTime)
        {
            var currentTimeOfDay = currentTime.TimeOfDay;
            var startTimeOfDay = startTime.TimeOfDay;
            var stopTimeOfDay = stopTime.TimeOfDay;

            // Handle normal case (start < stop, same day)
            if (startTimeOfDay <= stopTimeOfDay)
            {
                return currentTimeOfDay >= startTimeOfDay && currentTimeOfDay <= stopTimeOfDay;
            }
            // Handle overnight case (start > stop, crosses midnight)
            else
            {
                return currentTimeOfDay >= startTimeOfDay || currentTimeOfDay <= stopTimeOfDay;
            }
        }
    }
}
