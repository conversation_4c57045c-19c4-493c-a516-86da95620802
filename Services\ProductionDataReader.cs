using System.IO;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service to read production data from PLC - does NOT handle database operations
    /// Database operations are handled by DatabaseService
    /// </summary>
    public class ProductionDataReader
    {
        private readonly PlcConnectionManager _plcManager;

        public ProductionDataReader(PlcConnectionManager plcManager)
        {
            _plcManager = plcManager;
        }

        /// <summary>
        /// Read production data from PLC and create ProductionData object
        /// Does NOT save to database - caller must use DatabaseService for that
        /// </summary>
        public async Task<ProductionData> ReadProductionDataFromPlcAsync(string station = "ALL")
        {
            try
            {
                var productionData = new ProductionData
                {
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    Station = station,
                    WorkShift = UserSession.CurrentShift?.Name ?? "Unknown",
                    CreatedBy = "System"
                };

                // Read data from PLC_MainLine
                if (_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    var plcService = _plcManager.GetPlcService("PLC_MainLine");
                    
                    // Read production quantities
                    var productOkResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                    if (productOkResult.IsSuccess)
                        productionData.Product_OK = Convert.ToInt32(productOkResult.Value);

                    var productNgResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                    if (productNgResult.IsSuccess)
                        productionData.Product_NG = Convert.ToInt32(productNgResult.Value);

                    var productTotalResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductTotal);
                    if (productTotalResult.IsSuccess)
                        productionData.Product_Total = Convert.ToInt32(productTotalResult.Value);

                    // Read completion time (average from stations)
                    float totalCompleteTime = 0;
                    int validStations = 0;
                    
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Timecompletest{i}");
                    //    var timeResult = await plcService.ReadAsync(stationEnum);
                    //    if (timeResult.IsSuccess)
                    //    {
                    //        totalCompleteTime += Convert.ToSingle(timeResult.Value);
                    //        validStations++;
                    //    }
                    //}
                    
                    if (validStations > 0)
                        productionData.Time_Complete = totalCompleteTime / validStations;

                    // Read delay time (sum from stations)
                    int totalDelayTime = 0;
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Timedelayst{i}");
                    //    var delayResult = await plcService.ReadAsync(stationEnum);
                    //    if (delayResult.IsSuccess)
                    //        totalDelayTime += Convert.ToInt32(delayResult.Value);
                    //}
                    productionData.Time_Delay = totalDelayTime;

                    // Read stop time (sum from stations)
                    int totalStopTime = 0;
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Timestopst{i}");
                    //    var stopResult = await plcService.ReadAsync(stationEnum);
                    //    if (stopResult.IsSuccess)
                    //        totalStopTime += Convert.ToInt32(stopResult.Value);
                    //}
                    productionData.Time_Stop = totalStopTime;

                    // Read stop count (sum from stations)
                    int totalStopCount = 0;
                    //for (int i = 1; i <= 26; i++)
                    //{
                    //    var stationEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"Numberstopst{i}");
                    //    var stopCountResult = await plcService.ReadAsync(stationEnum);
                    //    if (stopCountResult.IsSuccess)
                    //        totalStopCount += Convert.ToInt32(stopCountResult.Value);
                    //}
                    productionData.Number_Stop = totalStopCount;

                    // Read error codes
                    var errorCodes = new System.Collections.Generic.List<string>();
                    //int errorCount = 0;
                    //for (int i = 0; i <= 90; i++)
                    //{
                    //    var errorEnum = (PlcDeviceAddress)Enum.Parse(typeof(PlcDeviceAddress), $"M40{i:D2}");
                    //    var errorResult = await plcService.ReadAsync(errorEnum);
                    //    if (errorResult.IsSuccess && Convert.ToBoolean(errorResult.Value))
                    //    {
                    //        errorCodes.Add($"M40{i:D2}");
                    //        errorCount++;
                    //    }
                    //}

                    productionData.Error_Code = string.Join(", ", errorCodes);
                    productionData.Error_Text = GetErrorDescription(productionData.Error_Code);
                }

                return productionData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProductionDataReader: Error reading from PLC: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get optimized shift actual calculation
        /// Current shift actual = Total daily actual - Previous shifts actual (from database)
        /// </summary>
        public async Task<int> GetOptimizedShiftActualAsync(string shiftName, IDatabaseService databaseService)
        {
            try
            {
                // Get current total actual from PLC
                if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                    return 0;

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                var okResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                var ngResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                
                int currentTotalActual = 0;
                if (okResult.IsSuccess) currentTotalActual += Convert.ToInt32(okResult.Value);
                if (ngResult.IsSuccess) currentTotalActual += Convert.ToInt32(ngResult.Value);

                // Get previous shifts actual from database (today only)
                var previousShiftsActual = await databaseService.GetTodayPreviousShiftsActualAsync(shiftName);

                // Current shift actual = Total - Previous shifts
                int currentShiftActual = currentTotalActual - previousShiftsActual;
                
                Console.WriteLine($"ProductionDataReader: Optimized shift actual calculation: Total={currentTotalActual}, Previous={previousShiftsActual}, Current={currentShiftActual}");
                
                return Math.Max(0, currentShiftActual); // Ensure non-negative
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ProductionDataReader: Error calculating optimized shift actual: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Error description based on error code from fault_mapping.json
        /// </summary>
        private string GetErrorDescription(string errorCode)
        {
            if (string.IsNullOrEmpty(errorCode))
                return "";

            try
            {
                // Handle multiple error codes separated by comma
                var errorCodes = errorCode.Split(',', StringSplitOptions.RemoveEmptyEntries);
                var descriptions = new List<string>();

                foreach (var code in errorCodes)
                {
                    var trimmedCode = code.Trim();
                    var description = GetSingleErrorDescription(trimmedCode);
                    descriptions.Add(description);
                }

                return string.Join("; ", descriptions);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting error description: {ex.Message}");
                return $"Error description unavailable: {errorCode}";
            }
        }

        /// <summary>
        /// Get description for a single error code from fault_mapping.json
        /// </summary>
        private string GetSingleErrorDescription(string errorCode)
        {
            try
            {
                var faultMappingPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "fault_mapping.json");
                if (File.Exists(faultMappingPath))
                {
                    var jsonContent = File.ReadAllText(faultMappingPath);
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    var faultMapping = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, object>>>(jsonContent, options);

                    if (faultMapping != null && faultMapping.ContainsKey(errorCode))
                    {
                        var faultInfo = faultMapping[errorCode];
                        if (faultInfo.ContainsKey("message"))
                        {
                            return faultInfo["message"].ToString();
                        }
                    }
                }

                // Fallback for unmapped errors
                return $"Unknown error: {errorCode}";
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading fault mapping for {errorCode}: {ex.Message}");
                return $"Error description unavailable: {errorCode}";
            }
        }
    }
}
