using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Diagnostics;
using ClosedXML.Excel;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Base Excel service class providing common Excel operations
    /// Reduces code duplication across different Excel services
    /// </summary>
    public abstract class BaseExcelService
    {
        /// <summary>
        /// Validates if an Excel file exists and is accessible
        /// </summary>
        /// <param name="filePath">Path to the Excel file</param>
        /// <returns>True if file is valid and accessible</returns>
        protected bool IsValidExcelFile(string filePath)
        {
            try
            {
                return File.Exists(filePath) && new FileInfo(filePath).Length > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error validating file {filePath}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Ensures directory exists, creates it if necessary
        /// </summary>
        /// <param name="filePath">Full file path</param>
        protected void EnsureDirectoryExists(string filePath)
        {
            try
            {
                var directory = Path.GetDirectoryName(filePath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                    Console.WriteLine($"BaseExcelService: Created directory: {directory}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error creating directory for {filePath}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Creates a new Excel workbook with error handling
        /// </summary>
        /// <returns>New XLWorkbook instance</returns>
        protected XLWorkbook CreateWorkbook()
        {
            try
            {
                return new XLWorkbook();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error creating workbook: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Opens an existing Excel workbook with error handling
        /// </summary>
        /// <param name="filePath">Path to the Excel file</param>
        /// <returns>XLWorkbook instance</returns>
        protected XLWorkbook OpenWorkbook(string filePath)
        {
            try
            {
                if (!IsValidExcelFile(filePath))
                {
                    throw new FileNotFoundException($"Excel file not found or invalid: {filePath}");
                }
                return new XLWorkbook(filePath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error opening workbook {filePath}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets or creates a worksheet in the workbook
        /// </summary>
        /// <param name="workbook">The workbook</param>
        /// <param name="worksheetName">Name of the worksheet</param>
        /// <param name="replaceIfExists">Whether to replace existing worksheet</param>
        /// <returns>IXLWorksheet instance</returns>
        protected IXLWorksheet GetOrCreateWorksheet(XLWorkbook workbook, string worksheetName, bool replaceIfExists = false)
        {
            try
            {
                if (workbook.Worksheets.Contains(worksheetName))
                {
                    if (replaceIfExists)
                    {
                        workbook.Worksheets.Delete(worksheetName);
                        return workbook.Worksheets.Add(worksheetName);
                    }
                    return workbook.Worksheet(worksheetName);
                }
                return workbook.Worksheets.Add(worksheetName);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error getting/creating worksheet {worksheetName}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Applies standard header formatting to a cell
        /// </summary>
        /// <param name="cell">The cell to format</param>
        /// <param name="fontSize">Font size (default: 12)</param>
        /// <param name="isBold">Whether text should be bold (default: true)</param>
        protected void ApplyHeaderFormatting(IXLCell cell, int fontSize = 12, bool isBold = true)
        {
            try
            {
                cell.Style.Font.Bold = isBold;
                cell.Style.Font.FontSize = fontSize;
                cell.Style.Fill.BackgroundColor = XLColor.LightGray;
                cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error applying header formatting: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies title formatting to a cell range
        /// </summary>
        /// <param name="range">The range to format</param>
        /// <param name="fontSize">Font size (default: 16)</param>
        protected void ApplyTitleFormatting(IXLRange range, int fontSize = 16)
        {
            try
            {
                range.Style.Font.Bold = true;
                range.Style.Font.FontSize = fontSize;
                range.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                range.Merge();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error applying title formatting: {ex.Message}");
            }
        }

        /// <summary>
        /// Auto-fits columns in a worksheet
        /// </summary>
        /// <param name="worksheet">The worksheet</param>
        protected void AutoFitColumns(IXLWorksheet worksheet)
        {
            try
            {
                worksheet.Columns().AdjustToContents();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error auto-fitting columns: {ex.Message}");
            }
        }

        /// <summary>
        /// Saves workbook to file with error handling
        /// </summary>
        /// <param name="workbook">The workbook to save</param>
        /// <param name="filePath">Path where to save the file</param>
        protected void SaveWorkbook(XLWorkbook workbook, string filePath)
        {
            try
            {
                EnsureDirectoryExists(filePath);
                workbook.SaveAs(filePath);
                Console.WriteLine($"BaseExcelService: Successfully saved workbook to {filePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error saving workbook to {filePath}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Finds column index by header name (case-insensitive)
        /// </summary>
        /// <param name="worksheet">The worksheet to search</param>
        /// <param name="headerNames">Array of possible header names</param>
        /// <param name="headerRow">Row number containing headers (default: 1)</param>
        /// <returns>Column index (1-based) or -1 if not found</returns>
        protected int FindColumnByHeader(IXLWorksheet worksheet, string[] headerNames, int headerRow = 1)
        {
            try
            {
                var row = worksheet.Row(headerRow);
                var lastColumn = row.LastCellUsed()?.Address.ColumnNumber ?? 10;

                for (int col = 1; col <= lastColumn; col++)
                {
                    var cellValue = row.Cell(col).GetString().Trim().ToLower();
                    foreach (var headerName in headerNames)
                    {
                        if (cellValue == headerName.ToLower())
                        {
                            return col;
                        }
                    }
                }
                return -1;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error finding column by header: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// Gets worksheet names from a workbook
        /// </summary>
        /// <param name="filePath">Path to the Excel file</param>
        /// <returns>List of worksheet names</returns>
        protected List<string> GetWorksheetNames(string filePath)
        {
            var worksheetNames = new List<string>();
            try
            {
                using var workbook = OpenWorkbook(filePath);
                foreach (var worksheet in workbook.Worksheets)
                {
                    worksheetNames.Add(worksheet.Name);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error getting worksheet names from {filePath}: {ex.Message}");
            }
            return worksheetNames;
        }

        /// <summary>
        /// Converts DataTable to Excel worksheet
        /// </summary>
        /// <param name="dataTable">DataTable to convert</param>
        /// <param name="worksheet">Target worksheet</param>
        /// <param name="startRow">Starting row (default: 1)</param>
        /// <param name="includeHeaders">Whether to include column headers (default: true)</param>
        protected void DataTableToWorksheet(DataTable dataTable, IXLWorksheet worksheet, int startRow = 1, bool includeHeaders = true)
        {
            try
            {
                int currentRow = startRow;

                // Add headers if requested
                if (includeHeaders)
                {
                    for (int col = 0; col < dataTable.Columns.Count; col++)
                    {
                        var cell = worksheet.Cell(currentRow, col + 1);
                        cell.Value = dataTable.Columns[col].ColumnName;
                        ApplyHeaderFormatting(cell);
                    }
                    currentRow++;
                }

                // Add data rows
                for (int row = 0; row < dataTable.Rows.Count; row++)
                {
                    for (int col = 0; col < dataTable.Columns.Count; col++)
                    {
                        var cellValue = dataTable.Rows[row][col];
                        worksheet.Cell(currentRow, col + 1).Value = cellValue?.ToString() ?? "";
                    }
                    currentRow++;
                }

                AutoFitColumns(worksheet);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"BaseExcelService: Error converting DataTable to worksheet: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Generates a timestamped filename
        /// </summary>
        /// <param name="prefix">Filename prefix</param>
        /// <param name="extension">File extension (default: .csv)</param>
        /// <returns>Timestamped filename</returns>
        protected string GenerateTimestampedFileName(string prefix, string extension = ".csv")
        {
            var timestamp = DateTime.Now.ToString("ddMMyy");
            return $"{timestamp}_{prefix}{extension}";
        }
    }
}
