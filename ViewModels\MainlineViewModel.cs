using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using System.Windows.Threading;
using ZoomableApp.Services;
using ZoomableApp.PLC;
using ZoomableApp.Models;
using ZoomableApp.ViewModels;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel for Mainline application
    /// Same data as current MainWindow but loads Mainline layout only
    /// </summary>
    public class MainlineViewModel : INotifyPropertyChanged
    {
        private PlcConnectionManager _plcManager;
        private DataAggregatorService _dataAggregator;
        private IDatabaseService _databaseService;
        private ProductionDataReader _productionDataReader;
        private PlcFaultService _faultService;
        private DashboardDataService _dashboardDataService;
        private ILoggerService _logger;
        private DispatcherTimer _plcDataReaderTimer;
        private DispatcherTimer _shiftChangeTimer;

        // System status properties
        private string _systemStatus = "Đang khởi tạo...";
        private string _systemTotalOK = "--";
        private string _systemTotalNG = "--";
        private string _mainlineFaultStatus = "Không có lỗi";
        private bool _isPlcConnected = false;
        private string _todayTotalIdleTime = "0:00";
        private string _todayRemainingIdleTime = "2:00";
        private string _monthTotalIdleTime = "0:00";
        private string _monthRemainingIdleTime = "40:00";

        // Plan properties
        private string _todayDateText = DateTime.Now.ToString("dd/MM/yyyy");
        private string _currentProductText = "Chưa có sản phẩm";
        private string _editableModelName = "";
        private bool _isEditingModel = false;
        private ObservableCollection<DailyPlanItem> _dailyPlanItems = new ObservableCollection<DailyPlanItem>();
        private DailyPlanItem _selectedItem;
        public StopTimesChartViewModel StopChartViewModel { get; private set; }

        public event PropertyChangedEventHandler PropertyChanged;

        #region Properties

        public string SystemStatus
        {
            get => _systemStatus;
            set
            {
                if (_systemStatus != value)
                {
                    _systemStatus = value;
                    OnPropertyChanged(nameof(SystemStatus));
                }
            }
        }

        public string SystemTotalOK
        {
            get => _systemTotalOK;
            set
            {
                if (_systemTotalOK != value)
                {
                    _systemTotalOK = value;
                    OnPropertyChanged(nameof(SystemTotalOK));
                }
            }
        }

        public string SystemTotalNG
        {
            get => _systemTotalNG;
            set
            {
                if (_systemTotalNG != value)
                {
                    _systemTotalNG = value;
                    OnPropertyChanged(nameof(SystemTotalNG));
                }
            }
        }

        public string MainlineFaultStatus
        {
            get => _mainlineFaultStatus;
            set
            {
                if (_mainlineFaultStatus != value)
                {
                    _mainlineFaultStatus = value;
                    OnPropertyChanged(nameof(MainlineFaultStatus));
                }
            }
        }

        public bool IsPlcConnected
        {
            get => _isPlcConnected;
            set
            {
                if (_isPlcConnected != value)
                {
                    _isPlcConnected = value;
                    OnPropertyChanged(nameof(IsPlcConnected));
                }
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                if (_todayDateText != value)
                {
                    _todayDateText = value;
                    OnPropertyChanged(nameof(TodayDateText));
                }
            }
        }

        public string CurrentProductText
        {
            get => _currentProductText;
            set
            {
                if (_currentProductText != value)
                {
                    _currentProductText = value;
                    OnPropertyChanged(nameof(CurrentProductText));
                }
            }
        }

        public string EditableModelName
        {
            get => _editableModelName;
            set
            {
                if (_editableModelName != value)
                {
                    _editableModelName = value;
                    OnPropertyChanged(nameof(EditableModelName));
                }
            }
        }

        public bool IsEditingModel
        {
            get => _isEditingModel;
            set
            {
                if (_isEditingModel != value)
                {
                    _isEditingModel = value;
                    OnPropertyChanged(nameof(IsEditingModel));
                }
            }
        }

        public ObservableCollection<DailyPlanItem> DailyPlanItems
        {
            get => _dailyPlanItems;
            set
            {
                if (_dailyPlanItems != value)
                {
                    _dailyPlanItems = value;
                    OnPropertyChanged(nameof(DailyPlanItems));
                }
            }
        }

        public DailyPlanItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (_selectedItem != value)
                {
                    _selectedItem = value;
                    OnPropertyChanged(nameof(SelectedItem));
                }
            }
        }

        public bool IsSystemRunning
        {
            get
            {
                if (_plcManager == null)
                {
                    _logger?.LogWarning("[MainlineVM] PlcConnectionManager is null in IsSystemRunning");
                    return false;
                }
                bool isRunning = _plcManager.IsPlcConnected("PLC_MainLine");
                _logger?.LogInfo($"[MainlineVM] IsSystemRunning: {isRunning}");
                return isRunning; // Có thể thêm logic khác, ví dụ đọc register từ PLC
            }
        }
        // Layout property - always "Mainline" for this ViewModel
        public string CurrentLayout => "Mainline";

        #endregion

        #region Commands

        public ICommand StartNextCommand { get; private set; }
        public ICommand StartEditModelCommand { get; private set; }
        public ICommand SaveModelCommand { get; private set; }
        public ICommand CancelEditModelCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand MarkCompleteCommand { get; private set; }

        #endregion

        public MainlineViewModel()
        {
            _logger = ServiceContainer.GetService<ILoggerService>();
            _logger?.LogInfo("[MainlineVM] Initializing Mainline ViewModel");

            // Initialize commands
            InitializeCommands();

            // Initialize services
            InitializeServices();

            // Initialize timers
            InitializeTimers();

            // Load daily plan data
            LoadDailyPlan();

            // Start services
            _ = Task.Run(async () => await InitializeAsync());
        }

        private void InitializeCommands()
        {
            StartNextCommand = new RelayCommand<object>(ExecuteStartNext, CanExecuteStartNext);
            StartEditModelCommand = new RelayCommand<object>(ExecuteStartEditModel);
            SaveModelCommand = new RelayCommand<object>(ExecuteSaveModel, CanExecuteSaveModel);
            CancelEditModelCommand = new RelayCommand<object>(ExecuteCancelEditModel);
            RefreshCommand = new RelayCommand<object>(ExecuteRefresh);
            MarkCompleteCommand = new RelayCommand<object>(ExecuteMarkComplete, CanExecuteMarkComplete);
        }

        private void InitializeServices()
        {
            try
            {
                var plcMode = ConfigurationService.GetPlcMode();
                _logger?.LogInfo($"[MainlineVM] Initializing with PLC mode: {plcMode}");

                _plcManager = ServiceContainer.GetService<PlcConnectionManager>();
                if (_plcManager == null)
                {
                    _logger?.LogError("[MainlineVM] Failed to get PlcConnectionManager from ServiceContainer");
                }
                else
                {
                    _logger?.LogInfo($"[MainlineVM] PlcConnectionManager instance: {_plcManager.GetHashCode()}");
                }

                _dataAggregator = new DataAggregatorService();
                _databaseService = ServiceContainer.GetService<IDatabaseService>();
                _productionDataReader = new ProductionDataReader(_plcManager);
                _faultService = ServiceContainer.GetService<PlcFaultService>();
                _logger?.LogInfo("[MainlineVM] Services initialized successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error initializing services: {ex.Message}", ex);
            }
        }

        private void InitializeTimers()
        {
            // PLC data reader timer
            _plcDataReaderTimer = new DispatcherTimer();
            _plcDataReaderTimer.Interval = TimeSpan.FromMilliseconds(ConfigurationService.GetDashboardRefreshInterval());
            _plcDataReaderTimer.Tick += async (s, e) => await PlcDataReaderTimer_Tick();

            // Shift change timer
            _shiftChangeTimer = new DispatcherTimer();
            _shiftChangeTimer.Interval = TimeSpan.FromMinutes(1);
            _shiftChangeTimer.Tick += ShiftChangeTimer_Tick;
        }

        private async Task InitializeAsync()
        {
            try
            {
                _logger?.LogInfo("[MainlineVM] Starting async initialization");
                await ServiceContainer.WaitForPlcConnectionAsync();
                if (_plcManager == null)
                {
                    _logger?.LogError("[MainlineVM] PlcConnectionManager is null after WaitForPlcConnectionAsync");
                }
                else if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    _logger?.LogWarning("[MainlineVM] PLC_MainLine is not connected after initialization");
                }
                StopChartViewModel = new StopTimesChartViewModel();
                _logger?.LogInfo("[MainlineVM] StopChartViewModel initialized");
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    _plcDataReaderTimer.Start();
                    _shiftChangeTimer.Start();
                });
                _logger?.LogInfo("[MainlineVM] Async initialization completed");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error in async initialization: {ex.Message}", ex);
            }
        }

        private async Task AutoConnectPlcsAsync()
        {
            try
            {
                // Auto-connect all PLCs that are configured for auto-connect
                await _plcManager.ConnectAllAutoConnectPlcsAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error in auto-connect: {ex.Message}", ex);
            }
        }

        private async Task PlcDataReaderTimer_Tick()
        {
            try
            {
                // Read mainline-specific PLC data
                await ReadMainlineProductionDataAsync();
                await UpdateMainlineSystemStatusAsync();
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error in PLC data reader: {ex.Message}", ex);
            }
        }

        private async Task ReadMainlineProductionDataAsync()
        {
            try
            {
                if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                    return;

                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("MAINLINE");

                // Process mainline-specific data
                if (!string.IsNullOrEmpty(productionData.Error_Code))
                {
                    _logger?.LogWarning($"[MainlineVM] Mainline error detected: {productionData.Error_Code}");

                    // Save error data to database
                    productionData.ReportType = "MainlineError";
                    productionData.Notes = $"Mainline error: {productionData.Error_Code}";
                    await _databaseService.SaveProductionDataAsync(productionData);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error reading mainline production data: {ex.Message}", ex);
            }
        }

        private async Task UpdateMainlineSystemStatusAsync()
        {
            try
            {
                bool systemRunning = false;
                int totalOK = 0;
                int totalNG = 0;
                bool hasConnection = false;

                if (_plcManager?.IsPlcConnected("PLC_MainLine") == true)
                {
                    var plcService = _plcManager.GetPlcService("PLC_MainLine");
                    if (plcService != null)
                    {
                        hasConnection = true;

                        // Read mainline system status
                        var stopMode = await plcService.ReadAsync(PlcDeviceAddress.Stopmode);
                        if (stopMode.IsSuccess)
                        {
                            systemRunning = !Convert.ToBoolean(stopMode.Value);
                        }

                        // Read mainline production totals
                        var okResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                        if (okResult.IsSuccess)
                        {
                            totalOK = Convert.ToInt32(okResult.Value);
                        }

                        var ngResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                        if (ngResult.IsSuccess)
                        {
                            totalNG = Convert.ToInt32(ngResult.Value);
                        }

                        // Read mainline fault status
                        //var faultResult = await plcService.ReadAsync(PlcDeviceAddress.MainlineFaultCode);
                        //if (faultResult.IsSuccess)
                        //{
                        //    _faultService?.ProcessFaultData(faultResult.Value, "mainline");
                        //}
                    }
                }

                // Update UI properties
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    IsPlcConnected = hasConnection;
                    SystemStatus = hasConnection ? (systemRunning ? "Đang chạy" : "Dừng") : "Không kết nối";
                    SystemTotalOK = hasConnection ? totalOK.ToString() : "--";
                    SystemTotalNG = hasConnection ? totalNG.ToString() : "--";
                    MainlineFaultStatus = hasConnection ? "Không có lỗi" : "Không kết nối";
                });
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error updating mainline system status: {ex.Message}", ex);
            }
        }

        private void ShiftChangeTimer_Tick(object sender, EventArgs e)
        {
            // Handle mainline-specific shift changes
            var currentShift = WorkShiftHelper.GetCurrentShift();
            _logger?.LogInfo($"[MainlineVM] Current shift: {WorkShiftHelper.GetShiftName(currentShift)}");
        }

        public async Task RefreshDataAsync()
        {
            await UpdateMainlineSystemStatusAsync();
        }

        /// <summary>
        /// Get PlanViewModel for dashboard charts
        /// </summary>
        /// <returns>PlanViewModel instance or null if not initialized</returns>
        public PlanViewModel GetPlanViewModel()
        {
            // Get the actual PlanViewModel from ServiceContainer that has the loaded data
            var planViewModel = ServiceContainer.GetService<PlanViewModel>();
            if (planViewModel != null)
            {
                _logger?.LogInfo("[MainlineVM] Returning PlanViewModel from ServiceContainer");
                return planViewModel;
            }

            // Fallback: Try to get from MainWindow
            var mainWindow = System.Windows.Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                var mainWindowPlanVM = mainWindow.GetPlanViewModel();
                if (mainWindowPlanVM != null)
                {
                    _logger?.LogInfo("[MainlineVM] Returning PlanViewModel from MainWindow");
                    return mainWindowPlanVM;
                }
            }

            _logger?.LogWarning("[MainlineVM] No PlanViewModel found, returning null");
            return null;
        }

        /// <summary>
        /// Get PlcConnectionManager for dashboard charts
        /// </summary>
        /// <returns>PlcConnectionManager instance or null if not initialized</returns>
        public PlcConnectionManager GetPlcManager()
        {
            return _plcManager;
        }

        #region Command Methods

        private void ExecuteStartNext(object parameter)
        {
            // Implement start next logic
            _logger?.LogInfo("[MainlineVM] Start Next command executed");
        }

        private bool CanExecuteStartNext(object parameter)
        {
            return SelectedItem != null && SelectedItem.Status == PlanItemStatus.NotStarted;
        }

        private void ExecuteStartEditModel(object parameter)
        {
            IsEditingModel = true;
            EditableModelName = CurrentProductText;
        }

        private void ExecuteSaveModel(object parameter)
        {
            CurrentProductText = EditableModelName;
            IsEditingModel = false;
            _logger?.LogInfo($"[MainlineVM] Model saved: {CurrentProductText}");
        }

        private bool CanExecuteSaveModel(object parameter)
        {
            return IsEditingModel && !string.IsNullOrWhiteSpace(EditableModelName);
        }

        private void ExecuteCancelEditModel(object parameter)
        {
            IsEditingModel = false;
            EditableModelName = "";
        }

        private void ExecuteRefresh(object parameter)
        {
            // Implement refresh logic
            _logger?.LogInfo("[MainlineVM] Refresh command executed");
        }

        private void ExecuteMarkComplete(object parameter)
        {
            // Implement mark complete logic
            _logger?.LogInfo("[MainlineVM] Mark Complete command executed");
        }

        private bool CanExecuteMarkComplete(object parameter)
        {
            return SelectedItem != null && SelectedItem.Status == PlanItemStatus.InProgress;
        }

        #endregion

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void LoadDailyPlan()
        {
            try
            {
                _logger?.LogInfo("[MainlineVM] Loading daily plan data");

                // Get PlanViewModel from ServiceContainer
                var planViewModel = ServiceContainer.GetService<PlanViewModel>();
                if (planViewModel == null)
                {
                    _logger?.LogWarning("[MainlineVM] PlanViewModel not found in ServiceContainer");
                    return;
                }

                // Refresh PlanViewModel data first
                if (planViewModel.RefreshCommand.CanExecute(null))
                {
                    planViewModel.RefreshCommand.Execute(null);
                }

                // Load from PlanViewModel
                LoadDailyPlanFromPlanViewModel(planViewModel);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error loading daily plan: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        private void LoadDailyPlanFromPlanViewModel(PlanViewModel planViewModel)
        {
            try
            {
                var dailyData = planViewModel.DailyPlanData;
                if (dailyData == null || dailyData.Rows.Count == 0)
                {
                    _logger?.LogInfo("[MainlineVM] No daily data available from PlanViewModel");
                    DailyPlanItems.Clear();
                    CurrentProductText = "Chưa có sản phẩm";
                    return;
                }

                DailyPlanItems.Clear();

                foreach (System.Data.DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "",
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };

                    DailyPlanItems.Add(item);
                }

                // Update current product if we have items
                if (DailyPlanItems.Count > 0)
                {
                    // Set first item as current working
                    var firstItem = DailyPlanItems.First();
                    firstItem.Status = PlanItemStatus.InProgress;
                    SelectedItem = firstItem;
                    CurrentProductText = firstItem.ModelName;
                }
                else
                {
                    CurrentProductText = "Chưa có sản phẩm";
                }

                _logger?.LogInfo($"[MainlineVM] Loaded {DailyPlanItems.Count} plan items");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineVM] Error loading daily plan from PlanViewModel: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        public async void Dispose()
        {
            _plcDataReaderTimer?.Stop();
            _shiftChangeTimer?.Stop();
            if (_plcManager != null)
            {
                await _plcManager.DisconnectAllAsync();
            }
            _logger?.LogInfo("[MainlineVM] Mainline ViewModel disposed");
        }
    }
}
