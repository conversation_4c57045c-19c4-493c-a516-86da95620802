<UserControl x:Class="ZoomableApp.Views.ShiftPlanActualChartControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             xmlns:local="clr-namespace:ZoomableApp.Views">
    
    <Border BorderThickness="2" CornerRadius="8" Width="auto" Height="Auto">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10,8">
                <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
                      Fill="#3498DB" Width="auto" Height="auto" Margin="0,0,8,0" VerticalAlignment="Center"/>
                <TextBlock Text="Ca hiện tại - Kế hoạch vs Thực tế" FontWeight="Bold" Foreground="White" FontSize="12" VerticalAlignment="Center"/>
            </StackPanel>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="30"/>
                    <ColumnDefinition Width="263*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Vertical" VerticalAlignment="Center" Margin="10,-10,0,0">
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="📅 Plan: " FontSize="16" FontWeight="Bold" Foreground="#3498DB" />
                        <TextBlock Text="{Binding PlanQuantity}" FontSize="16" Foreground="White"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="✅ Actual: " FontSize="16" FontWeight="Bold" Foreground="#27AE60"/>
                        <TextBlock Text="{Binding ActualQuantity}" FontSize="16" Foreground="White"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,2">
                        <TextBlock Text="↔️ Gap: " FontSize="16" FontWeight="Bold" Foreground="#E67E22"/>
                        <TextBlock Text="{Binding Gap}" FontSize="16" Foreground="White"/>
                    </StackPanel>
                </StackPanel>

                <lvc:PieChart Grid.Column="3" x:Name="ShiftPlanChart"
                              Width="250"
                              Height="250"
                              Series="{Binding Series}"
                              InitialRotation="-180"
                              MaxAngle="180"
                              Background="Transparent">
                </lvc:PieChart>
            </Grid>

            <!-- Chart -->
            
        </Grid>
    </Border>
</UserControl>
