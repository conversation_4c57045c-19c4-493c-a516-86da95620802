using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;
using ZoomableApp.ViewModels;
using ZoomableApp.PLC;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Shift Plan vs Actual Chart (Panel 4 - Left)
    /// Hi<PERSON>n thị kế hoạch vs thực tế ca hiện tại dưới dạng half pie chart
    /// </summary>
    public class ShiftPlanActualChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private readonly PlanViewModel _planViewModel;
        private readonly PlcConnectionManager _plcManager;
        private readonly IDatabaseService _databaseService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ISeries> Series { get; set; } = new();
        
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        private int _planQuantity;
        public int PlanQuantity
        {
            get => _planQuantity;
            set
            {
                if (_planQuantity != value)
                {
                    _planQuantity = value;
                    OnPropertyChanged(nameof(PlanQuantity));
                }
            }
        }

        private int _actualQuantity;
        public int ActualQuantity
        {
            get => _actualQuantity;
            set
            {
                if (_actualQuantity != value)
                {
                    _actualQuantity = value;
                    OnPropertyChanged(nameof(ActualQuantity));
                }
            }
        }

        private int _gap;
        public int Gap
        {
            get => _gap;
            set
            {
                if (_gap != value)
                {
                    _gap = value;
                    OnPropertyChanged(nameof(Gap));
                }
            }
        }

        private double _achievementRate;
        public double AchievementRate
        {
            get => _achievementRate;
            set
            {
                if (Math.Abs(_achievementRate - value) > 0.0001)
                {
                    _achievementRate = value;
                    OnPropertyChanged(nameof(AchievementRate));
                }
            }
        }

        public string CurrentShift { get; private set; } = "";

        public ShiftPlanActualChartViewModel(PlanViewModel planViewModel = null, PlcConnectionManager plcManager = null)
        {
            _mockDataService = new MockDashboardDataService();
            _planViewModel = planViewModel;
            _plcManager = plcManager;
            _databaseService = ServiceContainer.GetService<IDatabaseService>();
            CurrentShift = GetCurrentShiftName();

            // Debug logging
            Console.WriteLine($"ShiftPlanActualChart: Constructor - PlanViewModel: {(_planViewModel != null ? "Available" : "NULL")}");
            Console.WriteLine($"ShiftPlanActualChart: Constructor - PlcManager: {(_plcManager != null ? "Available" : "NULL")}");
            Console.WriteLine($"ShiftPlanActualChart: Constructor - Current Shift: {CurrentShift}");

            // Subscribe to PlanViewModel property changes to detect when data is loaded
            if (_planViewModel != null)
            {
                _planViewModel.PropertyChanged += PlanViewModel_PropertyChanged;
            }

            InitializeTimer();
            //LoadDataAsync();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }

        /// <summary>
        /// Handle PlanViewModel property changes to reload data when plan data becomes available
        /// </summary>
        private void PlanViewModel_PropertyChanged(object sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(PlanViewModel.DailyPlanData))
            {
                Console.WriteLine("ShiftPlanActualChart: PlanViewModel.DailyPlanData changed, reloading data...");
                _ = Task.Run(async () => await LoadDataAsync());
            }
        }

        private async Task LoadDataAsync()
        {
            await Task.Run(async () =>
            {
                try
                {
                    // Check if PlanViewModel has data loaded
                    if (_planViewModel?.DailyPlanData == null || _planViewModel.DailyPlanData.Rows.Count == 0)
                    {
                        Console.WriteLine("ShiftPlanActualChart: PlanViewModel data not ready yet, using mock data");
                        //LoadMockData();
                        LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                        return;
                    }

                    // Load real data from PlanViewModel and PLC
                    await LoadRealDataAsync();
                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ShiftPlanActualChart: Error loading real data: {ex.Message}");
                    // Fallback to mock data
                    //LoadMockData();
                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
            });
        }

        /// <summary>
        /// Load real data from PlanViewModel and PLC for current shift
        /// </summary>
        private async Task LoadRealDataAsync()
        {
            // Get shift plan data from PlanViewModel
            int shiftPlanQuantity = GetShiftPlanQuantityFromPlanViewModel();

            // Get actual data from PLC for current shift
            int shiftActualQuantity = await GetShiftActualQuantityFromPlcAsync();

            // Note: Database saving removed to prevent spam - data is saved by scheduled timers in MainWindow

            // Update properties
            PlanQuantity = shiftPlanQuantity;
            ActualQuantity = shiftActualQuantity;
            Gap = shiftActualQuantity - shiftPlanQuantity;
            AchievementRate = shiftPlanQuantity > 0 ? Math.Round((double)shiftActualQuantity / shiftPlanQuantity * 100, 1) : 0;

            // Update chart
            UpdateChart();

            Console.WriteLine($"ShiftPlanActualChart: Real Data - Shift={CurrentShift}, Plan={shiftPlanQuantity}, Actual={shiftActualQuantity}, Gap={Gap}, Rate={AchievementRate:F1}%");
        }

        /// <summary>
        /// Update chart with current data
        /// </summary>
        private void UpdateChart()
        {
            Series.Clear();

            // Create half pie chart for Plan vs Actual
            if (ActualQuantity <= PlanQuantity)
            {
                // Actual doesn't exceed plan
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { ActualQuantity },
                    Name = $"Thực tế ({ActualQuantity})",
                    Fill = new SolidColorPaint(SKColors.LightGreen),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });

                var remaining = PlanQuantity - ActualQuantity;
                if (remaining > 0)
                {
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { remaining },
                        Name = $"Còn lại ({remaining})",
                        Fill = new SolidColorPaint(SKColors.LightGray),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        InnerRadius = 40,
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });
                }
            }
            else
            {
                // Actual exceeds plan
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { PlanQuantity },
                    Name = $"Kế hoạch ({PlanQuantity})",
                    Fill = new SolidColorPaint(SKColors.LightBlue),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });

                var excess = ActualQuantity - PlanQuantity;
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { excess },
                    Name = $"Vượt KH (+{excess})",
                    Fill = new SolidColorPaint(SKColors.Gold),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"+{point.PrimaryValue}"
                });
            }
        }

        /// <summary>
        /// Get shift plan quantity from PlanViewModel daily data for current shift
        /// </summary>
        private int GetShiftPlanQuantityFromPlanViewModel()
        {
            try
            {
                // For night shift, we need to check if we should use yesterday's or today's plan
                var currentTime = DateTime.Now;
                var planDate = WorkShiftHelper.GetShiftPlanDate(currentTime, CurrentShift);

                Console.WriteLine($"ShiftPlanActualChart: Current time: {currentTime:yyyy-MM-dd HH:mm}, Shift: {CurrentShift}, Plan date: {planDate:yyyy-MM-dd}");

                if (_planViewModel?.DailyPlanData == null || _planViewModel.DailyPlanData.Rows.Count == 0)
                {
                    Console.WriteLine("ShiftPlanActualChart: No daily plan data available");
                    return 0;
                }

                // For shift calculation, we'll take a portion of daily plan based on shift hours
                var dailyData = _planViewModel.DailyPlanData;
                int totalDailyPlanQuantity = 0;

                // First, let's log all available columns for debugging
                Console.WriteLine("ShiftPlanActualChart: Available columns:");
                foreach (System.Data.DataColumn column in dailyData.Columns)
                {
                    Console.WriteLine($"  - {column.ColumnName}");
                }

                foreach (System.Data.DataRow row in dailyData.Rows)
                {
                    // Look for the correct quantity column "Q'ty"
                    foreach (System.Data.DataColumn column in dailyData.Columns)
                    {
                        var columnName = column.ColumnName;
                        if (columnName == "Q'ty" || columnName.ToLower() == "qty" ||
                            columnName.ToLower() == "quantity" || columnName.Contains("Q'ty"))
                        {
                            if (int.TryParse(row[column].ToString(), out int quantity))
                            {
                                totalDailyPlanQuantity += quantity;
                                Console.WriteLine($"ShiftPlanActualChart: Found daily plan quantity {quantity} in column '{column.ColumnName}'");
                            }
                            else
                            {
                                Console.WriteLine($"ShiftPlanActualChart: Could not parse quantity from column '{column.ColumnName}', value: '{row[column]}'");
                            }
                        }
                    }
                }

                // Calculate shift portion based on shift hours (simplified calculation)
                int shiftPlanQuantity = CalculateShiftPortion(totalDailyPlanQuantity);

                Console.WriteLine($"ShiftPlanActualChart: Total daily plan: {totalDailyPlanQuantity}, Shift plan ({CurrentShift}): {shiftPlanQuantity}");
                return shiftPlanQuantity;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ShiftPlanActualChart: Error getting shift plan quantity: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Calculate shift portion of daily plan based on shift working hours
        /// </summary>
        private int CalculateShiftPortion(int totalDailyQuantity)
        {
            // Shift working hours (simplified)
            var shiftHours = CurrentShift switch
            {
                "Ca hành chính" => 9, // 8-17h = 9 hours
                "Ca sáng" => 5,       // 6-11h = 5 hours
                "Ca chiều" => 8,      // 14-22h = 8 hours
                "Ca đêm" => 8,        // 22-6h = 8 hours
                _ => 8                // Default 8 hours
            };

            // Total working hours per day (assuming 24h operation)
            int totalDailyHours = 24;

            // Calculate shift portion
            double shiftRatio = (double)shiftHours / totalDailyHours;
            int shiftQuantity = (int)Math.Round(totalDailyQuantity * shiftRatio);

            Console.WriteLine($"ShiftPlanActualChart: Shift {CurrentShift} = {shiftHours}h / {totalDailyHours}h = {shiftRatio:F2} ratio");
            return shiftQuantity;
        }

        private void LoadMockData()
        {
            var planData = _mockDataService.GetMockPlanActualData(true); // Current shift data

            PlanQuantity = planData.Plan;
            ActualQuantity = planData.Actual;
            Gap = planData.Gap;
            AchievementRate = PlanQuantity > 0 ? Math.Round((double)ActualQuantity / PlanQuantity * 100, 1) : 0;

            Series.Clear();

            // Tạo half pie chart cho Plan vs Actual
            if (ActualQuantity <= PlanQuantity)
            {
                // Actual không vượt plan
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { ActualQuantity },
                    InnerRadius = 40,
                    Fill = new SolidColorPaint(SKColors.LightGreen),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });

                var remaining = PlanQuantity - ActualQuantity;
                if (remaining > 0)
                {
                    Series.Add(new PieSeries<int>
                    {
                        Values = new[] { remaining },
                        Fill = new SolidColorPaint(SKColors.LightGray),
                        Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                        InnerRadius = 40,
                        DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                        DataLabelsSize = 12,
                        DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                        DataLabelsFormatter = point => $"{point.PrimaryValue}"
                    });
                }
            }
            else
            {
                // Actual vượt plan
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { PlanQuantity },
                    Fill = new SolidColorPaint(SKColors.LightBlue),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });

                var excess = ActualQuantity - PlanQuantity;
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { excess },
                    Fill = new SolidColorPaint(SKColors.Gold),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    InnerRadius = 40,
                    DataLabelsPaint = new SolidColorPaint(SKColors.Black),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"+{point.PrimaryValue}"
                });
            }

            // Update properties for binding
            OnPropertyChanged(nameof(PlanQuantity));
            OnPropertyChanged(nameof(ActualQuantity));
            OnPropertyChanged(nameof(Gap));
            OnPropertyChanged(nameof(AchievementRate));

            Console.WriteLine($"ShiftPlanActual: Plan={PlanQuantity}, Actual={ActualQuantity}, Gap={Gap}, Rate={AchievementRate:F1}%");
        }

        private string GetCurrentShiftName()
        {
            var hour = DateTime.Now.Hour;
            
            if (hour >= 6 && hour < 14)
                return "Ca sáng (06:00-14:00)";
            else if (hour >= 14 && hour < 22)
                return "Ca chiều (14:00-22:00)";
            else if (hour >= 8 && hour < 17)
                return "Ca hành chính (08:00-17:00)";
            else
                return "Ca đêm (22:00-06:00)";
        }

        /// <summary>
        /// Get actual quantity from PLC for current shift
        /// </summary>
        private async Task<int> GetShiftActualQuantityFromPlcAsync()
        {
            try
            {
                if (_plcManager == null || !_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    Console.WriteLine("ShiftPlanActualChart: PLC_MainLine not connected, returning 0");
                    return 0;
                }

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                if (plcService == null)
                {
                    Console.WriteLine("ShiftPlanActualChart: PLC service not available");
                    return 0;
                }

                // Read total production (OK + NG) from PLC for current shift
                int totalOK = 0;
                int totalNG = 0;

                // Read OK products
                var okResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductOk);
                if (okResult.IsSuccess)
                {
                    totalOK = Convert.ToInt32(okResult.Value);
                }

                // Read NG products
                var ngResult = await plcService.ReadAsync(PlcDeviceAddress.NumberProductNg);
                if (ngResult.IsSuccess)
                {
                    totalNG = Convert.ToInt32(ngResult.Value);
                }

                // For shift calculation, we need to get shift-specific data
                int totalActual = totalOK + totalNG;
                int shiftActual = CalculateShiftActualPortion(totalActual);

                Console.WriteLine($"ShiftPlanActualChart: PLC Data - Total OK={totalOK}, NG={totalNG}, Shift Actual ({CurrentShift})={shiftActual}");

                return shiftActual;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ShiftPlanActualChart: Error reading from PLC: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Calculate shift portion of total actual production
        /// </summary>
        private int CalculateShiftActualPortion(int totalActual)
        {
            // This is a simplified calculation
            var shiftHours = CurrentShift switch
            {
                "Ca hành chính" => 9,
                "Ca sáng" => 5,
                "Ca chiều" => 8,
                "Ca đêm" => 8,
                _ => 8
            };

            double shiftRatio = (double)shiftHours / 24;
            return (int)Math.Round(totalActual * shiftRatio);
        }



        /// <summary>
        /// Force refresh data
        /// </summary>
        public async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
