using System;
using System.Data;
using System.IO;
using System.Linq;
using ClosedXML.Excel;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service for reading idle plan data from Excel file
    /// </summary>
    public class IdlePlanService : BaseExcelService
    {
        private readonly string _idlePlansFilePath;
        private DataTable _cachedIdlePlansData;
        private DateTime _lastFileModified;

        public IdlePlanService()
        {
            // Use existing ConfigLoader pattern like other services
            var excelSettings = ConfigLoader.LoadExcelSettings();
            _idlePlansFilePath = excelSettings.IdlePlansFilePath;
        }

        /// <summary>
        /// Load idle plans data from Excel file
        /// </summary>
        /// <returns>DataTable containing idle plans data</returns>
        public DataTable LoadIdlePlansData()
        {
            try
            {
                if (string.IsNullOrEmpty(_idlePlansFilePath))
                {
                    Console.WriteLine("IdlePlanService: No idle plans file path configured");
                    return new DataTable();
                }

                if (!File.Exists(_idlePlansFilePath))
                {
                    Console.WriteLine($"IdlePlanService: Idle plans file not found: {_idlePlansFilePath}");
                    return new DataTable();
                }

                // Check if file has been modified since last load
                var fileInfo = new FileInfo(_idlePlansFilePath);
                if (_cachedIdlePlansData != null && fileInfo.LastWriteTime == _lastFileModified)
                {
                    Console.WriteLine("IdlePlanService: Using cached idle plans data");
                    return _cachedIdlePlansData;
                }

                Console.WriteLine($"IdlePlanService: Loading idle plans from: {_idlePlansFilePath}");

                using var workbook = new XLWorkbook(_idlePlansFilePath);
                var worksheet = workbook.Worksheet(1); // First worksheet

                var dataTable = new DataTable();
                var range = worksheet.RangeUsed();

                if (range == null)
                {
                    Console.WriteLine("IdlePlanService: Excel file is empty");
                    return new DataTable();
                }

                // Add columns from first row (headers)
                var firstRow = range.FirstRow();
                foreach (var cell in firstRow.Cells())
                {
                    var columnName = cell.GetString();
                    if (!string.IsNullOrEmpty(columnName))
                    {
                        dataTable.Columns.Add(columnName);
                    }
                }

                // Add data rows
                var dataRows = range.Rows().Skip(1); // Skip header row
                foreach (var row in dataRows)
                {
                    var dataRow = dataTable.NewRow();
                    var cellIndex = 0;
                    
                    foreach (var cell in row.Cells())
                    {
                        if (cellIndex < dataTable.Columns.Count)
                        {
                            var cellValue = cell.GetString();
                            dataRow[cellIndex] = cellValue;
                            cellIndex++;
                        }
                    }
                    
                    dataTable.Rows.Add(dataRow);
                }

                // Cache the data
                _cachedIdlePlansData = dataTable;
                _lastFileModified = fileInfo.LastWriteTime;

                Console.WriteLine($"IdlePlanService: Loaded {dataTable.Rows.Count} rows from idle plans Excel");
                return dataTable;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"IdlePlanService: Error loading idle plans: {ex.Message}");
                return new DataTable();
            }
        }

        /// <summary>
        /// Get daily plan idle time for a specific date
        /// </summary>
        /// <param name="date">Date to get plan for</param>
        /// <returns>Plan idle hours for the date</returns>
        public double GetDailyPlanIdleTime(DateTime date)
        {
            try
            {
                var idlePlansData = LoadIdlePlansData();
                if (idlePlansData.Rows.Count == 0)
                {
                    Console.WriteLine("IdlePlanService: No idle plans data available");
                    return 8.0; // Default 8 hours
                }

                var dateString = $"{date.Day:D2}/{date.Month:D2}";

                // Find Day and Plan columns
                string dayColumnName = null;
                string planColumnName = null;

                foreach (DataColumn column in idlePlansData.Columns)
                {
                    var columnName = column.ColumnName.ToLower();
                    if (dayColumnName == null && (columnName.Contains("day") || columnName.Contains("ngày") || columnName.Contains("date")))
                    {
                        dayColumnName = column.ColumnName;
                    }
                    if (planColumnName == null && (columnName.Contains("plan") || columnName.Contains("kế hoạch") || columnName.Contains("idle") || columnName.Contains("nghỉ")))
                    {
                        planColumnName = column.ColumnName;
                    }
                }

                if (dayColumnName != null && planColumnName != null)
                {
                    foreach (DataRow row in idlePlansData.Rows)
                    {
                        var dayValue = row[dayColumnName]?.ToString();
                        var planValue = row[planColumnName]?.ToString();

                        if (!string.IsNullOrEmpty(dayValue) && !string.IsNullOrEmpty(planValue))
                        {
                            if (dayValue.Contains(dateString))
                            {
                                // Parse plan hours (format: "1h" or "1 hours" or just "1")
                                var planText = planValue.ToLower().Replace("h", "").Replace("hours", "").Replace("giờ", "").Trim();
                                if (double.TryParse(planText, out double planHours))
                                {
                                    Console.WriteLine($"IdlePlanService: Found plan idle time for {dateString}: {planHours:F1}h");
                                    return planHours;
                                }
                            }
                        }
                    }
                }

                Console.WriteLine($"IdlePlanService: No plan found for {dateString}, using default 8h");
                return 8.0; // Default
            }
            catch (Exception ex)
            {
                Console.WriteLine($"IdlePlanService: Error getting daily plan: {ex.Message}");
                return 8.0; // Default fallback
            }
        }

        /// <summary>
        /// Get monthly plan idle time for current month
        /// </summary>
        /// <returns>Total plan idle hours for the month</returns>
        public double GetMonthlyPlanIdleTime()
        {
            try
            {
                var idlePlansData = LoadIdlePlansData();
                if (idlePlansData.Rows.Count == 0)
                {
                    Console.WriteLine("IdlePlanService: No idle plans data available");
                    return 240.0; // Default 8h * 30 days
                }

                double totalPlanIdleHours = 0;
                var currentMonth = DateTime.Now.Month;

                // Find Day and Plan columns
                string dayColumnName = null;
                string planColumnName = null;

                foreach (DataColumn column in idlePlansData.Columns)
                {
                    var columnName = column.ColumnName.ToLower();
                    if (dayColumnName == null && (columnName.Contains("day") || columnName.Contains("ngày") || columnName.Contains("date")))
                    {
                        dayColumnName = column.ColumnName;
                    }
                    if (planColumnName == null && (columnName.Contains("plan") || columnName.Contains("kế hoạch") || columnName.Contains("idle") || columnName.Contains("nghỉ")))
                    {
                        planColumnName = column.ColumnName;
                    }
                }

                if (dayColumnName != null && planColumnName != null)
                {
                    foreach (DataRow row in idlePlansData.Rows)
                    {
                        var dayValue = row[dayColumnName]?.ToString();
                        var planValue = row[planColumnName]?.ToString();

                        if (!string.IsNullOrEmpty(dayValue) && !string.IsNullOrEmpty(planValue))
                        {
                            // Parse day (format: DD/MM)
                            var dayParts = dayValue.Split('/');
                            if (dayParts.Length >= 2 && 
                                int.TryParse(dayParts[0], out int day) && 
                                int.TryParse(dayParts[1], out int month))
                            {
                                // Only include current month data
                                if (month == currentMonth)
                                {
                                    // Parse plan hours (format: "1h" or "1 hours" or just "1")
                                    var planText = planValue.ToLower().Replace("h", "").Replace("hours", "").Replace("giờ", "").Trim();
                                    if (double.TryParse(planText, out double planHours))
                                    {
                                        totalPlanIdleHours += planHours;
                                    }
                                }
                            }
                        }
                    }

                    Console.WriteLine($"IdlePlanService: Total monthly plan idle time: {totalPlanIdleHours:F1}h");
                    return totalPlanIdleHours;
                }

                Console.WriteLine("IdlePlanService: Could not find Day or Plan columns, using default 240h");
                return 240.0; // Default
            }
            catch (Exception ex)
            {
                Console.WriteLine($"IdlePlanService: Error getting monthly plan: {ex.Message}");
                return 240.0; // Default fallback
            }
        }

        /// <summary>
        /// Clear cached data to force reload
        /// </summary>
        public void ClearCache()
        {
            _cachedIdlePlansData = null;
            Console.WriteLine("IdlePlanService: Cache cleared");
        }
    }
}
