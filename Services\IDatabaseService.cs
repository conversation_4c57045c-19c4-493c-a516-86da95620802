﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Centralized database service interface - ALL database operations must go through this
    /// </summary>
    public interface IDatabaseService
    {
        // Database initialization
        Task InitializeDatabaseAsync();

        // User management
        Task<User> AuthenticateUserAsync(string username, string password);
        Task<User> AuthenticateUserByRfidAsync(string rfid);
        Task<List<User>> GetAllUsersAsync();
        Task<bool> SaveUserAsync(User user);
        Task<bool> DeleteUserAsync(int userId);

        // Production data
        Task<bool> SaveProductionDataAsync(ProductionData data);
        Task<List<ProductionData>> GetProductionDataAsync(DateTime? fromDate = null, DateTime? toDate = null, string workShift = null, string reportType = null);
        Task<ProductionData> GetLastShiftChangeDataAsync();
        Task<int> GetTodayPreviousShiftsActualAsync(string currentShiftName);

        // Production summary
        Task<ProductionSummaryData> GetDailyProductionSummaryAsync(DateTime date);
        Task<ProductionSummaryData> GetShiftProductionSummaryAsync(DateTime date, string shiftName);
        Task<TimeSpan> GetUsedIdleTimeAsync(DateTime startDate, DateTime endDate);

        // Idle time management
        Task SaveDailyIdleTimeSummaryAsync(DateTime date, TimeSpan idleTime);
        Task<TimeSpan> GetMonthlyIdleTimeFromDatabaseAsync();

        // Product records
        Task SaveProductRecordAsync(ProductRecord record);

        // Generic query execution
        Task<T> ExecuteScalarAsync<T>(string query, object parameters = null);
        Task<List<T>> ExecuteQueryAsync<T>(string query, object parameters = null) where T : new();
        Task<bool> ExecuteNonQueryAsync(string query, object parameters = null);
    }
}
