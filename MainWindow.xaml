﻿<Window x:Class="ZoomableApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
        xmlns:local="clr-namespace:ZoomableApp.Views"
        xmlns:controls="clr-namespace:ZoomableApp.SharedControls"
        xmlns:converters="clr-namespace:ZoomableApp.Converters"
        xmlns:ViewModels="clr-namespace:ZoomableApp.ViewModels"
        xmlns:views="clr-namespace:ZoomableApp.Views"
        mc:Ignorable="d"
        Title="Hệ thống Quản lý <PERSON>n xuất" Height="900" Width="1400"
        x:Name="mainWindow"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    <Window.Resources>
        <!-- Bell Shake Animation -->
        <Storyboard x:Key="BellShakeStoryboard" RepeatBehavior="Forever">
            <DoubleAnimationUsingKeyFrames Storyboard.TargetProperty="Angle">
                <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0"/>
                <!-- Pause -->
            </DoubleAnimationUsingKeyFrames>
        </Storyboard>
        <Storyboard x:Key="ResetBellAngleStoryboard">
            <DoubleAnimation Storyboard.TargetProperty="Angle" To="0" Duration="0:0:0.01"/>
        </Storyboard>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
        <converters:StringContainsFailedConverter x:Key="StringContainsFailedConverter"/>
        <converters:PlanItemStatusToColorConverter x:Key="StatusToColorConverter"/>
        <converters:PlanItemStatusToTextColorConverter x:Key="StatusToTextColorConverter"/>
        <converters:RowIndexConverter x:Key="RowIndexConverter"/>

        <!-- Button Style with Press Effect -->
        <Style x:Key="RoundButtonStyle" TargetType="Button">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="17"
                                BorderThickness="0"
                                RenderTransformOrigin="0.5,0.5">
                            <Border.RenderTransform>
                                <ScaleTransform x:Name="ButtonScale" ScaleX="1" ScaleY="1"/>
                            </Border.RenderTransform>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsPressed" Value="True">
                                <Trigger.EnterActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="0.95" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="0.95" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.EnterActions>
                                <Trigger.ExitActions>
                                    <BeginStoryboard>
                                        <Storyboard>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleX"
                                                           To="1.0" Duration="0:0:0.1"/>
                                            <DoubleAnimation Storyboard.TargetName="ButtonScale"
                                                           Storyboard.TargetProperty="ScaleY"
                                                           To="1.0" Duration="0:0:0.1"/>
                                        </Storyboard>
                                    </BeginStoryboard>
                                </Trigger.ExitActions>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Opacity" Value="0.8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="15">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#CC0000"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="SidebarColumn" Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Lớp phủ mờ -->
        <Rectangle Grid.Row="0" Grid.Column="0" Grid.RowSpan="2" Grid.ColumnSpan="2" Panel.ZIndex="-1">
            <Rectangle.Fill>
                <StaticResource ResourceKey="BackgroundBrush"/>
            </Rectangle.Fill>
            <Rectangle.Effect>
                <BlurEffect Radius="50"/>
            </Rectangle.Effect>
        </Rectangle>

        <!-- Sidebar Menu (Grid.Column="0") -->
        <Border Grid.Column="0" BorderBrush="{StaticResource BorderBrush}" BorderThickness="0,0,1,0" MinWidth="200">
            <Border.Effect>
                <DropShadowEffect Color="#40000000" Direction="0" ShadowDepth="6" BlurRadius="12" Opacity="0.2"/>
            </Border.Effect>
            <Grid x:Name="SidebarPanel" Visibility="Collapsed" Width="200">
                <!-- Close Button - Absolute positioned -->
                <Button HorizontalAlignment="Right" VerticalAlignment="Top"
                        Margin="0,10,10,0" Style="{StaticResource CloseButtonStyle}"
                        Click="CloseSidebarButton_Click" Cursor="Hand" ToolTip="Đóng menu"
                        Panel.ZIndex="10">
                    <Path Data="M18,6L6,18M6,6L18,18" Stroke="White" StrokeThickness="2"
                          Width="16" Height="16" Stretch="Uniform"/>
                </Button>

                <!-- User Info Section - Absolute positioned -->
                <Border HorizontalAlignment="Stretch" VerticalAlignment="Top" MaxWidth="250"
                        Background="{StaticResource SecondaryBrush}" Padding="10" Margin="0,50,0,0"
                        CornerRadius="0,0,8,8" MinHeight="80">
                    <Border.Effect>
                        <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.2"/>
                    </Border.Effect>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Avatar -->
                        <Border Grid.Column="0" Width="40" Height="40" CornerRadius="20" Background="{StaticResource AccentBrush}"
                                VerticalAlignment="Top" Margin="0,0,8,0">
                            <Path Data="M12,12c2.21,0 4,-1.79 4,-4s-1.79,-4 -4,-4 -4,1.79 -4,4 1.79,4 4,4zM12,14c-2.67,0 -8,1.34 -8,4v2h16v-2c0,-2.66 -5.33,-4 -8,-4z"
                                  Fill="White" Stretch="Uniform" Width="22" Height="22"/>
                        </Border>

                        <!-- User Details -->
                        <StackPanel Grid.Column="1" VerticalAlignment="Top">
                            <TextBlock x:Name="UserFullNameTextBlock" Text="Admin User"
                                       Foreground="White" FontWeight="Bold" FontSize="12"
                                       TextWrapping="Wrap" MaxWidth="200"/>
                            <TextBlock x:Name="UserShiftTextBlock" Text="Ca hành chính: 08:00-17:00"
                                       Foreground="#BDC3C7" FontSize="10" Margin="0,2,0,0"
                                       TextWrapping="Wrap" LineHeight="12" MaxWidth="200"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Menu Items - Top aligned with proper spacing -->
                <ListBox x:Name="SidebarMenuListBox" Background="Transparent" BorderThickness="0"
                         SelectionMode="Single" Foreground="White" FontSize="14"
                         SelectionChanged="SidebarMenuListBox_SelectionChanged"
                         VerticalAlignment="Top" HorizontalAlignment="Stretch"
                         Margin="0,140,0,60">
                    <ListBox.ItemContainerStyle>
                        <Style TargetType="ListBoxItem">
                            <Setter Property="Padding" Value="15,10"/>
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Foreground" Value="White"/>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#3498DB"/>
                                </Trigger>
                                <Trigger Property="IsSelected" Value="True">
                                    <Setter Property="Background" Value="#2980B9"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </ListBox.ItemContainerStyle>
                    <ListBoxItem Content="🏠 Trang chủ"/>
                    <ListBoxItem Content="📋 Kế hoạch"/>
                    <ListBoxItem Content="📊 Báo cáo"/>
                    <ListBoxItem Content="🔧 Bảo dưỡng"/>
                    <ListBoxItem Content="⚙️ Cài đặt"/>
                    <ListBoxItem Content="👥 Người dùng"/>
                </ListBox>

                <!-- Logout Button - Absolute positioned at bottom -->
                <Button x:Name="LogoutButton" Content="🚪 Đăng xuất"
                        Background="#E74C3C" Foreground="White"
                        Margin="15,0,15,15" FontSize="12" Height="Auto"
                        Click="LogoutButton_Click"
                        HorizontalAlignment="Stretch" VerticalAlignment="Bottom"
                        Style="{StaticResource Modern3DButtonStyle}"/>
            </Grid>
        </Border>
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <!-- Header cố định -->
                <RowDefinition Height="*"/>
                <!-- Vùng nội dung chính -->
            </Grid.RowDefinitions>

            <!-- Header cố định -->
            <Grid Grid.Row="0" Height="60">
                <Grid.Effect>
                    <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                </Grid.Effect>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Nút mở sidebar -->
                <Button x:Name="OpenSidebarButton" Grid.Column="0"
                        Width="50" Height="50"
                        Background="Transparent"
                        BorderThickness="0"
                        Foreground="{StaticResource SurfaceBrush}"
                        VerticalAlignment="Center"
                        Margin="15,0,0,0"
                        Click="OpenSidebarButton_Click"
                        Cursor="Hand"
                        Visibility="{Binding IsSidebarOpen, ElementName=mainWindow, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                    <Path Data="M0,5 L25,5 M0,12.5 L25,12.5 M0,20 L25,20"
                          Width="38"
                          Stroke="{StaticResource SurfaceBrush}"
                          StrokeThickness="3"
                          Stretch="Uniform"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Center" />
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="{StaticResource LightBrush}"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Tên trang ở giữa -->
                <TextBlock x:Name="PageTitleTextBlock" Grid.Column="1" Text="🏠 Trang chủ"
                           Style="{StaticResource HeadingMediumStyle}"
                           HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <!-- Bell notification ở bên phải -->
                <Grid Grid.Column="2" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="0,0,10,0">
                    <Button x:Name="ErrorBellButton" Width="40" Height="40" Background="Transparent"
                BorderThickness="0" Click="ErrorBellButton_Click" ToolTip="View Errors">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Grid>
                                    <Path x:Name="BellPath"
                              Data="M12,22c1.1,0 2,-0.9 2,-2h-4c0,1.1 0.9,2 2,2z M18,16v-5c0,-3.07 -1.63,-5.64 -4.5,-6.32L13.5,4c0,-0.83 -0.67,-1.5 -1.5,-1.5s-1.5,0.67 -1.5,1.5v0.68C7.63,5.36 6,7.92 6,11v5l-2,2v1h16v-1l-2,-2z M16,17H8v-6c0,-2.48 1.51,-4.5 4,-4.5s4,2.02 4,4.5V17z"
                              Fill="DimGray" Stretch="Uniform" RenderTransformOrigin="0.5,0.1">
                                        <Path.RenderTransform>
                                            <RotateTransform x:Name="BellButtonTransform" Angle="0"/>
                                        </Path.RenderTransform>
                                    </Path>
                                </Grid>

                                <ControlTemplate.Triggers>
                                    <DataTrigger Binding="{Binding HasNewErrors, ElementName=mainWindow}" Value="True">
                                        <Setter TargetName="BellPath" Property="Fill" Value="Red"/>
                                        <DataTrigger.EnterActions>
                                            <BeginStoryboard x:Name="BellShakeBeginStoryboard">
                                                <!-- Use an inline Storyboard -->
                                                <BeginStoryboard.Storyboard>
                                                    <Storyboard RepeatBehavior="Forever">
                                                        <DoubleAnimationUsingKeyFrames Storyboard.TargetName="BellPath"
                                                                           Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)">
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.0" Value="0"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.1" Value="-15"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.2" Value="15"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.3" Value="-10"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.4" Value="10"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.5" Value="-5"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.6" Value="5"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:0.7" Value="0"/>
                                                            <EasingDoubleKeyFrame KeyTime="0:0:1.5" Value="0"/>
                                                            <!-- Pause -->
                                                        </DoubleAnimationUsingKeyFrames>
                                                    </Storyboard>
                                                </BeginStoryboard.Storyboard>
                                            </BeginStoryboard>
                                        </DataTrigger.EnterActions>
                                        <DataTrigger.ExitActions>
                                            <StopStoryboard BeginStoryboardName="BellShakeBeginStoryboard"/>
                                            <BeginStoryboard x:Name="BellResetAngleBeginStoryboard">
                                                <!-- Use an inline Storyboard -->
                                                <BeginStoryboard.Storyboard>
                                                    <Storyboard>
                                                        <DoubleAnimation Storyboard.TargetName="BellPath"
                                                             Storyboard.TargetProperty="(UIElement.RenderTransform).(RotateTransform.Angle)"
                                                             To="0" Duration="0:0:0.01"/>
                                                    </Storyboard>
                                                </BeginStoryboard.Storyboard>
                                            </BeginStoryboard>
                                        </DataTrigger.ExitActions>
                                    </DataTrigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                    </Button>

                    <!-- Badge hiển thị số lượng lỗi -->
                    <Border x:Name="ErrorCountBadge" Background="Red" CornerRadius="8"
                Height="16" MinWidth="16" Padding="3,0"
                HorizontalAlignment="Right" VerticalAlignment="Top" Margin="0,-5,-5,0"
                Visibility="{Binding HasNewErrors, ElementName=mainWindow, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="{Binding NewErrorCount, ElementName=mainWindow}" Foreground="White"
                       FontSize="10" FontWeight="Bold"
                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </Grid>
            </Grid>

            <!-- Hàng dưới: Vùng nội dung chính -->
            <Grid Grid.Row="1" x:Name="MainContentGrid">
                <!-- Toast Notification Area (Bottom Right of Main Content) -->
                <Grid x:Name="ToastNotificationContainer" Panel.ZIndex="1000"
                      VerticalAlignment="Bottom" HorizontalAlignment="Right"
                      Margin="0,0,20,20"/>
                <!-- Dynamic Content Area -->
                <ContentControl x:Name="MainContentArea" Visibility="Visible">
                    <!-- Content will be loaded dynamically -->
                </ContentControl>




                <!-- Trang Kế hoạch -->
                <ContentControl x:Name="PlanPage" Visibility="Collapsed">
                    <views:PlanPage />
                </ContentControl>

                <!-- Trang Báo cáo -->
                <ContentControl x:Name="ReportPage" Visibility="Collapsed">
                    <views:ReportPage />
                </ContentControl>

                <!-- Trang Bảo dưỡng -->
                <ContentControl x:Name="MaintenancePage" Visibility="Collapsed">
                    <views:MaintenancePage />
                </ContentControl>

                <!-- Trang Cài đặt -->
                <ContentControl x:Name="SettingsPage" Visibility="Collapsed">
                    <views:SettingsPage />
                </ContentControl>

                <!-- Trang Người dùng -->
                <ContentControl x:Name="UsersPage" Visibility="Collapsed">
                    <views:UsersPage />
                </ContentControl>
            </Grid>

            <!-- Error List Popup -->
            <Popup x:Name="ErrorListPopup" Placement="Bottom" PlacementTarget="{Binding ElementName=ErrorBellButton}"
               StaysOpen="False" AllowsTransparency="True" PopupAnimation="Slide"
               IsOpen="{Binding IsErrorListPopupOpen, ElementName=mainWindow, Mode=TwoWay}">
                <Border Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="1" CornerRadius="3"
                    Padding="10" MaxHeight="300" MinWidth="350" MaxWidth="500">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="Error Notifications" FontWeight="Bold" FontSize="14" Margin="0,0,0,10"/>
                        <ListBox Grid.Row="1" x:Name="ErrorListBox" ItemsSource="{Binding AllErrors, ElementName=mainWindow}"
                             ScrollViewer.VerticalScrollBarVisibility="Auto">
                            <ListBox.ItemContainerStyle>
                                <Style TargetType="ListBoxItem">
                                    <Setter Property="Padding" Value="3"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsNew}" Value="True">
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ListBox.ItemContainerStyle>
                        </ListBox>
                        <Button Grid.Row="2" Content="Clear All Errors (Display Only)" Click="ClearAllErrorsButton_Click" Margin="0,10,0,0" HorizontalAlignment="Right" Padding="5,2"/>
                    </Grid>
                </Border>
            </Popup>
        </Grid>
    </Grid>
</Window>
