﻿using System;
using System.IO;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;
// Thêm using cho HSLCommunication
using HslCommunication;
using HslCommunication.Profinet.Melsec; // <PERSON><PERSON> thể cho Mitsubishi MC Protocol
// Dành cho Mx Component
using ActUtlTypeLib;
using System.Runtime.InteropServices;
using System.Text.Json;

namespace ZoomableApp.Services
{
    public class MitsubishiPlcService : IPlcService, IDisposable
    {
        private readonly Dictionary<PlcDeviceAddress, PlcRegisterInfo> _registerMap;
        private readonly string _plcIdForMap; // Lưu lại plcId để load map
        private readonly ILoggerService? _logger;
        //private ActUtlType _actUtlType;

        private bool _isConnectedInternal = false;
        //public bool IsConnected => _actUtlType != null && _isConnectedInternal;

        // Phần bên dưới dành cho HSLCommunication
        private MelsecMcNet _melsecNet;
        private bool _hasSuccessfullyConnected = false;
        public bool IsConnected => _melsecNet != null && _hasSuccessfullyConnected;
        public string PlcIpAddress { get; set; } // Placeholder
        public int PlcPort { get; set; } // Cổng MC Protocol (QnA 3E Frame Binary)
        internal PlcConnectionManager OwnerManager { get; set; } // Tham chiếu đến manager sở hữu nó
        internal string ServicePlcId { get; set; }

        //private MelsecMcNet _melsecNet; // Đối tượng client của HSLCommunication

        public MitsubishiPlcService() : this("UNKNOWN_PLC_ID_DEFAULT") // Gọi constructor chính với ID mặc định nếu cần
        {
            Console.WriteLine("Warning: MitsubishiPlcService created without a specific PLC ID. Register map might be empty or default.");
        }

        public MitsubishiPlcService(string plcId)
        {
            _plcIdForMap = plcId;

            // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
            }

            _registerMap = LoadAndInitializeRegisterMapForPlc(_plcIdForMap);
        }

        private Dictionary<PlcDeviceAddress, PlcRegisterInfo> LoadAndInitializeRegisterMapForPlc(string plcId)
        {
            var map = new Dictionary<PlcDeviceAddress, PlcRegisterInfo>();
            const string mapFileName = "plc_map.json";
            string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, mapFileName);

            if (!File.Exists(filePath))
            {
                Console.WriteLine($"PLCService ({plcId}): Register map file '{filePath}' not found. _registerMap will be empty.");
                return map; // Trả về map rỗng
            }

            try
            {
                string jsonContent = File.ReadAllText(filePath);
                // Deserialize toàn bộ file thành Dictionary<string, List<PlcMapEntry>>
                var allPlcMaps = JsonSerializer.Deserialize<Dictionary<string, List<PlcMapEntry>>>(jsonContent,
                    new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                if (allPlcMaps != null && allPlcMaps.TryGetValue(plcId, out var plcSpecificEntries))
                {
                    foreach (var entry in plcSpecificEntries)
                    {
                        if (Enum.TryParse<PlcDeviceAddress>(entry.LogicalName, true, out var logicalAddr) && // true for ignore case
                            Enum.TryParse<PlcDataType>(entry.DataType, true, out var dataType))
                        {
                            map[logicalAddr] = new PlcRegisterInfo(logicalAddr, entry.PhysicalAddress, dataType, entry.Length == 0 ? 1 : entry.Length);
                        }
                        else
                        {
                            Console.WriteLine($"PLCService ({plcId}): Error parsing map entry from file: LogicalName='{entry.LogicalName}', DataType='{entry.DataType}'");
                        }
                    }
                    Console.WriteLine($"PLCService ({plcId}): Loaded {map.Count} register mappings from '{filePath}'.");
                }
                else
                {
                    Console.WriteLine($"PLCService ({plcId}): No specific register map found for PLC ID '{plcId}' in '{filePath}'. _registerMap will be empty for this ID.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"PLCService ({plcId}): Error loading or parsing register map file '{filePath}'. Error: {ex.Message}");
                // Trả về map rỗng nếu có lỗi
            }
            return map;
        }


        public Task<ConnectionResult> ConnectAsync()
        {
            return Task.Run(() =>
            {
                try
                {
                    //// Kết nối với PLC bằng Mx Component
                    //if (_actUtlType != null)
                    //{
                    //    Marshal.ReleaseComObject(_actUtlType);
                    //    _actUtlType = null;
                    //    _isConnectedInternal = false;
                    //}

                    //_actUtlType = new ActUtlType(); // Khởi tạo đối tượng COM
                    //_actUtlType.ActLogicalStationNumber = this.LogicalStationNumber;

                    //int iRet = _actUtlType.Open(); // Mở kết nối
                    //_isConnectedInternal = (iRet == 0); // 0 thường là thành công

                    //if (_isConnectedInternal)
                    //{
                    //    Console.WriteLine($"PLCService MX: Connected via LogicalStationNumber: {this.LogicalStationNumber}. (PLC Info: {PlcIpAddress}:{PlcPort} for reference)");
                    //    return new ConnectionResult(true, "Connected via MX Component.");
                    //}
                    //else
                    //{
                    //    // Lấy thông báo lỗi từ MX Component nếu có
                    //    // string mxError = GetMxErrorMessage(iRet); // Cần hàm helper
                    //    Console.WriteLine($"PLCService MX: Failed to connect via LogicalStationNumber: {this.LogicalStationNumber}. ReturnCode: {iRet}. (PLC Info: {PlcIpAddress}:{PlcPort})");
                    //    Marshal.ReleaseComObject(_actUtlType); // Giải phóng nếu Open thất bại
                    //    _actUtlType = null;
                    //    return new ConnectionResult(false, $"MX Component Open failed. Return Code: {iRet}");
                    //}

                    // Kết nối với PLC bằng HSLCommunication

                    _melsecNet = new MelsecMcNet(PlcIpAddress, PlcPort);
                    OperateResult connectResult = _melsecNet.ConnectServer();
                    _hasSuccessfullyConnected = connectResult.IsSuccess;

                    if (_hasSuccessfullyConnected)
                    {
                        Console.WriteLine($"PLCService HSL: Connected to {PlcIpAddress}:{PlcPort}.");
                        return new ConnectionResult(true, "Connected");
                    }
                    else
                    {
                        Console.WriteLine($"PLCService HSL: Failed to connect. Error: {connectResult.Message}");
                        _melsecNet = null;
                        return new ConnectionResult(false, connectResult.Message ?? "Unknown connection error");
                    }
                }
                catch (Exception ex)
                {
                    // Dành cho HSLCommunication

                    Console.WriteLine($"PLCService HSL: Exception during connect: {ex.Message}");
                    _melsecNet = null;
                    _hasSuccessfullyConnected = false;
                    return new ConnectionResult(false, $"Exception: {ex.Message}");

                    // Dành cho Mx Component
                    //Console.WriteLine($"PLCService MX: Exception during connect: {ex.Message}");
                    //if (_actUtlType != null) Marshal.ReleaseComObject(_actUtlType);
                    //_actUtlType = null;
                    //_isConnectedInternal = false;
                    //return new ConnectionResult(false, $"Exception: {ex.Message}");
                }
            });
        }

        public Task DisconnectAsync()
        {
            return Task.Run(() =>
            {
                // HSL Communication
                if (_melsecNet != null) // Không cần kiểm tra _hasSuccessfullyConnected ở đây nữa
                {
                    _melsecNet.ConnectClose(); // Không cần kiểm tra OperateResult ở đây vì ta đang ngắt kết nối
                    Console.WriteLine("PLCService HSL: Disconnected command sent.");
                }
                _melsecNet = null; // Luôn giải phóng
                _hasSuccessfullyConnected = false; // Reset cờ

                // Mx Component
                //if (_actUtlType != null)
                //{
                //    int iRetClose = _actUtlType.Close(); // Kiểm tra xem có hàm này không
                //    if (iRetClose == 0) Console.WriteLine("PLCService MX: Close() successful.");
                //    else Console.WriteLine($"PLCService MX: Close() failed with code {iRetClose}.");

                //    Marshal.ReleaseComObject(_actUtlType);
                //    _actUtlType = null;
                //    _isConnectedInternal = false;
                //    Console.WriteLine("PLCService MX: Disconnected (COM Object Released).");
                //}
            });
        }

        public PlcRegisterInfo GetRegisterInfo(PlcDeviceAddress deviceAddress)
        {
            if (_registerMap.TryGetValue(deviceAddress, out var info))
            {
                return info;
            }
            throw new ArgumentException($"No register info found for logical address: {deviceAddress}");
        }

        public Task<PlcReadResult> ReadAsync(PlcDeviceAddress deviceAddress) // Sửa tên lại (nếu trước đó là ReadGenericAsync)
        {
            if (!IsConnected)
            {
                return Task.FromResult(PlcReadResult.Failure("Not connected"));
            }
            var regInfo = GetRegisterInfo(deviceAddress);

            return Task.Run(() => // Sử dụng Task.Run trực tiếp cho kiểu trả về Task<PlcReadResult>
            {
                try
                {
                    object rawValue = null;
                    OperateResult hslResult = null; // Để lấy message chung
                    //int iRet = 0; // Để lấy mã lỗi từ Mx Component
                    //int plcOutValue;

                    switch (regInfo.DataType)
                    {
                        case PlcDataType.BIT:
                            //iRet = _actUtlType.GetDevice(regInfo.PhysicalAddress, out plcOutValue);
                            //if (iRet == 0) rawValue = (plcOutValue != 0); // Chuyển int (0 hoặc 1) thành bool
                            //break;
                            var rBool = _melsecNet.ReadBool(regInfo.PhysicalAddress, 1);
                            hslResult = rBool; // Gán kết quả chung
                            if (rBool.IsSuccess) rawValue = rBool.Content[0];
                            break;
                        case PlcDataType.WORD:
                            //iRet = _actUtlType.GetDevice(regInfo.PhysicalAddress, out plcOutValue);
                            //if (iRet == 0) rawValue = (short)plcOutValue; // Ép kiểu int sang short
                            var rShort = _melsecNet.ReadInt16(regInfo.PhysicalAddress);
                            hslResult = rShort;
                            if (rShort.IsSuccess) rawValue = rShort.Content;
                            break;

                        // Không biết cách giải quyết cho DWord trong Mx Component
                        //case PlcDataType.DWORD:
                        //    short[] dwordDataAsShorts = new short[2]; // DWORD là 32-bit = 2 words
                        //                                              // Đọc 2 word liên tiếp bắt đầu từ regInfo.PhysicalAddress
                        //    iRet = _actUtlType.ReadDeviceBlock(regInfo.PhysicalAddress, 2, ref dwordDataAsShorts[0]);
                        //    if (iRet == 0)
                        //    {

                        //        rawValue = (int)((ushort)dwordDataAsShorts[0] | (dwordDataAsShorts[1] << 16));

                        //        //var rInt = _melsecNet.ReadInt32(regInfo.PhysicalAddress);
                        //        //hslResult = rInt;
                        //        //if (rInt.IsSuccess) rawValue = rInt.Content;
                        //    }
                        //    break;

                        case PlcDataType.FLOAT:
                            //short[] floatDataAsShorts = new short[2]; // Float là 32-bit = 2 words
                            //iRet = _actUtlType.ReadDeviceBlock(regInfo.PhysicalAddress, 2, ref floatDataAsShorts[0]);
                            //if (iRet == 0)
                            //{
                            //    byte[] bytes = new byte[4];
                            //    Buffer.BlockCopy(floatDataAsShorts, 0, bytes, 0, 4);
                            //    rawValue = BitConverter.ToSingle(bytes, 0);
                            //}


                            var rFloat = _melsecNet.ReadFloat(regInfo.PhysicalAddress);
                            hslResult = rFloat;
                            if (rFloat.IsSuccess) rawValue = rFloat.Content;
                            break;
                        case PlcDataType.STRING:

                            //short[] stringDataAsShorts = new short[regInfo.Length];
                            //iRet = _actUtlType.ReadDeviceBlock(regInfo.PhysicalAddress, regInfo.Length, ref stringDataAsShorts[0]);
                            //if (iRet == 0)
                            //{
                            //    byte[] stringBytes = new byte[regInfo.Length * 2];
                            //    Buffer.BlockCopy(stringDataAsShorts, 0, stringBytes, 0, stringBytes.Length);
                            //    rawValue = Encoding.ASCII.GetString(stringBytes).TrimEnd('\0', ' ');
                            //}

                            ushort byteLength = (ushort)(regInfo.Length * 2);
                            var rString = _melsecNet.ReadString(regInfo.PhysicalAddress, byteLength, Encoding.ASCII);
                            hslResult = rString;
                            if (rString.IsSuccess) rawValue = rString.Content.TrimEnd('\0');
                            break;
                        default:
                            return PlcReadResult.Failure($"Unsupported data type for read: {regInfo.DataType}");
                    }

                    //if (iRet == 0) // Thành công
                    //{
                    //    Console.WriteLine($"PLCService MX: Read {regInfo.LogicalName} ({regInfo.PhysicalAddress}) = {rawValue}");
                    //    return PlcReadResult.Success(rawValue, "Read successful");
                    //}
                    //else
                    //{
                    //    Console.WriteLine($"PLCService MX: Read Error ({regInfo.DataType} {regInfo.PhysicalAddress}): {iRet}");
                    //    return PlcReadResult.Failure($"MX Component Read Error: {iRet}");
                    //}

                    if (hslResult == null) // Không nên xảy ra nếu switch case đầy đủ
                    {
                        return PlcReadResult.Failure($"Internal error: HSL result was null for {regInfo.DataType}");
                    }

                    if (hslResult.IsSuccess)
                    {
                        Console.WriteLine($"PLCService HSL: Read {regInfo.LogicalName} ({regInfo.PhysicalAddress}) = {rawValue}");
                        return PlcReadResult.Success(rawValue, hslResult.Message);
                    }
                    else
                    {
                        string errorMsg = hslResult.Message ?? "Unknown read error";
                        Console.WriteLine($"HSL Read Error ({regInfo.DataType} {regInfo.PhysicalAddress}): {errorMsg}");
                        OwnerManager?.ReportPlcOperationError(ServicePlcId, $"Read {regInfo.LogicalName}", errorMsg); // BÁO LỖI
                        return PlcReadResult.Failure(errorMsg);
                    }
                }
                catch (Exception ex)
                {
                    //Console.WriteLine($"PLCService MX: Exception reading {regInfo.LogicalName} ({regInfo.PhysicalAddress}): {ex.Message}");
                    string errorMsg = $"Exception: {ex.Message}";
                    Console.WriteLine($"PLCService HSL: Exception reading {regInfo.LogicalName} ({regInfo.PhysicalAddress}): {errorMsg}");
                    OwnerManager?.ReportPlcOperationError(ServicePlcId, $"Read {regInfo.LogicalName}", errorMsg); // BÁO LỖI
                    return PlcReadResult.Failure(errorMsg);
                }
            });
        }

        public async Task<Dictionary<PlcDeviceAddress, object>> ReadMultipleAsync(IEnumerable<PlcDeviceAddress> deviceAddresses)
        {
            var results = new Dictionary<PlcDeviceAddress, object>();
            if (!IsConnected) return results;

            foreach (var addr in deviceAddresses)
            {
                PlcReadResult readOpResult = await ReadAsync(addr).ConfigureAwait(false);
                if (readOpResult.IsSuccess)
                {
                    results[addr] = readOpResult.Value;
                }
                else
                {
                    // Log lỗi hoặc xử lý theo cách khác (ví dụ, không thêm vào dictionary)
                    Console.WriteLine($"ReadMultipleAsync: Failed to read {addr}. Error: {readOpResult.Message}");
                    // results[addr] = null; // Hoặc một giá trị mặc định
                }
            }
            return results;
        }


        public Task<PlcWriteResult> WriteAsync(PlcDeviceAddress deviceAddress, object value)
        {
            if (!IsConnected)
            {
                return Task.FromResult(PlcWriteResult.Failure("Not connected"));
            }
            var regInfo = GetRegisterInfo(deviceAddress);

            return Task.Run(() => // Sử dụng Task.Run trực tiếp
            {
                try
                {
                    object plcValue = PlcDataMapper.MapToPlcType(value, regInfo);
                    if (plcValue == null && value != null)
                    {
                        return PlcWriteResult.Failure($"Failed to map value for {regInfo.LogicalName}. Original: {value}");
                    }

                    OperateResult writeResult = null;
                    //int iRet = 0;
                    switch (regInfo.DataType)
                    {
                        case PlcDataType.BIT:
                            if (plcValue is bool bVal) writeResult = _melsecNet.Write(regInfo.PhysicalAddress, bVal);
                            else return PlcWriteResult.Failure($"Invalid type for BIT ({regInfo.PhysicalAddress}). Expected bool, got {plcValue?.GetType()}.");
                            //if (plcValue is bool bVal) iRet = _actUtlType.SetDevice(regInfo.PhysicalAddress, (short)(bVal ? 1 : 0));
                            //else return PlcWriteResult.Failure($"Invalid type for BIT. Expected bool, got {plcValue?.GetType()}.");
                            break;
                        case PlcDataType.WORD:
                            //if (plcValue is short sVal) iRet = _actUtlType.SetDevice(regInfo.PhysicalAddress, sVal);
                            //else return PlcWriteResult.Failure($"Invalid type for WORD. Expected short, got {plcValue?.GetType()}.");
                            if (plcValue is short sVal) writeResult = _melsecNet.Write(regInfo.PhysicalAddress, sVal);
                            else return PlcWriteResult.Failure($"Invalid type for WORD ({regInfo.PhysicalAddress}). Expected short, got {plcValue?.GetType()}.");
                            break;
                        case PlcDataType.DWORD:
                            //if (plcValue is int iVal) iRet = _actUtlType.SetDevice2(regInfo.PhysicalAddress, iVal);
                            //else return PlcWriteResult.Failure($"Invalid type for DWORD. Expected int, got {plcValue?.GetType()}.");

                            //HslCommunication 
                            if (plcValue is int iVal) writeResult = _melsecNet.Write(regInfo.PhysicalAddress, iVal);
                            else return PlcWriteResult.Failure($"Invalid type for DWORD ({regInfo.PhysicalAddress}). Expected int, got {plcValue?.GetType()}.");
                            break;
                        case PlcDataType.FLOAT:
                            //if (plcValue is float fVal)
                            //{
                            //    byte[] bytes = BitConverter.GetBytes(fVal);
                            //    short[] floatData = new short[2];
                            //    floatData[0] = BitConverter.ToInt16(bytes, 0);
                            //    floatData[1] = BitConverter.ToInt16(bytes, 2);
                            //    iRet = _actUtlType.WriteDeviceBlock(regInfo.PhysicalAddress, 2, ref floatData[0]);
                            //}
                            //else return PlcWriteResult.Failure($"Invalid type for FLOAT. Expected float, got {plcValue?.GetType()}.");

                            if (plcValue is float fVal) writeResult = _melsecNet.Write(regInfo.PhysicalAddress, fVal);
                            else return PlcWriteResult.Failure($"Invalid type for FLOAT ({regInfo.PhysicalAddress}). Expected float, got {plcValue?.GetType()}.");
                            break;
                        case PlcDataType.STRING:
                            //if (plcValue is short[] saVal)
                            //{
                            //    iRet = _actUtlType.WriteDeviceBlock(regInfo.PhysicalAddress, regInfo.Length, ref saVal[0]);
                            //}
                            //// Hoặc nếu bạn muốn truyền string trực tiếp và để hàm này xử lý encoding
                            //else if (value is string strVal)
                            //{
                            //    byte[] stringBytes = Encoding.ASCII.GetBytes(strVal); // Hoặc Shift_JIS
                            //    int wordsToWrite = (stringBytes.Length + 1) / 2; // Số WORDs cần thiết
                            //    if (wordsToWrite > regInfo.Length) wordsToWrite = regInfo.Length; // Giới hạn bởi Length đã map

                            //    short[] dataToWrite = new short[wordsToWrite];
                            //    for (int i = 0; i < wordsToWrite; i++)
                            //    {
                            //        if (i * 2 + 1 < stringBytes.Length)
                            //            dataToWrite[i] = BitConverter.ToInt16(stringBytes, i * 2);
                            //        else if (i * 2 < stringBytes.Length)
                            //            dataToWrite[i] = (short)(stringBytes[i * 2] | (0 << 8)); // Byte cuối, byte cao là 0
                            //        else dataToWrite[i] = 0; // Nên là 0 nếu không có data
                            //    }
                            //    if (dataToWrite.Length > 0)
                            //    {
                            //        iRet = _actUtlType.WriteDeviceBlock(regInfo.PhysicalAddress, dataToWrite.Length, ref dataToWrite[0]);
                            //    }
                            //    else
                            //    { // Chuỗi rỗng
                            //        // Có thể cần ghi 1 word giá trị 0 nếu PLC yêu cầu
                            //        short emptyChar = 0;
                            //        iRet = _actUtlType.WriteDeviceBlock(regInfo.PhysicalAddress, 1, ref emptyChar);
                            //    }
                            //}
                            //else return PlcWriteResult.Failure($"Invalid type for STRING. Expected string or short[], got {plcValue?.GetType()}.");

                            // HSL 
                            if (value is string strVal) // Truyền string gốc, HSL sẽ xử lý
                            {
                                int maxByteLength = regInfo.Length * 2;
                                byte[] bytes = Encoding.ASCII.GetBytes(strVal); // Hoặc encoding phù hợp
                                if (bytes.Length > maxByteLength) Array.Resize(ref bytes, maxByteLength);
                                string finalStrVal = Encoding.ASCII.GetString(bytes).TrimEnd('\0');
                                writeResult = _melsecNet.Write(regInfo.PhysicalAddress, finalStrVal, Encoding.ASCII);
                            }
                            else if (plcValue is short[] saVal) // Nếu mapper trả về mảng short
                            {
                                writeResult = _melsecNet.Write(regInfo.PhysicalAddress, saVal);
                            }
                            else return PlcWriteResult.Failure($"Invalid type for STRING ({regInfo.PhysicalAddress}). Expected string or short[], got {plcValue?.GetType()}.");
                            break;
                        default:
                            return PlcWriteResult.Failure($"Unsupported data type for write: {regInfo.DataType}");
                    }

                    //if(iRet == 0)
                    //{
                    //    Console.WriteLine($"PLCService MX: Write {regInfo.LogicalName} ({regInfo.PhysicalAddress}) = {value}, Success.");
                    //    return PlcWriteResult.Success();
                    //}
                    //else
                    //{
                    //    Console.WriteLine($"MX Write Error ({regInfo.DataType} {regInfo.PhysicalAddress}). Return Code: {iRet}");
                    //    return PlcWriteResult.Failure($"MX Write Error. Code: {iRet}");
                    //}

                    // HSL
                    if (writeResult != null && writeResult.IsSuccess)
                    {
                        _logger?.LogDebug($"PLCService HSL: Write {regInfo.LogicalName} ({regInfo.PhysicalAddress}) = {value} (PLC Value: {plcValue}), Success.");
                        return PlcWriteResult.Success(writeResult.Message);
                    }
                    else
                    {
                        OwnerManager?.ReportPlcOperationError(ServicePlcId, $"Read {regInfo.LogicalName}", writeResult?.Message ?? "Unknown write error"); // BÁO LỖI
                        _logger?.LogError($"PLCService HSL: Write {regInfo.LogicalName} ({regInfo.PhysicalAddress}) = {value}, Failed. Error: {writeResult?.Message}");
                        return PlcWriteResult.Failure(writeResult?.Message ?? "Unknown write error");
                    }
                }
                catch (Exception ex)
                {
                    OwnerManager?.ReportPlcOperationError(ServicePlcId, $"Read {regInfo.LogicalName}", ex.Message); // BÁO LỖI
                    _logger?.LogError($"PLCService HSL: Exception writing {regInfo.LogicalName} ({regInfo.PhysicalAddress}): {ex.Message}", ex);
                    return PlcWriteResult.Failure($"Exception: {ex.Message}");
                }
            });
        }

        public async Task<bool> WriteMultipleAsync(Dictionary<PlcDeviceAddress, object> valuesToWrite)
        {
            if (!IsConnected) return false;
            bool allSuccess = true;
            foreach (var kvp in valuesToWrite)
            {
                PlcWriteResult writeOpResult = await WriteAsync(kvp.Key, kvp.Value).ConfigureAwait(false);
                if (!writeOpResult.IsSuccess)
                {
                    allSuccess = false;
                    Console.WriteLine($"WriteMultipleAsync: Failed to write to {kvp.Key}. Error: {writeOpResult.Message}");
                    // Có thể dừng lại hoặc tiếp tục ghi các giá trị khác tùy theo yêu cầu
                    // break; // Nếu muốn dừng ngay khi có lỗi
                }
            }
            return allSuccess;
        }

        private class PlcMapEntry
        {
            public string LogicalName { get; set; }
            public string PhysicalAddress { get; set; }
            public string DataType { get; set; } // Đọc DataType là string từ JSON
            public int Length { get; set; }
        }
        public void Dispose() // Implement IDisposable để giải phóng HSL client
        {
            if (_melsecNet != null)
            {
                if (IsConnected) // Sử dụng IsConnected của HSL nếu có, hoặc dựa vào cờ của bạn
                {
                    _melsecNet.ConnectClose();
                }
                _melsecNet.Dispose();
                _melsecNet = null;
            }
             _hasSuccessfullyConnected = false;
        }
    }
}