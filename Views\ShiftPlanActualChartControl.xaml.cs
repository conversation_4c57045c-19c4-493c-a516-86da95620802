using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System;
using System.Windows;
using System.Windows.Controls;
using ZoomableApp.ViewModels;
using ZoomableApp.PLC;
using ZoomableApp.Services;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for ShiftPlanActualChartControl.xaml
    /// </summary>
    public partial class ShiftPlanActualChartControl : UserControl
    {
        private ShiftPlanActualChartViewModel _viewModel;

        public ShiftPlanActualChartControl()
        {
            InitializeComponent();
            ShiftPlanChart.LegendTextPaint = new SolidColorPaint(SKColors.White);

            // Delay initialization until the control is loaded and has proper parent context
            this.Loaded += ShiftPlanActualChartControl_Loaded;
        }

        private void ShiftPlanActualChartControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            try
            {
                // Delay initialization to ensure parent DataContext is set first
                Dispatcher.BeginInvoke(new System.Action(() =>
                {
                    InitializeViewModel();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ShiftPlanActualChartControl] Error in Loaded event: {ex.Message}");
            }
        }

        private void InitializeViewModel()
        {
            try
            {
                PlanViewModel planViewModel = null;
                PlcConnectionManager plcManager = null;

                // Try to get dependencies from MainWindow first (most reliable)
                var mainWindow = System.Windows.Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    planViewModel = mainWindow.GetPlanViewModel();
                    plcManager = mainWindow.GetPlcManager();
                    Console.WriteLine("[ShiftPlanActualChartControl] Got dependencies from MainWindow");
                }

                // Fallback: Try to get from parent ContentView's ViewModel
                if (planViewModel == null || plcManager == null)
                {
                    var parentContentView = FindParentContentView();
                    if (parentContentView != null)
                    {
                        // Get from ContentView's ViewModel (MainlineViewModel or InspectionViewModel)
                        if (parentContentView.DataContext is MainlineViewModel mainlineVM)
                        {
                            planViewModel = planViewModel ?? mainlineVM.GetPlanViewModel();
                            plcManager = plcManager ?? mainlineVM.GetPlcManager();
                            Console.WriteLine("[ShiftPlanActualChartControl] Got dependencies from MainlineViewModel (fallback)");
                        }
                        else if (parentContentView.DataContext is InspectionViewModel inspectionVM)
                        {
                            planViewModel = planViewModel ?? inspectionVM.GetPlanViewModel();
                            plcManager = plcManager ?? inspectionVM.GetPlcManager();
                            Console.WriteLine("[ShiftPlanActualChartControl] Got dependencies from InspectionViewModel (fallback)");
                        }
                    }
                }

                _viewModel = new ShiftPlanActualChartViewModel(planViewModel, plcManager);

                // Force set DataContext to our ViewModel (override any inherited DataContext)
                this.DataContext = _viewModel;

                Console.WriteLine($"[ShiftPlanActualChartControl] ViewModel initialized - DataContext set to: {this.DataContext?.GetType().Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ShiftPlanActualChartControl] Error initializing ViewModel: {ex.Message}");
            }
        }

        private FrameworkElement FindParentContentView()
        {
            var parent = this.Parent;
            while (parent != null)
            {
                if (parent is Views.MainlineContentView || parent is Views.InspectionContentView)
                {
                    return parent as FrameworkElement;
                }

                if (parent is FrameworkElement fe)
                {
                    parent = fe.Parent;
                }
                else
                {
                    break;
                }
            }
            return null;
        }
    }
}
