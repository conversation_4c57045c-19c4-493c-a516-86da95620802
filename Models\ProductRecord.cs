﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public class ProductRecord : INotifyPropertyChanged
    {
        private int? _sequenceNumber; // S<PERSON> thứ tự từ PLC (ví dụ PLC cuối)
        public int? SequenceNumber
        {
            get => _sequenceNumber;
            set { _sequenceNumber = value; OnPropertyChanged(); UpdateCompleteness(); }
        }

        private string _productCode; // Mã sản phẩm từ PLC đầu đọc (ví dụ PLC đầu)
        public string ProductCode
        {
            get => _productCode;
            set { _productCode = value; OnPropertyChanged(); UpdateCompleteness(); }
        }

        private string _testMachineId; // ID của máy test (nếu có nhiều)
        public string TestMachineId
        {
            get => _testMachineId;
            set { _testMachineId = value; OnPropertyChanged(); /* <PERSON>hông ảnh hưởng trực tiếp đến completeness */ }
        }

        private TestResultStatus _testResult; // Kết quả OK/NG từ máy test (hoặc PLC máy test)
        public TestResultStatus TestResult
        {
            get => _testResult;
            set { _testResult = value; OnPropertyChanged(); UpdateCompleteness(); }
        }

        private DateTime _recordTimestamp;
        public DateTime RecordTimestamp
        {
            get => _recordTimestamp;
            set { _recordTimestamp = value; OnPropertyChanged(); }
        }

        // public string OperatorId { get; set; }
        // public TimeSpan CycleTime { get; set; }

        // Trạng thái hoàn thành
        public bool IsSequenceNumberReceived => SequenceNumber.HasValue;
        public bool IsProductCodeReceived => !string.IsNullOrEmpty(ProductCode);
        public bool IsTestResultReceived => TestResult != TestResultStatus.None && TestResult != TestResultStatus.Testing && TestResult != TestResultStatus.AwaitingProduct; // Hoặc logic phù hợp hơn

        private bool _isComplete;
        public bool IsComplete
        {
            get => _isComplete;
            private set { _isComplete = value; OnPropertyChanged(); } // Chỉ set private
        }

        public ProductRecord()
        {
            RecordTimestamp = DateTime.Now; // Hoặc set khi bản ghi hoàn thành
            TestResult = TestResultStatus.AwaitingProduct; // Trạng thái ban đầu
        }

        private void UpdateCompleteness()
        {
            IsComplete = IsSequenceNumberReceived && IsProductCodeReceived && IsTestResultReceived;
            if (IsComplete)
            {
                RecordTimestamp = DateTime.Now; // Cập nhật timestamp khi hoàn thành
            }
            OnPropertyChanged(nameof(IsSequenceNumberReceived));
            OnPropertyChanged(nameof(IsProductCodeReceived));
            OnPropertyChanged(nameof(IsTestResultReceived));
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public override string ToString()
        {
            return $"Seq: {SequenceNumber?.ToString() ?? "N/A"}, Code: {ProductCode ?? "N/A"}, Result: {TestResult}, Complete: {IsComplete}";
        }
    }
}
