namespace ZoomableApp.Models
{
    public class WorkShift
    {
        public string Name { get; set; } = string.Empty;
        public string TimeRange { get; set; } = string.Empty;
        public string DisplayText => $"{Name}: {TimeRange}";

        public static List<WorkShift> GetAvailableShifts()
        {
            return new List<WorkShift>
            {
                new WorkShift { Name = "Ca hành chính", TimeRange = "8 giờ - 17 giờ" },
                new WorkShift { Name = "Ca sáng", TimeRange = "6 giờ - 11 giờ" },
                new WorkShift { Name = "Ca chiều", TimeRange = "14 giờ - 22 giờ" },
                new WorkShift { Name = "Ca đêm", TimeRange = "22 giờ - 6 giờ" }
            };
        }
    }
}
