using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using ZoomableApp.Models;

namespace ZoomableApp.Converters
{
    public class AdminRoleToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string role && role == "Administrator")
            {
                // Show warning only if current user is not Admin
                return UserSession.CurrentUser?.Role != "Administrator" ? Visibility.Visible : Visibility.Collapsed;
            }
            return Visibility.Collapsed;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
