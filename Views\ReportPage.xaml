<UserControl x:Class="ZoomableApp.Views.ReportPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="2000"
             Background="#F8F9FA">
    
    <UserControl.Resources>
        <!-- Converters -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- Button Style -->
        <Style x:Key="ReportButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980B9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#21618C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Export Button Style -->
        <Style x:Key="ExportButtonStyle" TargetType="Button" BasedOn="{StaticResource ReportButtonStyle}">
            <Setter Property="Background" Value="#27AE60"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#229954"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#1E8449"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Search Button Style -->
        <Style x:Key="SearchButtonStyle" TargetType="Button" BasedOn="{StaticResource ReportButtonStyle}">
            <Setter Property="Background" Value="#E67E22"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#D35400"/>
                </Trigger>
                <Trigger Property="IsPressed" Value="True">
                    <Setter Property="Background" Value="#BA4A00"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/> <!-- Header controls -->
            <RowDefinition Height="*"/>    <!-- Data grid -->
            <RowDefinition Height="Auto"/> <!-- Status bar -->
        </Grid.RowDefinitions>

        <!-- Header Controls -->
        <Border Grid.Row="0" Background="{StaticResource SurfaceBrush}" BorderBrush="{StaticResource BorderBrush}"
                BorderThickness="0,0,0,1" Padding="25" Style="{StaticResource ModernCardStyle}"
                CornerRadius="0" Margin="0">
            <Border.Effect>
                <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
            </Border.Effect>
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="10"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Title -->
                <TextBlock Grid.Row="0" Text="📊 BÁO CÁO SẢN XUẤT"
                           Style="{StaticResource HeadingLargeStyle}"
                           HorizontalAlignment="Center"/>

                <!-- Controls Row -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>   <!-- Left: Report buttons -->
                        <ColumnDefinition Width="20"/>  <!-- Gap -->
                        <ColumnDefinition Width="Auto"/> <!-- Right: Filters -->
                    </Grid.ColumnDefinitions>

                    <!-- Left: Report Type Buttons -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" HorizontalAlignment="Left">
                        <Button Content="📈 Sản lượng"
                                Background="#3498DB"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding ProductionCommand}"
                                Margin="0,0,10,0"/>
                        <Button Content="🐌 Thao tác chậm"
                                Background="#E67E22"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding SlowOperationCommand}"
                                Margin="0,0,10,0"
                                ToolTip="Đọc dữ liệu thao tác chậm từ PLC và lưu vào database"/>
                        <Button Content="📏 Đo thao tác"
                                Background="#9B59B6"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding MeasureOperationCommand}"
                                Margin="0,0,10,0"
                                ToolTip="Đọc dữ liệu đo thao tác từ PLC và lưu vào database"/>
                        <Button Content="📅 Tổng hợp tháng"
                                Background="#27AE60"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding MonthlyReportCommand}"
                                Margin="0,0,10,0"/>
                        <Button Content="⚠️ Lịch sử lỗi"
                                Background="#E74C3C"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding ErrorHistoryCommand}"
                                Margin="0,0,10,0"/>
                    </StackPanel>

                    <!-- Right: Filters and Actions -->
                    <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Right">
                        <!-- Date From -->
                        <StackPanel Orientation="Horizontal" Margin="8">
                            <TextBlock Text="Từ:" VerticalAlignment="Center" Margin="0,0,8,0"
                                       Style="{StaticResource BodyMediumStyle}" FontWeight="SemiBold"/>
                            <DatePicker x:Name="FromDatePicker"
                                        SelectedDate="{Binding FromDate, Mode=TwoWay}"
                                        Width="140" Height="Auto"
                                        />
                        </StackPanel>

                        <!-- Date To -->
                        <StackPanel Orientation="Horizontal" Margin="8">
                            <TextBlock Text="Đến:" VerticalAlignment="Center" Margin="0,0,8,0"
                                       Style="{StaticResource BodyMediumStyle}" FontWeight="SemiBold"/>
                            <DatePicker x:Name="ToDatePicker"
                                        SelectedDate="{Binding ToDate, Mode=TwoWay}"
                                        Width="140" Height="Auto"
                                        />
                        </StackPanel>

                        <!-- Work Shift -->
                        <StackPanel Orientation="Horizontal" Margin="8">
                            <TextBlock Text="Ca:" VerticalAlignment="Center" Margin="0,0,8,0"
                                       Style="{StaticResource BodyMediumStyle}" FontWeight="SemiBold"/>
                            <ComboBox x:Name="WorkShiftComboBox"
                                      ItemsSource="{Binding WorkShifts}"
                                      SelectedItem="{Binding SelectedWorkShift, Mode=TwoWay}"
                                      Width="200" Height="Auto"
                                      />
                        </StackPanel>

                        <!-- Report Type Selection -->
                        <StackPanel Orientation="Horizontal" Margin="8">
                            <TextBlock Text="Loại báo cáo:" VerticalAlignment="Center" Margin="0,0,8,0"
                                       Style="{StaticResource BodyMediumStyle}" FontWeight="SemiBold"/>
                            <ComboBox x:Name="ReportTypeComboBox"
                                      ItemsSource="{Binding ReportTypes}"
                                      SelectedItem="{Binding SelectedReportType, Mode=TwoWay}"
                                      Width="150" Height="Auto"
                                      FontSize="14"
                                      />
                        </StackPanel>

                        <!-- Search Button -->
                        <Button Content="🔍 Tìm kiếm"
                                Background="#34495E"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding SearchCommand}"
                                Margin="5,0,10,0"/>

                        <!-- Export Excel Button -->
                        <Button Content="📊 Xuất Excel"
                                Background="#27AE60"
                                Foreground="White"
                                FontSize="16"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding ExportExcelCommand}"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- Data Grid -->
        <Border Grid.Row="1" Background="{StaticResource SurfaceBrush}"
                Style="{StaticResource ModernCardStyle}" Margin="20,15,20,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Header Báo cáo -->
                <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20,12" CornerRadius="12,12,0,0">
                    <Border.Effect>
                        <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.2"/>
                    </Border.Effect>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="📊 Dữ liệu Báo cáo"
                                   Foreground="White"
                                   Style="{StaticResource HeadingSmallStyle}"
                                   VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding SelectedReportType, StringFormat='- {0}'}"
                                   Foreground="#BDC3C7"
                                   Style="{StaticResource BodyLargeStyle}"
                                   Margin="12,0,0,0"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- DataGrid Container -->
                <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" CornerRadius="0,0,5,5">
                    <Grid>
                        <!-- Loading Overlay -->
                        <Border Background="#80000000"
                                Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                                Panel.ZIndex="100">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <ProgressBar IsIndeterminate="True" Width="200" Height="10" Margin="0,0,0,10"/>
                                <TextBlock Text="Đang tải dữ liệu..." Foreground="White" FontSize="16" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Border>

                        <!-- Data Grid with Frozen Headers -->
                        <DataGrid x:Name="ReportDataGrid"
                                  ItemsSource="{Binding ReportData}"
                                  AutoGenerateColumns="False"
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="True"
                                  CanUserResizeColumns="True"
                                  CanUserResizeRows="False"
                                  IsReadOnly="True"
                                  SelectionMode="Extended"
                                  GridLinesVisibility="Horizontal"
                                  HeadersVisibility="Column"
                                  RowHeaderWidth="0"
                                  FontSize="14"
                                  RowHeight="32"
                                  MinWidth="1200"
                                  MinHeight="300"
                                  HorizontalAlignment="Stretch"
                                  VerticalAlignment="Stretch"
                                  ColumnHeaderHeight="40"
                                  HorizontalScrollBarVisibility="Auto"
                                  VerticalScrollBarVisibility="Auto"
                                  FrozenColumnCount="0"
                                  EnableColumnVirtualization="True"
                                  EnableRowVirtualization="True">

                                <DataGrid.Style>
                                    <Style TargetType="DataGrid">
                                        <Setter Property="Background" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="RowBackground" Value="White"/>
                                        <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
                                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                                        <Setter Property="HorizontalGridLinesBrush" Value="#E9ECEF"/>
                                    </Style>
                                </DataGrid.Style>

                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="SemiBold"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="Padding" Value="10,8"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,0"/>
                                        <Setter Property="BorderBrush" Value="#2C3E50"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>

                                <DataGrid.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="Padding" Value="8,6"/>
                                        <Setter Property="FontSize" Value="14"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="#3498DB"/>
                                                <Setter Property="Foreground" Value="White"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.CellStyle>

                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow">
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E8F4FD"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="STT" Width="60" MinWidth="50" MaxWidth="80" CanUserResize="True">
                                        <DataGridTextColumn.Binding>
                                            <Binding Path="Id"/>
                                        </DataGridTextColumn.Binding>
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Thời gian" Binding="{Binding Timestamp}" Width="160" MinWidth="140" MaxWidth="200" CanUserResize="True"/>
                                    <DataGridTextColumn Header="Trạm" Binding="{Binding Station}" Width="100" MinWidth="80" MaxWidth="150" CanUserResize="True"/>
                                    <DataGridTextColumn Header="SP OK" Binding="{Binding Product_OK}" Width="80" MinWidth="70" MaxWidth="120" CanUserResize="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#27AE60"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="SP NG" Binding="{Binding Product_NG}" Width="80" MinWidth="70">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="Foreground" Value="#E74C3C"/>
                                                <Setter Property="FontWeight" Value="SemiBold"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Tổng SP" Binding="{Binding Product_Total}" Width="90" MinWidth="80">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Setter Property="FontWeight" Value="Bold"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="TG hoàn thành" Binding="{Binding Time_Complete, StringFormat=F2}" Width="120" MinWidth="100" MaxWidth="160" CanUserResize="True"/>
                                    <DataGridTextColumn Header="TG trễ" Binding="{Binding Time_Delay}" Width="90" MinWidth="80" MaxWidth="130" CanUserResize="True"/>
                                    <DataGridTextColumn Header="TG dừng" Binding="{Binding Time_Stop}" Width="90" MinWidth="80" MaxWidth="130" CanUserResize="True"/>
                                    <DataGridTextColumn Header="Số lần dừng" Binding="{Binding Number_Stop}" Width="110" MinWidth="90" MaxWidth="150" CanUserResize="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Mã lỗi" Binding="{Binding Error_Code}" Width="120" MinWidth="100" MaxWidth="180" CanUserResize="True">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="HorizontalAlignment" Value="Center"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Error_Code}" Value="CONFIG_ERROR">
                                                        <Setter Property="Foreground" Value="#E74C3C"/>
                                                        <Setter Property="FontWeight" Value="Bold"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                    <DataGridTextColumn Header="Ca làm việc" Binding="{Binding WorkShift}" Width="180" MinWidth="150" MaxWidth="220" CanUserResize="True"/>
                                    <DataGridTextColumn Header="Ghi chú" Binding="{Binding Notes}" Width="*" MinWidth="150" CanUserResize="True"/>
                                </DataGrid.Columns>
                            </DataGrid>
                    </Grid>
                </Border>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="2" Background="{StaticResource PrimaryBrush}" Padding="20,12"
                Margin="20,0,20,15" CornerRadius="8">
            <Border.Effect>
                <DropShadowEffect Color="#40000000" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.15"/>
            </Border.Effect>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="{Binding StatusMessage}"
                           Foreground="White"
                           Style="{StaticResource BodyLargeStyle}"
                           FontWeight="Medium"
                           VerticalAlignment="Center"/>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Tổng số bản ghi: "
                               Foreground="#BDC3C7"
                               Style="{StaticResource BodyLargeStyle}"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding ReportData.Count}"
                               Foreground="White"
                               Style="{StaticResource BodyLargeStyle}"
                               FontWeight="Bold"
                               VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>
