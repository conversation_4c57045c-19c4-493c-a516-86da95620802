﻿using System;
using System.Configuration;
using System.Data;
using System.IO;
using System.Runtime.InteropServices;
using System.Windows;
using ZoomableApp.Models;
using ZoomableApp.PLC;
using ZoomableApp.Services;

namespace ZoomableApp;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    [DllImport("kernel32.dll", SetLastError = true)]
    [return: MarshalAs(UnmanagedType.Bool)]
    static extern bool AllocConsole();

    [DllImport("kernel32.dll", SetLastError = true)]
    [return: MarshalAs(UnmanagedType.Bool)]
    static extern bool FreeConsole();

    private bool _consoleAllocated = false;
    protected override async void OnStartup(StartupEventArgs e)
    {
        // Initialize ServiceContainer FIRST before creating any windows
        InitializeServiceContainer();

        // Allocate console for debugging (if enabled in config)
        if (ConfigurationService.GetConsoleLoggingEnabled())
        {
            AllocateConsole();
            Console.WriteLine("Console log initialized!");
        }

        base.OnStartup(e);

        // Không tự động khởi tạo database - sử dụng database có sẵn trong thư mục Data
        // await DatabaseInitializer.InitializeDatabaseAsync();

        // Tạo file Excel mẫu cho maintenance nếu chưa có
        // CreateMaintenanceExcel.CreateSampleFile();

        // Tạo và hiển thị LoginWindow (ServiceContainer is now available)
        LoginWindow loginWindow = new LoginWindow();
        loginWindow.Show();
    }

    protected override void OnExit(ExitEventArgs e)
    {
        if (_consoleAllocated)
        {
            Console.WriteLine("Application exiting...");
            FreeConsole();
            _consoleAllocated = false;
        }
        base.OnExit(e);
    }

    private void AllocateConsole()
    {
        // Allocate a console for this GUI application
        AllocConsole();
        Console.OutputEncoding = System.Text.Encoding.UTF8;
        _consoleAllocated = true;

        // Redirect console output to the allocated console
        Console.SetOut(new StreamWriter(Console.OpenStandardOutput()) { AutoFlush = true });
        Console.SetError(new StreamWriter(Console.OpenStandardError()) { AutoFlush = true });
        Console.SetIn(new StreamReader(Console.OpenStandardInput()));

        Console.WriteLine("Console window opened successfully!");
        Console.WriteLine("WPF Application started at: " + DateTime.Now);
    }

    private void InitializeServiceContainer()
    {
        try
        {
            // Clear configuration cache to ensure fresh settings are loaded
            ConfigurationService.ClearCache();

            // Initialize Service Container with PLC mode from config
            var plcMode = ConfigurationService.GetPlcMode();
            ServiceContainer.Initialize(plcMode);

            Console.WriteLine($"[App] ServiceContainer initialized with PLC mode: {plcMode}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[App] Error initializing ServiceContainer: {ex.Message}");
            // Fallback to Mock mode
            ServiceContainer.Initialize(PlcMode.Mock);
        }
    }
}

