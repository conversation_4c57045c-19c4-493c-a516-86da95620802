using System;
using System.IO;
using System.Threading;

namespace ZoomableApp.Services
{
    /// <summary>
    /// File-based logging service that writes logs to daily log files
    /// </summary>
    public class FileLoggerService : ILoggerService
    {
        private readonly string _logDirectory;
        private readonly object _lockObject = new object();
        private readonly bool _enableDebugLogs;

        public FileLoggerService(string logDirectory = "Logs", bool enableDebugLogs = true)
        {
            _logDirectory = logDirectory;
            _enableDebugLogs = enableDebugLogs;
            
            // Create logs directory if it doesn't exist
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
        }

        public void LogInfo(string message)
        {
            WriteLog("INFO", message);
        }

        public void LogWarning(string message)
        {
            WriteLog("WARN", message);
        }

        public void LogError(string message)
        {
            WriteLog("ERROR", message);
        }

        public void LogError(string message, Exception exception)
        {
            var fullMessage = $"{message}\nException: {exception.GetType().Name}: {exception.Message}\nStackTrace: {exception.StackTrace}";
            WriteLog("ERROR", fullMessage);
        }

        public void LogDebug(string message)
        {
            if (_enableDebugLogs)
            {
                WriteLog("DEBUG", message);
            }
        }

        private void WriteLog(string level, string message)
        {
            try
            {
                lock (_lockObject)
                {
                    var timestamp = DateTime.Now;
                    var logFileName = $"ZoomableApp_{timestamp:yyyy-MM-dd}.log";
                    var logFilePath = Path.Combine(_logDirectory, logFileName);
                    
                    var logEntry = $"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{level}] [{Thread.CurrentThread.ManagedThreadId}] {message}";
                    
                    File.AppendAllText(logFilePath, logEntry + Environment.NewLine);
                    
                    // Also write to debug console for development
                    Console.WriteLine(logEntry);
                }
            }
            catch (Exception ex)
            {
                // Fallback to debug console if file logging fails
                Console.WriteLine($"[LOGGER ERROR] Failed to write log: {ex.Message}");
                Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] [{level}] {message}");
            }
        }

        /// <summary>
        /// Clean up old log files (older than specified days)
        /// </summary>
        /// <param name="daysToKeep">Number of days to keep log files</param>
        public void CleanupOldLogs(int daysToKeep = 30)
        {
            try
            {
                if (!Directory.Exists(_logDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var logFiles = Directory.GetFiles(_logDirectory, "ZoomableApp_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                        LogInfo($"Deleted old log file: {Path.GetFileName(logFile)}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("Failed to cleanup old log files", ex);
            }
        }
    }
}
