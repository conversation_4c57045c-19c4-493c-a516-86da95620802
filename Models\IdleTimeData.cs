using System;

namespace ZoomableApp.Models
{
    public class IdleTimeData
    {
        public TimeSpan DailyAllowedIdleTime { get; set; } = TimeSpan.FromHours(2);
        public TimeSpan MonthlyAllowedIdleTime { get; set; } = TimeSpan.FromHours(40);
        public TimeSpan DailyUsedIdleTime { get; set; } = TimeSpan.Zero;
        public TimeSpan MonthlyUsedIdleTime { get; set; } = TimeSpan.Zero;

        public TimeSpan DailyRemainingIdleTime => DailyAllowedIdleTime - DailyUsedIdleTime;
        public TimeSpan MonthlyRemainingIdleTime => MonthlyAllowedIdleTime - MonthlyUsedIdleTime;

        public double DailyUsagePercentage => DailyAllowedIdleTime.TotalMinutes > 0 
            ? (DailyUsedIdleTime.TotalMinutes / DailyAllowedIdleTime.TotalMinutes) * 100 
            : 0;

        public double MonthlyUsagePercentage => MonthlyAllowedIdleTime.TotalMinutes > 0 
            ? (MonthlyUsedIdleTime.TotalMinutes / MonthlyAllowedIdleTime.TotalMinutes) * 100 
            : 0;
    }

    public class ProductionSummaryData
    {
        public int PlanQuantity { get; set; }
        public int ActualQuantity { get; set; }
        public int Gap => ActualQuantity - PlanQuantity;

        public int OkQuantity { get; set; }
        public int NgQuantity { get; set; }
        public int ReworkQuantity { get; set; }

        /// <summary>
        /// Total quantity read directly from PLC register (Product_Total)
        /// </summary>
        public int TotalQuantity { get; set; }

        public double OkPercentage => TotalQuantity > 0 ? (double)OkQuantity / TotalQuantity * 100 : 0;
        public double NgPercentage => TotalQuantity > 0 ? (double)NgQuantity / TotalQuantity * 100 : 0;
        public double ReworkPercentage => TotalQuantity > 0 ? (double)ReworkQuantity / TotalQuantity * 100 : 0;
    }
}
