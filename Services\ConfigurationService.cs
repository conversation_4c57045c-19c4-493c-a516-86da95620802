using System;
using System.IO;
using System.Text.Json;
using System.Diagnostics;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service for reading application configuration from appsettings.json
    /// </summary>
    public static class ConfigurationService
    {
        private static readonly string ConfigFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
        private static AppSettings _cachedSettings;

        /// <summary>
        /// Gets the current PLC operating mode from configuration
        /// </summary>
        /// <returns>PLC mode (Real or Mock)</returns>
        public static PlcMode GetPlcMode()
        {
            try
            {
                var settings = GetAppSettings();
                var modeString = settings?.PlcSettings?.Mode ?? "Mock";
                
                if (Enum.TryParse<PlcMode>(modeString, true, out var mode))
                {
                    Console.WriteLine($"ConfigurationService: PLC Mode = {mode}");
                    return mode;
                }
                
                Console.WriteLine($"ConfigurationService: Invalid PLC mode '{modeString}', defaulting to Mock");
                return PlcMode.Mock;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ConfigurationService: Error reading PLC mode: {ex.Message}");
                return PlcMode.Mock; // Safe default
            }
        }

        /// <summary>
        /// Gets the mock data update interval in milliseconds
        /// </summary>
        /// <returns>Update interval in milliseconds</returns>
        public static int GetMockDataUpdateInterval()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.PlcSettings?.MockDataUpdateInterval ?? 2000;
            }
            catch
            {
                return 2000; // Default 2 seconds
            }
        }

        /// <summary>
        /// Gets whether random variation is enabled for mock data
        /// </summary>
        /// <returns>True if random variation is enabled</returns>
        public static bool IsRandomVariationEnabled()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.PlcSettings?.EnableRandomVariation ?? true;
            }
            catch
            {
                return true; // Default enabled
            }
        }

        /// <summary>
        /// Gets the dashboard refresh interval in milliseconds
        /// </summary>
        /// <returns>Refresh interval in milliseconds</returns>
        public static int GetDashboardRefreshInterval()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.DashboardSettings?.RefreshInterval ?? 5000;
            }
            catch
            {
                return 5000; // Default 5 seconds
            }
        }

        /// <summary>
        /// Gets whether console logging is enabled
        /// </summary>
        /// <returns>True if console logging is enabled</returns>
        public static bool GetConsoleLoggingEnabled()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.ConsoleLogging?.Enabled ?? true;
            }
            catch
            {
                return true; // Default enabled
            }
        }



        /// <summary>
        /// Gets whether auto-connect on startup is enabled
        /// </summary>
        /// <returns>True if auto-connect is enabled</returns>
        public static bool IsAutoConnectEnabled()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.PlcSettings?.AutoConnectOnStartup ?? true;
            }
            catch
            {
                return true; // Default enabled
            }
        }

        /// <summary>
        /// Gets the conveyor movement interval in milliseconds
        /// </summary>
        /// <returns>Movement interval in milliseconds</returns>
        public static int GetConveyorMovementInterval()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.SimulationSettings?.ConveyorMovementInterval ?? 5000;
            }
            catch
            {
                return 5000; // Default 5 seconds
            }
        }

        /// <summary>
        /// Gets the product entry interval in milliseconds
        /// </summary>
        /// <returns>Product entry interval in milliseconds</returns>
        public static int GetProductEntryInterval()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.SimulationSettings?.ProductEntryInterval ?? 7000;
            }
            catch
            {
                return 7000; // Default 7 seconds
            }
        }

        /// <summary>
        /// Gets the conveyor transfer time in milliseconds
        /// </summary>
        /// <returns>Transfer time in milliseconds</returns>
        public static int GetConveyorTransferTime()
        {
            try
            {
                var settings = GetAppSettings();
                return settings?.SimulationSettings?.ConveyorTransferTime ?? 1000;
            }
            catch
            {
                return 1000; // Default 1 second
            }
        }

        /// <summary>
        /// Gets the default layout to load on startup
        /// </summary>
        /// <returns>Layout name (Mainline or Inspection)</returns>
        public static string GetDefaultLayout()
        {
            try
            {
                var settings = GetAppSettings();
                var layoutName = settings?.LayoutSettings?.DefaultLayout ?? "Mainline";

                // Validate layout name
                if (layoutName != "Mainline" && layoutName != "Inspection")
                {
                    Console.WriteLine($"ConfigurationService: Invalid layout '{layoutName}', defaulting to Mainline");
                    return "Mainline";
                }

                Console.WriteLine($"ConfigurationService: Default Layout = {layoutName}");
                return layoutName;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ConfigurationService: Error reading default layout: {ex.Message}");
                return "Mainline"; // Safe default
            }
        }

        /// <summary>
        /// Get layout settings from configuration
        /// </summary>
        /// <returns>LayoutSettings object or null if not found</returns>
        public static LayoutSettings GetLayoutSettings()
        {
            try
            {
                var settings = GetAppSettings();
                var layoutSettings = settings?.LayoutSettings;

                if (layoutSettings == null)
                {
                    Console.WriteLine("ConfigurationService: LayoutSettings not found, returning default");
                    return new LayoutSettings { DefaultLayout = "Mainline" };
                }

                Console.WriteLine($"ConfigurationService: LayoutSettings loaded - DefaultLayout: {layoutSettings.DefaultLayout}");
                return layoutSettings;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ConfigurationService: Error getting layout settings: {ex.Message}");
                return new LayoutSettings { DefaultLayout = "Mainline" };
            }
        }

        private static AppSettings GetAppSettings()
        {
            if (_cachedSettings != null)
                return _cachedSettings;

            try
            {
                if (!File.Exists(ConfigFilePath))
                {
                    Console.WriteLine($"ConfigurationService: Config file not found at {ConfigFilePath}");
                    return CreateDefaultSettings();
                }

                var jsonString = File.ReadAllText(ConfigFilePath);
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    ReadCommentHandling = JsonCommentHandling.Skip
                };

                _cachedSettings = JsonSerializer.Deserialize<AppSettings>(jsonString, options);
                Console.WriteLine("ConfigurationService: Settings loaded successfully");
                return _cachedSettings;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ConfigurationService: Error loading settings: {ex.Message}");
                return CreateDefaultSettings();
            }
        }

        private static AppSettings CreateDefaultSettings()
        {
            return new AppSettings
            {
                PlcSettings = new PlcSettings
                {
                    Mode = "Mock",
                    MockDataUpdateInterval = 2000,
                    EnableRandomVariation = true,
                    AutoConnectOnStartup = true
                },
                DashboardSettings = new DashboardSettings
                {
                    RefreshInterval = 5000,
                    EnableRealTimeUpdates = true,
                    ChartAnimationDuration = 500
                },
                LayoutSettings = new LayoutSettings
                {
                    DefaultLayout = "Mainline"
                },
                SimulationSettings = new SimulationSettings
                {
                    ConveyorMovementInterval = 5000,
                    ProductEntryInterval = 7000,
                    ConveyorTransferTime = 1000
                }
            };
        }

        /// <summary>
        /// Clears the cached settings to force reload on next access
        /// </summary>
        public static void ClearCache()
        {
            _cachedSettings = null;
            Console.WriteLine("ConfigurationService: Settings cache cleared");
        }
    }

    // Configuration model classes
    public class AppSettings
    {
        public PlcSettings PlcSettings { get; set; }
        public DashboardSettings DashboardSettings { get; set; }
        public LayoutSettings LayoutSettings { get; set; }
        public SimulationSettings SimulationSettings { get; set; }
        public ConsoleLoggingSettings ConsoleLogging { get; set; }
    }

    public class PlcSettings
    {
        public string Mode { get; set; } = "Mock";
        public string Description { get; set; }
        public int MockDataUpdateInterval { get; set; } = 2000;
        public bool EnableRandomVariation { get; set; } = true;
        public bool AutoConnectOnStartup { get; set; } = true;
    }

    public class DashboardSettings
    {
        public int RefreshInterval { get; set; } = 5000;
        public bool EnableRealTimeUpdates { get; set; } = true;
        public int ChartAnimationDuration { get; set; } = 500;
    }

    public class LayoutSettings
    {
        public string DefaultLayout { get; set; } = "Mainline";
        public string Description { get; set; }
    }

    public class SimulationSettings
    {
        public int ConveyorMovementInterval { get; set; } = 5000;
        public int ProductEntryInterval { get; set; } = 7000;
        public int ConveyorTransferTime { get; set; } = 1000;
        public string Description { get; set; }
    }

    public class ConsoleLoggingSettings
    {
        public bool Enabled { get; set; } = true;
        public string Description { get; set; }
    }

    public static class DatabaseHelper
    {
        /// <summary>
        /// Gets the database connection string for SQLite
        /// </summary>
        /// <returns>SQLite connection string</returns>
        public static string GetConnectionString()
        {
            try
            {
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "panaDB.db");
                var connectionString = $"Data Source={dbPath}";
                Console.WriteLine($"DatabaseHelper: Database connection string: {connectionString}");
                return connectionString;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseHelper: Error creating connection string: {ex.Message}");
                // Fallback connection string
                return "Data Source=panaDB.db";
            }
        }


    }
}
