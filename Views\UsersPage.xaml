<UserControl x:Class="ZoomableApp.Views.UsersPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200">

    <UserControl.Resources>

        <!-- DataGrid Style -->
        <Style x:Key="ModernDataGridStyle" TargetType="DataGrid">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E1E8ED"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HorizontalGridLinesBrush" Value="#F7F9FA"/>
            <Setter Property="VerticalGridLinesBrush" Value="Transparent"/>
            <Setter Property="RowBackground" Value="White"/>
            <Setter Property="AlternatingRowBackground" Value="#F8F9FA"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="SelectionUnit" Value="FullRow"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
        </Style>

        <!-- DataGrid Header Style -->
        <Style x:Key="ModernDataGridColumnHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#34495E"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="BorderThickness" Value="0,0,1,0"/>
            <Setter Property="BorderBrush" Value="#2C3E50"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
        </Style>

        <!-- TextBox Style -->
        <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E1E8ED"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#3498DB"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- ComboBox Style -->
        <Style x:Key="ModernComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E1E8ED"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- PasswordBox Style -->
        <Style x:Key="ModernPasswordBoxStyle" TargetType="PasswordBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="#E1E8ED"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Style.Triggers>
                <Trigger Property="IsFocused" Value="True">
                    <Setter Property="BorderBrush" Value="#3498DB"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F8F9FA">
        <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <Grid Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- Header -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="👥" FontSize="24" Margin="0,0,10,0" VerticalAlignment="Center"/>
                        <TextBlock Text="QUẢN LÝ NGƯỜI DÙNG" FontSize="24" FontWeight="Bold" 
                                   Foreground="#2C3E50" VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="🔄 Làm mới" Margin='0,0,10,0'
                                Foreground="White"
                                Background="#FF45A5FF"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding RefreshCommand}"/>
                        <Button Content="➕ Thêm người dùng" 
                                Foreground="White"
                                Background="#FF00DC2C"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding AddUserCommand}"/>
                    </StackPanel>
                </Grid>

                <!-- Search Section -->
                <Border Grid.Row="1" Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20"
                        BorderBrush="#E1E8ED" BorderThickness="1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBox Grid.Column="0" 
                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 Style="{StaticResource ModernTextBoxStyle}"
                                 FontSize="14"
                                 Margin="0,0,10,0">
                            <TextBox.InputBindings>
                                <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
                            </TextBox.InputBindings>
                        </TextBox>

                        <Button Grid.Column="1" Content="🔍 Tìm kiếm" 
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding SearchCommand}"/>
                    </Grid>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,15">
                    <Button Content="✏️ Sửa" Margin="0,0,10,0"
                            Background="#E67E22"
                            Foreground="White"
                            Style="{StaticResource Modern3DButtonStyle}"
                            Command="{Binding EditUserCommand}"/>
                    <Button Content="🗑️ Xóa" 
                            Background="#E74C3C"
                            Foreground="White"
                            Style="{StaticResource Modern3DButtonStyle}"
                            Command="{Binding DeleteUserCommand}"/>
                </StackPanel>

                <!-- Users DataGrid -->
                <Border Grid.Row="3" Background="White" CornerRadius="8" 
                        BorderBrush="#E1E8ED" BorderThickness="1">
                    <DataGrid x:Name="UsersDataGrid"
                              ItemsSource="{Binding Users}"
                              SelectedItem="{Binding SelectedUser}"
                              Style="{StaticResource ModernDataGridStyle}"
                              ColumnHeaderStyle="{StaticResource ModernDataGridColumnHeaderStyle}">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" Binding="{Binding Id}" Width="60"/>
                            <DataGridTextColumn Header="Tên đăng nhập" Binding="{Binding Username}" Width="150"/>
                            <DataGridTextColumn Header="Họ tên" Binding="{Binding Fullname}" Width="200"/>
                            <DataGridTextColumn Header="Vai trò" Binding="{Binding Role}" Width="120"/>
                            <DataGridTextColumn Header="RFID" Binding="{Binding Rfid}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>

                <!-- Status Bar -->
                <Border Grid.Row="4" Background="#34495E" CornerRadius="6" Padding="15,10" Margin="0,20,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" Text="{Binding StatusMessage}" 
                                   Foreground="White" FontWeight="SemiBold"/>
                        
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock Text="Tổng số: " Foreground="#BDC3C7" Margin="0,0,5,0"/>
                            <TextBlock Text="{Binding Users.Count}" Foreground="White" FontWeight="Bold"/>
                            <TextBlock Text=" người dùng" Foreground="#BDC3C7" Margin="5,0,0,0"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Loading Overlay -->
                <Border Grid.Row="0" Grid.RowSpan="5" 
                        Background="#80000000" 
                        Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                        CornerRadius="8">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" Width="200" Height="4" 
                                     Background="Transparent" Foreground="#3498DB"/>
                        <TextBlock Text="Đang xử lý..." Foreground="White" FontSize="16" 
                                   FontWeight="SemiBold" Margin="0,15,0,0" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- Add User Dialog -->
        <Border Background="#80000000" 
                Visibility="{Binding IsAddUserDialogOpen, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border Background="White" CornerRadius="12" MaxWidth="500" MaxHeight="600" 
                    HorizontalAlignment="Center" VerticalAlignment="Center" Margin="20">
                <Grid Margin="30">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Dialog Header -->
                    <TextBlock Grid.Row="0" Text="➕ THÊM NGƯỜI DÙNG MỚI" 
                               FontSize="18" FontWeight="Bold" Foreground="#2C3E50" 
                               HorizontalAlignment="Center" Margin="0,0,0,20"/>

                    <!-- Dialog Content -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Tên đăng nhập *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewUser.Username, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Mật khẩu *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <PasswordBox x:Name="NewUserPasswordBox"
                                             Style="{StaticResource ModernPasswordBoxStyle}"
                                             PasswordChanged="NewUserPasswordBox_PasswordChanged"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Họ tên *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewUser.Fullname, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Vai trò *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding AvailableRoles}"
                                          SelectedItem="{Binding NewUser.Role}"
                                          Style="{StaticResource ModernComboBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="RFID" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding NewUser.Rfid, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBoxStyle}"/>
                                <TextBlock Text="Đưa thẻ lại gần bộ đọc thẻ" Margin="0,5,0,0"
                                           Foreground="#777" FontStyle="Italic"/>
                            </StackPanel>

                            <!-- Permission Warning -->
                            <Border Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" 
                                    CornerRadius="6" Padding="10" Margin="0,10,0,0"
                                    Visibility="{Binding NewUser.Role, Converter={StaticResource AdminRoleToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚠️" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Chỉ Admin mới có quyền tạo tài khoản Admin" 
                                               Foreground="#856404" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Dialog Buttons -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                        <Button Content="❌ Hủy" 
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding CancelAddUserCommand}"/>
                        <Button Content="💾 Lưu" 
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding SaveNewUserCommand}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Border>

        <!-- Edit User Dialog -->
        <Border Background="#80000000" 
                Visibility="{Binding IsEditUserDialogOpen, Converter={StaticResource BooleanToVisibilityConverter}}">
            <Border Background="White" CornerRadius="12" MaxWidth="500" MaxHeight="600" 
                    HorizontalAlignment="Center" VerticalAlignment="Center" Margin="20">
                <Grid Margin="30">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Dialog Header -->
                    <TextBlock Grid.Row="0" Text="✏️ CHỈNH SỬA NGƯỜI DÙNG" 
                               FontSize="18" FontWeight="Bold" Foreground="#2C3E50" 
                               HorizontalAlignment="Center" Margin="0,0,0,20"/>

                    <!-- Dialog Content -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Tên đăng nhập *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding EditingUser.Username, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Mật khẩu" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <PasswordBox x:Name="EditUserPasswordBox"
                                             Style="{StaticResource ModernPasswordBoxStyle}"
                                             PasswordChanged="EditUserPasswordBox_PasswordChanged"/>
                                <TextBlock Text="(Để trống nếu không muốn thay đổi)"
                                           FontSize="12" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Họ tên *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding EditingUser.Fullname, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="Vai trò *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding AvailableRoles}"
                                          SelectedItem="{Binding EditingUser.Role}"
                                          Style="{StaticResource ModernComboBoxStyle}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,0,15">
                                <TextBlock Text="RFID" FontWeight="SemiBold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding EditingUser.Rfid, UpdateSourceTrigger=PropertyChanged}"
                                         Style="{StaticResource ModernTextBoxStyle}"/>
                            </StackPanel>

                            <!-- Permission Warning -->
                            <Border Background="#FFF3CD" BorderBrush="#FFEAA7" BorderThickness="1" 
                                    CornerRadius="6" Padding="10" Margin="0,10,0,0"
                                    Visibility="{Binding EditingUser.Role, Converter={StaticResource AdminRoleToVisibilityConverter}}">
                                <StackPanel Orientation="Horizontal">
                                    <TextBlock Text="⚠️" FontSize="16" Margin="0,0,10,0"/>
                                    <TextBlock Text="Chỉ Admin mới có quyền phân quyền Admin" 
                                               Foreground="#856404" FontWeight="SemiBold"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Dialog Buttons -->
                    <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                        <Button Content="❌ Hủy" Margin="0,0,10,0"
                                Background="#FFAE2727"
                                Foreground="White"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding CancelEditUserCommand}"/>
                        <Button Content="💾 Cập nhật"
                                Background="#27AE60"
                                Foreground="White"
                                Style="{StaticResource Modern3DButtonStyle}"
                                Command="{Binding SaveEditUserCommand}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Border>
    </Grid>
</UserControl>
