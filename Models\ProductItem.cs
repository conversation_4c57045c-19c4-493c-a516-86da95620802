﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public class ProductItem : INotifyPropertyChanged
    {
        private string _productId;
        public string ProductId
        {
            get => _productId;
            set { _productId = value; OnPropertyChanged(nameof(ProductId)); }
        }

        private string _productCode;
        public string ProductCode
        {
            get => _productCode;
            set { _productCode = value; OnPropertyChanged(nameof(ProductCode)); }
        }

        private TestResultStatus _currentTestResult;
        public TestResultStatus CurrentTestResult
        {
            get => _currentTestResult;
            set { _currentTestResult = value; OnPropertyChanged(nameof(CurrentTestResult)); }
        }

        // To track which station it's logically associated with.
        // Could be an index or a direct reference to StationControl if needed by ProductItem itself.
        // For now, an index into MainlineLayout's list of stations is simpler.
        private int _currentStationIndex = -1; // -1 means not on any station / off-line
        public int CurrentStationIndex
        {
            get => _currentStationIndex;
            set { _currentStationIndex = value; OnPropertyChanged(nameof(CurrentStationIndex)); }
        }

        // PLC8_Tester data
        private int _testResult = 0; // 0 = NG, 1 = OK
        public int TestResult
        {
            get => _testResult;
            set { _testResult = value; OnPropertyChanged(nameof(TestResult)); OnPropertyChanged(nameof(TestResultText)); OnPropertyChanged(nameof(HasTestData)); }
        }

        private double _voltage = 0.0; // Voltage in volts
        public double Voltage
        {
            get => _voltage;
            set { _voltage = value; OnPropertyChanged(nameof(Voltage)); OnPropertyChanged(nameof(VoltageText)); OnPropertyChanged(nameof(HasTestData)); }
        }

        private int _sequenceNumber = 0; // Sequence number from tester
        public int SequenceNumber
        {
            get => _sequenceNumber;
            set { _sequenceNumber = value; OnPropertyChanged(nameof(SequenceNumber)); OnPropertyChanged(nameof(SequenceText)); OnPropertyChanged(nameof(HasTestData)); }
        }

        // Display properties for UI binding
        public string TestResultText => TestResult == 1 ? "OK" : (TestResult == 0 ? "NG" : "");
        public string VoltageText => Voltage > 0 ? $"{Voltage:F1}V" : "";
        public string SequenceText => SequenceNumber > 0 ? $"#{SequenceNumber}" : "";
        public bool HasTestData => TestResult > 0 || Voltage > 0 || SequenceNumber > 0;

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public ProductItem(string productId, string productCode)
        {
            ProductId = productId;
            ProductCode = productCode;
            CurrentTestResult = TestResultStatus.AwaitingProduct; // Default
            TestResult = -1; // Not tested yet
            Voltage = 0.0;
            SequenceNumber = 0;
        }
    }
}
