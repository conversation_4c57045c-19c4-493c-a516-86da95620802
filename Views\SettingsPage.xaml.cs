using System;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using ZoomableApp.ViewModels;
using System.IO;

namespace ZoomableApp.Views
{
    public partial class SettingsPage : UserControl
    {
        private SettingsViewModel _viewModel;

        public SettingsPage()
        {
            InitializeComponent();
            _viewModel = new SettingsViewModel();
            DataContext = _viewModel;
        }

        private void BrowsePlanFile_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Chọn file kế hoạch sản xuất",
                Filter = "Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*",
                CheckFileExists = true,
                CheckPathExists = true
            };

            // Set initial directory if current path exists
            if (!string.IsNullOrEmpty(_viewModel.PlanFilePath) && File.Exists(_viewModel.PlanFilePath))
            {
                openFileDialog.InitialDirectory = Path.GetDirectoryName(_viewModel.PlanFilePath);
                openFileDialog.FileName = Path.GetFileName(_viewModel.PlanFilePath);
            }

            if (openFileDialog.ShowDialog() == true)
            {
                _viewModel.PlanFilePath = openFileDialog.FileName;
            }
        }

        private void BrowseMaintenanceFile_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Chọn file kế hoạch bảo dưỡng",
                Filter = "Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*",
                CheckFileExists = true,
                CheckPathExists = true
            };

            // Set initial directory if current path exists
            if (!string.IsNullOrEmpty(_viewModel.MaintenancePlanPath) && File.Exists(_viewModel.MaintenancePlanPath))
            {
                openFileDialog.InitialDirectory = Path.GetDirectoryName(_viewModel.MaintenancePlanPath);
                openFileDialog.FileName = Path.GetFileName(_viewModel.MaintenancePlanPath);
            }

            if (openFileDialog.ShowDialog() == true)
            {
                _viewModel.MaintenancePlanPath = openFileDialog.FileName;
            }
        }

        private void BrowseIdlePlansFile_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Chọn file kế hoạch thời gian nghỉ",
                Filter = "Excel Files (*.xlsx;*.xls)|*.xlsx;*.xls|All Files (*.*)|*.*",
                CheckFileExists = true,
                CheckPathExists = true
            };

            // Set initial directory if current path exists
            if (!string.IsNullOrEmpty(_viewModel.IdlePlansFilePath) && File.Exists(_viewModel.IdlePlansFilePath))
            {
                openFileDialog.InitialDirectory = Path.GetDirectoryName(_viewModel.IdlePlansFilePath);
                openFileDialog.FileName = Path.GetFileName(_viewModel.IdlePlansFilePath);
            }

            if (openFileDialog.ShowDialog() == true)
            {
                _viewModel.IdlePlansFilePath = openFileDialog.FileName;
            }
        }

        private void BrowseDatabaseFolder_Click(object sender, RoutedEventArgs e)
        {
            using var folderDialog = new System.Windows.Forms.FolderBrowserDialog
            {
                Description = "Chọn thư mục chứa database",
                ShowNewFolderButton = true
            };

            // Set initial directory if current path exists
            if (!string.IsNullOrEmpty(_viewModel.DatabasePath) && Directory.Exists(_viewModel.DatabasePath))
            {
                folderDialog.SelectedPath = _viewModel.DatabasePath;
            }

            if (folderDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                _viewModel.DatabasePath = folderDialog.SelectedPath;
            }
        }

        private void AddPlc_Click(object sender, RoutedEventArgs e)
        {
            _viewModel.AddNewPlc();
        }

        private void DeletePlc_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is PlcConfigItem plc)
            {
                var result = MessageBox.Show(
                    $"Bạn có chắc chắn muốn xóa PLC '{plc.Name}' ({plc.Id})?",
                    "Xác nhận xóa",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _viewModel.RemovePlc(plc);
                }
            }
        }

        private void SaveAllButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate settings before saving
            if (!ValidateSettings())
            {
                return;
            }

            _viewModel.SaveAllSettings();
        }

        private bool ValidateSettings()
        {
            // Validate Excel file paths
            if (!string.IsNullOrEmpty(_viewModel.PlanFilePath) && !File.Exists(_viewModel.PlanFilePath))
            {
                MessageBox.Show(
                    "File kế hoạch sản xuất không tồn tại. Vui lòng chọn file hợp lệ.",
                    "Lỗi validation",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return false;
            }

            if (!string.IsNullOrEmpty(_viewModel.MaintenancePlanPath) && !File.Exists(_viewModel.MaintenancePlanPath))
            {
                MessageBox.Show(
                    "File kế hoạch bảo dưỡng không tồn tại. Vui lòng chọn file hợp lệ.",
                    "Lỗi validation",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return false;
            }

            if (!string.IsNullOrEmpty(_viewModel.IdlePlansFilePath) && !File.Exists(_viewModel.IdlePlansFilePath))
            {
                MessageBox.Show(
                    "File kế hoạch thời gian nghỉ không tồn tại. Vui lòng chọn file hợp lệ.",
                    "Lỗi validation",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return false;
            }

            // Validate database path
            if (!string.IsNullOrEmpty(_viewModel.DatabasePath) && !Directory.Exists(_viewModel.DatabasePath))
            {
                var result = MessageBox.Show(
                    "Thư mục database không tồn tại. Bạn có muốn tạo thư mục này?",
                    "Xác nhận tạo thư mục",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        Directory.CreateDirectory(_viewModel.DatabasePath);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show(
                            $"Không thể tạo thư mục database:\n{ex.Message}",
                            "Lỗi",
                            MessageBoxButton.OK,
                            MessageBoxImage.Error);
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }

            // Validate PLC configurations
            foreach (var plc in _viewModel.PlcConfigs)
            {
                if (string.IsNullOrWhiteSpace(plc.Id))
                {
                    MessageBox.Show(
                        "Tất cả PLC phải có ID hợp lệ.",
                        "Lỗi validation",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }

                if (string.IsNullOrWhiteSpace(plc.IpAddress))
                {
                    MessageBox.Show(
                        $"PLC '{plc.Id}' phải có địa chỉ IP hợp lệ.",
                        "Lỗi validation",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }

                if (plc.Port <= 0 || plc.Port > 65535)
                {
                    MessageBox.Show(
                        $"PLC '{plc.Id}' phải có port hợp lệ (1-65535).",
                        "Lỗi validation",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                    return false;
                }
            }

            // Validate data refresh interval
            if (_viewModel.DataRefreshInterval < 100 || _viewModel.DataRefreshInterval > 60000)
            {
                MessageBox.Show(
                    "Tần suất đọc dữ liệu phải từ 100ms đến 60000ms.",
                    "Lỗi validation",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                return false;
            }

            return true;
        }
    }
}
