{"PLC_Real_Test": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "ProductCodeAtReader", "PhysicalAddress": "D4000", "DataType": "STRING", "Length": 10}, {"LogicalName": "SequenceForProductCode", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}, {"LogicalName": "ConveyorStartStopBit", "PhysicalAddress": "M100", "DataType": "BIT", "Length": 1}, {"LogicalName": "Time_Stop_ST1", "PhysicalAddress": "D1132", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST2", "PhysicalAddress": "D1134", "DataType": "WORD", "Length": 1}, {"LogicalName": "Time_Stop_ST3", "PhysicalAddress": "D1136", "DataType": "WORD", "Length": 1}], "PLC1_Reader": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "ProductCodeAtReader", "PhysicalAddress": "D4000", "DataType": "STRING", "Length": 10}, {"LogicalName": "SequenceForProductCode", "PhysicalAddress": "D100", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}, {"LogicalName": "ConveyorStartStopBit", "PhysicalAddress": "M100", "DataType": "BIT", "Length": 1}], "PLC8_Tester": [{"LogicalName": "SystemReadyBit", "PhysicalAddress": "M1000", "DataType": "BIT", "Length": 1}, {"LogicalName": "TestResultFromTester", "PhysicalAddress": "D500", "DataType": "WORD", "Length": 1}, {"LogicalName": "SequenceNumberAtTest", "PhysicalAddress": "D502", "DataType": "WORD", "Length": 1}, {"LogicalName": "VoltageFromTester", "PhysicalAddress": "D504", "DataType": "WORD", "Length": 1}, {"LogicalName": "GenericTestDataWord", "PhysicalAddress": "D0", "DataType": "WORD", "Length": 1}], "PLC_GeneralStatus": [{"LogicalName": "OverallSystemError", "PhysicalAddress": "M2000", "DataType": "BIT", "Length": 1}, {"LogicalName": "EmergencyStopStatus", "PhysicalAddress": "X10", "DataType": "BIT", "Length": 1}], "PLC_MainLine": [{"LogicalName": "NumberProductOk", "PhysicalAddress": "D1000", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductNg", "PhysicalAddress": "D1002", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumberProductTotal", "PhysicalAddress": "D1004", "DataType": "WORD", "Length": 1}, {"LogicalName": "NumProductPresent", "PhysicalAddress": "D1006", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timemodel", "PhysicalAddress": "D1008", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecurrent", "PhysicalAddress": "D1010", "DataType": "WORD", "Length": 1}, {"LogicalName": "<PERSON><PERSON><PERSON>", "PhysicalAddress": "D1012", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeNextStep", "PhysicalAddress": "D1014", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest1", "PhysicalAddress": "D1020", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest2", "PhysicalAddress": "D1022", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest3", "PhysicalAddress": "D1024", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest4", "PhysicalAddress": "D1026", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest5", "PhysicalAddress": "D1028", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest6", "PhysicalAddress": "D1030", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest7", "PhysicalAddress": "D1032", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest8", "PhysicalAddress": "D1034", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest9", "PhysicalAddress": "D1036", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest10", "PhysicalAddress": "D1038", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest11", "PhysicalAddress": "D1040", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest12", "PhysicalAddress": "D1042", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest13", "PhysicalAddress": "D1044", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest14", "PhysicalAddress": "D1046", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest15", "PhysicalAddress": "D1048", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest16", "PhysicalAddress": "D1050", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest17", "PhysicalAddress": "D1052", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest18", "PhysicalAddress": "D1054", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest19", "PhysicalAddress": "D1056", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest20", "PhysicalAddress": "D1058", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest21", "PhysicalAddress": "D1060", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest22", "PhysicalAddress": "D1062", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest23", "PhysicalAddress": "D1064", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest24", "PhysicalAddress": "D1066", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest25", "PhysicalAddress": "D1068", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timecompletest26", "PhysicalAddress": "D1070", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst1", "PhysicalAddress": "D1080", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst2", "PhysicalAddress": "D1082", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst3", "PhysicalAddress": "D1084", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst4", "PhysicalAddress": "D1086", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst5", "PhysicalAddress": "D1088", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst6", "PhysicalAddress": "D1090", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst7", "PhysicalAddress": "D1092", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst8", "PhysicalAddress": "D1094", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst9", "PhysicalAddress": "D1096", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst10", "PhysicalAddress": "D1098", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst11", "PhysicalAddress": "D1100", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst12", "PhysicalAddress": "D1102", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst13", "PhysicalAddress": "D1104", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst14", "PhysicalAddress": "D1106", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst15", "PhysicalAddress": "D1108", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst16", "PhysicalAddress": "D1110", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst17", "PhysicalAddress": "D1112", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst18", "PhysicalAddress": "D1114", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst19", "PhysicalAddress": "D1116", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst20", "PhysicalAddress": "D1118", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst21", "PhysicalAddress": "D1120", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst22", "PhysicalAddress": "D1122", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst23", "PhysicalAddress": "D1124", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst24", "PhysicalAddress": "D1126", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst25", "PhysicalAddress": "D1128", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timedelayst26", "PhysicalAddress": "D1130", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst1", "PhysicalAddress": "D1132", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst2", "PhysicalAddress": "D1134", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst3", "PhysicalAddress": "D1136", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst4", "PhysicalAddress": "D1138", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst5", "PhysicalAddress": "D1140", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst6", "PhysicalAddress": "D1142", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst7", "PhysicalAddress": "D1144", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst8", "PhysicalAddress": "D1146", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst9", "PhysicalAddress": "D1148", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst10", "PhysicalAddress": "D1150", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst11", "PhysicalAddress": "D1152", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst12", "PhysicalAddress": "D1154", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst13", "PhysicalAddress": "D1156", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst14", "PhysicalAddress": "D1158", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst15", "PhysicalAddress": "D1160", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst16", "PhysicalAddress": "D1162", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst17", "PhysicalAddress": "D1164", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst18", "PhysicalAddress": "D1166", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst19", "PhysicalAddress": "D1168", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst20", "PhysicalAddress": "D1170", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst21", "PhysicalAddress": "D1172", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst22", "PhysicalAddress": "D1174", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst23", "PhysicalAddress": "D1176", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst24", "PhysicalAddress": "D1178", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst25", "PhysicalAddress": "D1180", "DataType": "WORD", "Length": 1}, {"LogicalName": "Timestopst26", "PhysicalAddress": "D1182", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst1", "PhysicalAddress": "D1190", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst2", "PhysicalAddress": "D1191", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst3", "PhysicalAddress": "D1192", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst4", "PhysicalAddress": "D1193", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst5", "PhysicalAddress": "D1194", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst6", "PhysicalAddress": "D1195", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst7", "PhysicalAddress": "D1196", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst8", "PhysicalAddress": "D1197", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst9", "PhysicalAddress": "D1198", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst10", "PhysicalAddress": "D1199", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst11", "PhysicalAddress": "D1200", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst12", "PhysicalAddress": "D1201", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst13", "PhysicalAddress": "D1202", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst14", "PhysicalAddress": "D1203", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst15", "PhysicalAddress": "D1204", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst16", "PhysicalAddress": "D1205", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst17", "PhysicalAddress": "D1206", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst18", "PhysicalAddress": "D1207", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst19", "PhysicalAddress": "D1208", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst20", "PhysicalAddress": "D1209", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst21", "PhysicalAddress": "D1210", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst22", "PhysicalAddress": "D1211", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst23", "PhysicalAddress": "D1212", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst24", "PhysicalAddress": "D1213", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst25", "PhysicalAddress": "D1214", "DataType": "WORD", "Length": 1}, {"LogicalName": "Numberstopst26", "PhysicalAddress": "D1215", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeRunningHour", "PhysicalAddress": "D1400", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeRunningMinute", "PhysicalAddress": "D1401", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeRunningSecond", "PhysicalAddress": "D1402", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopHour", "PhysicalAddress": "D1406", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopMinute", "PhysicalAddress": "D1407", "DataType": "WORD", "Length": 1}, {"LogicalName": "TimeStopSecond", "PhysicalAddress": "D1408", "DataType": "WORD", "Length": 1}, {"LogicalName": "NotError", "PhysicalAddress": "M0", "DataType": "BIT", "Length": 1}, {"LogicalName": "Automode", "PhysicalAddress": "M1", "DataType": "BIT", "Length": 1}, {"LogicalName": "Manmode", "PhysicalAddress": "M2", "DataType": "BIT", "Length": 1}, {"LogicalName": "Originmode", "PhysicalAddress": "M3", "DataType": "BIT", "Length": 1}, {"LogicalName": "Homedone", "PhysicalAddress": "M4", "DataType": "BIT", "Length": 1}, {"LogicalName": "Runningmode", "PhysicalAddress": "M5", "DataType": "BIT", "Length": 1}, {"LogicalName": "Stopmode", "PhysicalAddress": "M6", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4000", "PhysicalAddress": "M4000", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4001", "PhysicalAddress": "M4001", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4002", "PhysicalAddress": "M4002", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4003", "PhysicalAddress": "M4003", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4004", "PhysicalAddress": "M4004", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4005", "PhysicalAddress": "M4005", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4006", "PhysicalAddress": "M4006", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4007", "PhysicalAddress": "M4007", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4008", "PhysicalAddress": "M4008", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4009", "PhysicalAddress": "M4009", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4010", "PhysicalAddress": "M4010", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4011", "PhysicalAddress": "M4011", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4012", "PhysicalAddress": "M4012", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4013", "PhysicalAddress": "M4013", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4014", "PhysicalAddress": "M4014", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4015", "PhysicalAddress": "M4015", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4016", "PhysicalAddress": "M4016", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4017", "PhysicalAddress": "M4017", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4018", "PhysicalAddress": "M4018", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4019", "PhysicalAddress": "M4019", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4020", "PhysicalAddress": "M4020", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4021", "PhysicalAddress": "M4021", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4022", "PhysicalAddress": "M4022", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4023", "PhysicalAddress": "M4023", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4024", "PhysicalAddress": "M4024", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4025", "PhysicalAddress": "M4025", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4026", "PhysicalAddress": "M4026", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4027", "PhysicalAddress": "M4027", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4028", "PhysicalAddress": "M4028", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4029", "PhysicalAddress": "M4029", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4030", "PhysicalAddress": "M4030", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4031", "PhysicalAddress": "M4031", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4032", "PhysicalAddress": "M4032", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4033", "PhysicalAddress": "M4033", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4034", "PhysicalAddress": "M4034", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4035", "PhysicalAddress": "M4035", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4036", "PhysicalAddress": "M4036", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4037", "PhysicalAddress": "M4037", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4038", "PhysicalAddress": "M4038", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4039", "PhysicalAddress": "M4039", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4040", "PhysicalAddress": "M4040", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4041", "PhysicalAddress": "M4041", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4042", "PhysicalAddress": "M4042", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4043", "PhysicalAddress": "M4043", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4044", "PhysicalAddress": "M4044", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4045", "PhysicalAddress": "M4045", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4046", "PhysicalAddress": "M4046", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4047", "PhysicalAddress": "M4047", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4048", "PhysicalAddress": "M4048", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4049", "PhysicalAddress": "M4049", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4050", "PhysicalAddress": "M4050", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4051", "PhysicalAddress": "M4051", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4052", "PhysicalAddress": "M4052", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4053", "PhysicalAddress": "M4053", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4054", "PhysicalAddress": "M4054", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4055", "PhysicalAddress": "M4055", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4056", "PhysicalAddress": "M4056", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4057", "PhysicalAddress": "M4057", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4058", "PhysicalAddress": "M4058", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4059", "PhysicalAddress": "M4059", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4060", "PhysicalAddress": "M4060", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4061", "PhysicalAddress": "M4061", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4062", "PhysicalAddress": "M4062", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4063", "PhysicalAddress": "M4063", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4064", "PhysicalAddress": "M4064", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4065", "PhysicalAddress": "M4065", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4066", "PhysicalAddress": "M4066", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4067", "PhysicalAddress": "M4067", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4068", "PhysicalAddress": "M4068", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4069", "PhysicalAddress": "M4069", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4070", "PhysicalAddress": "M4070", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4071", "PhysicalAddress": "M4071", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4072", "PhysicalAddress": "M4072", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4073", "PhysicalAddress": "M4073", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4074", "PhysicalAddress": "M4074", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4075", "PhysicalAddress": "M4075", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4076", "PhysicalAddress": "M4076", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4077", "PhysicalAddress": "M4077", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4078", "PhysicalAddress": "M4078", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4079", "PhysicalAddress": "M4079", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4080", "PhysicalAddress": "M4080", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4081", "PhysicalAddress": "M4081", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4082", "PhysicalAddress": "M4082", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4083", "PhysicalAddress": "M4083", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4084", "PhysicalAddress": "M4084", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4085", "PhysicalAddress": "M4085", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4086", "PhysicalAddress": "M4086", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4087", "PhysicalAddress": "M4087", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4088", "PhysicalAddress": "M4088", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4089", "PhysicalAddress": "M4089", "DataType": "BIT", "Length": 1}, {"LogicalName": "M4090", "PhysicalAddress": "M4090", "DataType": "BIT", "Length": 1}]}