﻿<UserControl x:Class="ZoomableApp.Layouts.MainlineLayout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:localEnums="clr-namespace:ZoomableApp.Models"
             xmlns:shared="clr-namespace:ZoomableApp.SharedControls"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="800"
             Background="Transparent">
    <Grid>

        <StackPanel x:Name="StationStackPanel" Orientation="Horizontal" VerticalAlignment="Top"
                    Background="Transparent">
        </StackPanel>
        
        <Canvas x:Name="ProductLayerCanvas"
                VerticalAlignment="Top"
                Height="{Binding ElementName=StationStackPanel, Path=ActualHeight}"
                Width="{Binding ElementName=StationStackPanel, Path=ActualWidth}"
                Background="Transparent" />
    </Grid>
</UserControl>
