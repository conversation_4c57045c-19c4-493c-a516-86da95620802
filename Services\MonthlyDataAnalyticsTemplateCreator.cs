using System;
using System.IO;
using ClosedXML.Excel;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service to create the MonthlyDataAnalytics Excel template
    /// </summary>
    public class MonthlyDataAnalyticsTemplateCreator
    {
        /// <summary>
        /// Create the MonthlyDataAnalytics Excel template with named cells
        /// </summary>
        public static void CreateTemplate()
        {
            try
            {
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "excel", "MonthlyDataAnalytics.xlsx");
                
                // Ensure the excel directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(templatePath));
                
                using var workbook = new XLWorkbook();
                var worksheet = workbook.Worksheets.Add("Monthly Analytics");
                
                // Set up the template structure
                worksheet.Cell(1, 1).Value = "MONTHLY DATA ANALYTICS REPORT";
                worksheet.Cell(1, 1).Style.Font.Bold = true;
                worksheet.Cell(1, 1).Style.Font.FontSize = 16;
                worksheet.Range(1, 1, 1, 4).Merge();
                
                worksheet.Cell(2, 1).Value = $"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";
                
                // Create the data structure
                worksheet.Cell(4, 1).Value = "Metric";
                worksheet.Cell(4, 2).Value = "Value";
                worksheet.Cell(4, 1).Style.Font.Bold = true;
                worksheet.Cell(4, 2).Style.Font.Bold = true;
                
                // Add the named cells
                worksheet.Cell(5, 1).Value = "total";
                worksheet.Cell(5, 2).Value = "0"; // Default value
                
                worksheet.Cell(6, 1).Value = "totalOK";
                worksheet.Cell(6, 2).Value = "0"; // Default value
                
                worksheet.Cell(7, 1).Value = "totalNG";
                worksheet.Cell(7, 2).Value = "0"; // Default value
                
                worksheet.Cell(8, 1).Value = "AvgStopTime";
                worksheet.Cell(8, 2).Value = "0.00"; // Default value
                
                // Format the columns
                worksheet.Column(1).Width = 20;
                worksheet.Column(2).Width = 15;
                
                // Add borders
                var dataRange = worksheet.Range(4, 1, 8, 2);
                dataRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
                dataRange.Style.Border.InsideBorder = XLBorderStyleValues.Thin;
                
                // Save the template
                workbook.SaveAs(templatePath);
                
                Console.WriteLine($"MonthlyDataAnalytics template created at: {templatePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating MonthlyDataAnalytics template: {ex.Message}");
                throw;
            }
        }
    }
}
