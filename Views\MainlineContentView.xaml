<UserControl x:Class="ZoomableApp.Views.MainlineContentView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp"
             xmlns:views="clr-namespace:ZoomableApp.Views"
             xmlns:converters="clr-namespace:ZoomableApp.Converters"

             mc:Ignorable="d"
             d:DesignHeight="900" d:DesignWidth="1600">

    <UserControl.Resources>
        <converters:DailyPlanItemHighlightConverter x:Key="DailyPlanItemHighlightConverter"/>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="2*"/>
            <!-- Zoom/Pan area - 1/3 -->
            <RowDefinition Height="3*"/>
            <!-- Bottom area - 2/3 -->
            <RowDefinition Height="6*"/>
            <!-- Bottom area - 2/3 -->
        </Grid.RowDefinitions>

        <!-- Zoom/Pan Area -->
        <Grid Grid.Row="0" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="5*"/>
                <ColumnDefinition Width="6*"/>
                <ColumnDefinition Width="554*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <!-- Controls -->
                <RowDefinition Height="*"/>
                <!-- Viewport -->
            </Grid.RowDefinitions>

            <!-- Layout controls -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10" Grid.ColumnSpan="3" HorizontalAlignment="Center">
                <TextBlock x:Name="CurrentLayoutTextBlock" Text="Hệ thống: Mainline" VerticalAlignment="Center" Margin="0,0,20,0" Foreground="{StaticResource SurfaceBrush}" FontWeight="Bold"/>
                <Button Content="Đặt lại" Margin="0,0,20,0" Click="ResetButton_Click"/>

                <!-- Lỗi Mainline -->
                <TextBlock x:Name="MainLineTxt" VerticalAlignment="Center" Margin="0,0,20,0" Foreground="{StaticResource SurfaceBrush}">
                    <Run Text="Mainline: "/>
                    <Run x:Name="MainlineFaultRun" Text="Không có lỗi" Foreground="#4ECDC4"/>
                </TextBlock>
            </StackPanel>

            <!-- Viewport -->
            <Border CornerRadius="10"  Grid.Row="1" x:Name="ViewportBorder"
                BorderBrush="Gray" BorderThickness="2"
                ClipToBounds="True"
                MouseWheel="ViewportBorder_MouseWheel"
                MouseDown="ViewportBorder_MouseDown"
                MouseMove="ViewportBorder_MouseMove"
                MouseUp="ViewportBorder_MouseUp" Grid.ColumnSpan="3">

                <!-- Vùng nội dung sẽ được zoom/pan -->
                <Canvas x:Name="ZoomPanCanvas"
                    Width="2500" Height="300"
                    >
                    <Canvas.RenderTransform>
                        <TransformGroup>
                            <ScaleTransform x:Name="ViewScaleTransform" ScaleX="0.5" ScaleY="0.5"/>
                            <TranslateTransform x:Name="ViewTranslateTransform" X="0" Y="0"/>
                        </TransformGroup>
                    </Canvas.RenderTransform>
                    <!-- NỘI DUNG SẼ ĐƯỢC TẢI ĐỘNG Ở ĐÂY -->
                </Canvas>
            </Border>
        </Grid>

        <Grid Grid.Row="1" Margin="10,0,10,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*"/>
                <ColumnDefinition Width="10"/>
                <!-- Gap -->
                <ColumnDefinition Width="3*"/>
                <ColumnDefinition Width="10"/>
                <!-- Gap -->
                <ColumnDefinition Width="4*"/>
            </Grid.ColumnDefinitions>
            <!-- Daily Idle Time -->
            <Border Grid.Column="0" BorderThickness="2" CornerRadius="8" Padding="0,0,0,10">
                <Border.BorderBrush>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#AA60C8" Offset="0" />
                        <GradientStop Color="#D69ADE" Offset="0.25" />
                        <GradientStop Color="#EABDE6" Offset="0.5" />
                        <GradientStop Color="#FFDFEF" Offset="0.75" />
                    </LinearGradientBrush>
                </Border.BorderBrush>
                <Grid Margin="10,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="10"/>
                        <!-- Gap -->
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="20"/>
                            <!-- Gap -->
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="20"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="1">
                            <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush2}"/>
                        </Grid>

                        <Grid Grid.Row="3">
                            <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush1}"/>
                        </Grid>

                    </Grid>

                    <Grid Grid.Column="2">
                        <Border Grid.Row="0" BorderThickness="2" CornerRadius="5">
                            <views:DailyIdleChartControl HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
                        </Border>
                    </Grid>


                </Grid>
            </Border>
            <!-- Monthly Idle Time -->
            <Border Grid.Column="2" BorderThickness="2" CornerRadius="8" Padding="0,0,0,10">
                <Border.BorderBrush>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#EB5A3C" Offset="0" />
                        <GradientStop Color="#DF9755" Offset="0.25" />
                        <GradientStop Color="#E7D283" Offset="0.5" />
                        <GradientStop Color="#EDF4C2" Offset="0.75" />
                    </LinearGradientBrush>
                </Border.BorderBrush>
                <Grid Margin="10,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="10"/>
                        <!-- Gap -->
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="20"/>
                            <!-- Gap -->
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="20"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="1">
                            <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush2}"/>
                        </Grid>

                        <Grid Grid.Row="3">
                            <Rectangle Width="10" Height="120" Fill="{StaticResource LoopGradientBrush1}"/>
                        </Grid>

                    </Grid>

                    <Grid Grid.Column="2">
                        <Border Grid.Row="0" BorderThickness="2" CornerRadius="5">
                            <views:MonthlyIdleChartControl HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
                        </Border>
                    </Grid>
                </Grid>
            </Border>
            <!-- System status -->
            <Border Grid.Column="4" BorderThickness="2" CornerRadius="8" Padding="0,0,0,10">
                <Border.BorderBrush>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#FF8595EF" Offset="0" />
                        <GradientStop Color="#FF58FDDB" Offset="0.25" />
                        <GradientStop Color="#FFFDD644" Offset="0.5" />
                        <GradientStop Color="#FFA4D7F7" Offset="0.75" />
                    </LinearGradientBrush>
                </Border.BorderBrush>
                <StackPanel VerticalAlignment="Center">
                    <!-- Trạng thái chạy/dừng hệ thống -->
                    <TextBlock FontSize="30" Margin="10,0,0,0" Foreground="{StaticResource SurfaceBrush}" HorizontalAlignment="Center">
                        <Run Text="Trạng thái: "/>
                        <Run Text="{Binding SystemStatus}" Foreground="#FF6B6B"/>
                    </TextBlock>

                    <!-- Tổng sản phẩm OK -->
                    <TextBlock FontSize="30" VerticalAlignment="Center" Margin="10,0,0,0" Foreground="{StaticResource SurfaceBrush}" HorizontalAlignment="Center">
                        <Run Text="OK: "/>
                        <Run Text="{Binding SystemTotalOK}" Foreground="#4ECDC4"/>
                    </TextBlock>

                    <!-- Tổng sản phẩm NG -->
                    <TextBlock FontSize="30" VerticalAlignment="Center" Margin="10,0,0,0" Foreground="{StaticResource SurfaceBrush}" HorizontalAlignment="Center">
                        <Run Text="NG: "/>
                        <Run Text="{Binding SystemTotalNG}" Foreground="#FF6B6B"/>
                    </TextBlock>
                </StackPanel>
            </Border>
        </Grid>

        <!-- Bottom Area: Daily Plan + Div3/Div4 -->
        <Grid Grid.Row="2" Margin="10,10,10,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="3*"/>
                <!-- 3/10 -->
                <ColumnDefinition Width="5"/>
                <!-- Gap -->
                <ColumnDefinition Width="3*"/>
                <!-- 3/10 -->
                <ColumnDefinition Width="5"/>
                <!-- Gap -->
                <ColumnDefinition Width="4*"/>
                <!-- 4/10 -->
            </Grid.ColumnDefinitions>

            <!-- Div3 và Div4 Section -->
            <Grid Grid.Column="0">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="5"/>
                    <!-- Gap -->
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Panel 3 (div3) - Left: Daily Plan vs Actual -->
                <Border Grid.Row="0" BorderBrush="#00B4D8" BorderThickness="2" CornerRadius="8">
                    <views:DailyPlanActualChartControl x:Name="DailyPlanChart"
                                                       HorizontalAlignment="Stretch"
                                                       VerticalAlignment="Stretch"/>
                </Border>

                <!-- Panel 4 (div4) - Shift Plan vs Actual -->
                <Border Grid.Row="2" BorderBrush="#2C98A0" BorderThickness="2" CornerRadius="8">
                    <views:ShiftPlanActualChartControl HorizontalAlignment="Stretch" VerticalAlignment="Stretch"/>
                </Border>
            </Grid>
            <Grid Grid.Column="2">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="5"/>
                    <!-- Gap -->
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Panel 3 (div3) - Right: Monthly Idle Hours -->
                <Border Grid.Row="0" BorderBrush="#FFAFCC" BorderThickness="2" CornerRadius="8">
                    <views:DailyQualityChartControl VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                </Border>

                <!-- Panel 4 (div4) - Shift Quality Chart -->
                <Border Grid.Row="2" BorderBrush="#FFC8D0" BorderThickness="2" CornerRadius="8">
                    <views:ShiftQualityChartControl VerticalAlignment="Stretch" HorizontalAlignment="Stretch"/>
                </Border>
            </Grid>
            <!-- Daily Plan Section -->
            <Grid Grid.Column="4" x:Name="DailyPlanSection">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="5"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Border CornerRadius="8" Grid.Column="0" BorderThickness="2" BorderBrush="#FA576B">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <Grid>
                            <views:StopTimesChartControl
                                x:Name="StopTimesChartControl"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch"/>
                        </Grid>
                    </ScrollViewer>
                </Border>

                <!-- Kế hoạch model hôm nay -->
                <Border Padding="4,4,4,0" Grid.Row="2" CornerRadius="8" BorderThickness="2" BorderBrush="#CAF0F8">
                    <Grid Grid.Row="2" Margin="0,0,0,10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <!-- Title -->
                            <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                                <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M12,6A6,6 0 0,0 6,12A6,6 0 0,0 12,18A6,6 0 0,0 18,12A6,6 0 0,0 12,6M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8Z"
                                      Fill="#3498DB" Width="auto" Height="auto" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="Kế hoạch" FontSize="16" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding TodayDateText}" FontSize="16" Foreground="{StaticResource SurfaceBrush}" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Current Product Display -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock VerticalAlignment="Center" Text="Model hiện tại" FontSize="14" FontWeight="Bold" Foreground="{StaticResource SurfaceBrush}" Margin="0,0,10,0"/>

                                <!-- Read-only display when not editing -->
                                <Border Background="#ECF0F1" CornerRadius="4" Visibility="{Binding DataContext.IsEditingModel, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding DataContext.CurrentProductText, RelativeSource={RelativeSource AncestorType=Window}}" FontSize="16" FontWeight="Bold" Foreground="#E74C3C" VerticalAlignment="Center" Margin="10,0,0,0"/>
                                        <Button Margin="8,0,0,0" Width="auto" Height="auto" Background="Transparent" BorderThickness="0" Foreground="Black"
                                                Command="{Binding DataContext.StartEditModelCommand, RelativeSource={RelativeSource AncestorType=Window}}" ToolTip="Chỉnh sửa model">
                                            <Path Data="M20.71,7.04C21.1,6.65 21.1,6 20.71,5.63L18.37,3.29C18,2.9 17.35,2.9 16.96,3.29L15.12,5.12L18.87,8.87M3,17.25V21H6.75L17.81,9.93L14.06,6.18L3,17.25Z"
                                                  Fill="#3498DB" Stretch="Uniform" Width="auto" Height="auto"/>
                                        </Button>
                                    </StackPanel>
                                </Border>

                                <!-- Editable input when editing -->
                                <StackPanel Orientation="Horizontal" Visibility="{Binding DataContext.IsEditingModel, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBox Text="{Binding DataContext.EditableModelName, RelativeSource={RelativeSource AncestorType=Window}, UpdateSourceTrigger=PropertyChanged}"
                                             FontSize="16" FontWeight="Bold" Foreground="#E74C3C"
                                             Background="#ECF0F1" BorderThickness="1" BorderBrush="#3498DB"
                                             Padding="8,4" MinWidth="150" VerticalAlignment="Center">
                                        <TextBox.InputBindings>
                                            <KeyBinding Key="Enter" Command="{Binding DataContext.SaveModelCommand, RelativeSource={RelativeSource AncestorType=Window}}"/>
                                            <KeyBinding Key="Escape" Command="{Binding DataContext.CancelEditModelCommand, RelativeSource={RelativeSource AncestorType=Window}}"/>
                                        </TextBox.InputBindings>
                                    </TextBox>
                                    <Button Margin="4,0,0,0" Width="auto" Height="auto" Background="#27AE60" BorderThickness="0"
                                            Command="{Binding DataContext.SaveModelCommand, RelativeSource={RelativeSource AncestorType=Window}}" ToolTip="Lưu (Enter)">
                                        <Path Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                              Fill="White" Stretch="Uniform" Width="12" Height="12"/>
                                    </Button>
                                    <Button Margin="4,0,0,0" Width="auto" Height="auto" Background="#E74C3C" BorderThickness="0"
                                            Command="{Binding DataContext.CancelEditModelCommand, RelativeSource={RelativeSource AncestorType=Window}}" ToolTip="Hủy (Escape)">
                                        <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                                              Fill="White" Stretch="Uniform" Width="12" Height="12"/>
                                    </Button>
                                </StackPanel>
                            </StackPanel>

                            <!-- Control Buttons -->
                            <StackPanel Grid.Column="4" Orientation="Horizontal" HorizontalAlignment="Right">
                                <Button x:Name="RefreshPlanButton" Width="35" Height="35" Margin="5,0"
                                        Background="#3498DB" BorderThickness="0" ToolTip="Cập nhật kế hoạch"
                                        Style="{StaticResource RoundButtonStyle}"
                                        Command="{Binding RefreshCommand}"
                                        Click="RefreshPlanButton_Click">
                                    <Path Data="M17.65,6.35C16.2,4.9 14.21,4 12,4c-4.42,0 -7.99,3.58 -7.99,8s3.57,8 7.99,8c3.73,0 6.84,-2.55 7.73,-6h-2.08c-0.82,2.33 -3.04,4 -5.65,4 -3.31,0 -6,-2.69 -6,-6s2.69,-6 6,-6c1.66,0 3.14,0.69 4.22,1.78L13,11h7V4L17.65,6.35z"
                                          Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                                </Button>

                                <Button x:Name="MarkCompleteButton" Width="35" Height="35" Margin="5,0"
                                        Background="#27AE60" BorderThickness="0" ToolTip="Hoàn thành sản phẩm hiện tại"
                                        Style="{StaticResource RoundButtonStyle}"
                                        Command="{Binding MarkCompleteCommand}"
                                        Click="MarkCompleteButton_Click">
                                    <Path Data="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"
                                          Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                                </Button>

                                <Button x:Name="StartNextButton" Width="35" Height="35" Margin="5,0"
                                        Background="#F39C12" BorderThickness="0" ToolTip="Bắt đầu sản phẩm được chọn"
                                        Style="{StaticResource RoundButtonStyle}"
                                        Command="{Binding StartNextCommand}"
                                        Click="StartNextButton_Click">
                                    <Path Data="M8,5.14V19.14L19,12.14L8,5.14Z"
                                          Fill="White" Stretch="Uniform" Width="18" Height="18"/>
                                </Button>
                            </StackPanel>
                            <!-- DataGrid for Daily Plan -->
                        </Grid>
                        <Border Grid.Row="1" BorderBrush="Gray" BorderThickness="1" CornerRadius="10" Padding="2" Background="{StaticResource BorderBrush}">
                            <DataGrid x:Name="DailyPlanDataGrid"
                                      ColumnHeaderStyle="{StaticResource CenterHeaderStyle}"
                                      ItemsSource="{Binding DailyPlanItems}"
                                      SelectedItem="{Binding SelectedItem, Mode=TwoWay}"
                                      AutoGenerateColumns="False"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      CanUserReorderColumns="False"
                                      CanUserResizeRows="False"
                                      IsReadOnly="True"
                                      SelectionMode="Single"
                                      GridLinesVisibility="All"
                                      HeadersVisibility="Column"
                                      RowHeaderWidth="0"
                                      FontSize="14"
                                      MinHeight="200"
                                      HorizontalScrollBarVisibility="Auto"
                                      VerticalScrollBarVisibility="Auto"
                                      BorderThickness="0"
                                      Background="{x:Null}">

                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Background" Value="{Binding Converter={StaticResource DailyPlanItemHighlightConverter}}"/>
                                        <Setter Property="Height" Value="40"/>
                                        <Style.Triggers>
                                            <!-- Prevent selection for completed and in-progress items -->
                                            <DataTrigger Binding="{Binding Status}" Value="Completed">
                                                <Setter Property="IsEnabled" Value="False"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Status}" Value="InProgress">
                                                <Setter Property="IsEnabled" Value="False"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>

                                <DataGrid.Columns>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="No" Binding="{Binding No}" Width="*" MinWidth="40"/>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Type" Binding="{Binding Type}" Width="*" MinWidth="60"/>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Model name" Binding="{Binding ModelName}" Width="*" MinWidth="120"/>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Market" Binding="{Binding Market}" Width="*" MinWidth="80"/>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Q'ty" Binding="{Binding Quantity}" Width="*" MinWidth="50"/>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Start time" Binding="{Binding StartTime}" Width="*" MinWidth="80"/>
                                    <DataGridTextColumn ElementStyle="{StaticResource CenterTextBlockStyle}" Header="Stop time" Binding="{Binding StopTime}" Width="*" MinWidth="80"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Border>

                    </Grid>
                </Border>
            </Grid>
        </Grid>
    </Grid>
</UserControl>
