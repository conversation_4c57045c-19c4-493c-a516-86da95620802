﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\sky.png" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.105.0" />
    <PackageReference Include="HslCommunication" Version="12.3.1" />
    <PackageReference Include="LiveCharts.NetCore" Version="0.9.8" />
    <PackageReference Include="LiveChartsCore.SkiaSharpView.WPF" Version="2.0.0-rc2" />
    <PackageReference Include="Microsoft.Data.Sqlite" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.5" />
    <PackageReference Include="PCSC" Version="7.0.1" />
    <PackageReference Include="PCSC.Iso7816" Version="7.0.1" />
    <PackageReference Include="WPF-UI" Version="4.0.3" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="ActUtlTypeLib">
      <HintPath>D:\Melservo\ACT\Control\ActUtlTypeLib.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Content Include="Resources\sky.png">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Config\fault_mapping.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Data\panaDB.db">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="plc_configs.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="plc_map.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="Resources\flowers.png">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
