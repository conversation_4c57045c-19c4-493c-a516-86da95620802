{"ExcelSettings": {"PlanFilePath": "C:\\Project-Dat\\PANA\\weeklyPlans.xlsx", "MaintenancePlanPath": "C:\\Project-Dat\\PANA\\maintenancePlans.xlsx", "IdlePlansFilePath": "C:\\Project-Dat\\PANA\\idleplans.xlsx"}, "PlcSettings": {"Mode": "Real", "Description": "PLC operating mode: 'Real' for production, 'Mock' for testing/demo", "MockDataUpdateInterval": 10000, "EnableRandomVariation": true, "AutoConnectOnStartup": true}, "DashboardSettings": {"RefreshInterval": 5000, "EnableRealTimeUpdates": true, "ChartAnimationDuration": 500}, "LayoutSettings": {"DefaultLayout": "Mainline", "Description": "Layout configuration: 'Mainline' for production line, 'Inspection' for inspection line"}, "SimulationSettings": {"ConveyorMovementInterval": 5000, "ProductEntryInterval": 7000, "ConveyorTransferTime": 1000, "Description": "Movement simulation settings: ConveyorMovementInterval (ms) for product movement, ProductEntryInterval (ms) for new product entry, ConveyorTransferTime (ms) for transfer between stations"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConsoleLogging": {"Enabled": true, "Description": "Set to false to disable console logging in production"}}