namespace ZoomableApp.Services
{
    /// <summary>
    /// Interface cho dịch vụ đọc RFID.
    /// </summary>
    public interface IRfidReaderService : IDisposable
    {
        /// <summary>
        /// Sự kiện kích hoạt khi quét được thẻ RFID. Trả về mã thẻ.
        /// </summary>
        event Action<string> CardScanned;

        /// <summary>
        /// Bắt đầu lắng nghe sự kiện quét thẻ.
        /// </summary>
        Task StartListeningAsync();

        /// <summary>
        /// Dừng lắng nghe và giải phóng tài nguyên.
        /// </summary>
        void StopListening();
    }
}
