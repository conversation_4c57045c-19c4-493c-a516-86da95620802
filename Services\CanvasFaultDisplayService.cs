using System.Windows;
using System.Windows.Controls;
using ZoomableApp.Models;
using ZoomableApp.SharedControls;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Service for managing fault display on canvas layouts
    /// </summary>
    public class CanvasFaultDisplayService
    {
        private readonly Dictionary<string, FaultDisplayControl> _activeFaultControls;
        private readonly ILoggerService? _logger;
        private Canvas? _mainlineCanvas;
        private Canvas? _inspectionCanvas;

        public CanvasFaultDisplayService()
        {
            _activeFaultControls = new Dictionary<string, FaultDisplayControl>();

            // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
            if (ServiceContainer.IsRegistered<ILoggerService>())
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
            }
        }

        /// <summary>
        /// Set the canvas references for mainline and inspection layouts
        /// </summary>
        public void SetCanvasReferences(Canvas? mainlineCanvas, Canvas? inspectionCanvas)
        {
            _mainlineCanvas = mainlineCanvas;
            _inspectionCanvas = inspectionCanvas;
        }

        /// <summary>
        /// Display fault on appropriate canvas based on system type
        /// </summary>
        public void DisplayFault(PlcFaultInfo faultInfo, string systemType)
        {
            if (faultInfo == null || !faultInfo.IsActive)
            {
                ClearFault(faultInfo?.FaultCode ?? "", systemType);
                return;
            }

            Canvas? targetCanvas = systemType.ToLower() switch
            {
                "mainline" => _mainlineCanvas,
                "inspection" => _inspectionCanvas,
                _ => null
            };

            if (targetCanvas == null)
            {
                _logger?.LogWarning($"Canvas not available for system: {systemType}");
                return;
            }

            string faultKey = $"{systemType}_{faultInfo.FaultCode}";

            // Remove existing fault display if any
            if (_activeFaultControls.TryGetValue(faultKey, out var existingControl))
            {
                targetCanvas.Children.Remove(existingControl);
                _activeFaultControls.Remove(faultKey);
            }

            // Create new fault display control
            var faultControl = new FaultDisplayControl
            {
                FaultInfo = faultInfo
            };

            // Position the fault display based on affected stations
            PositionFaultControl(faultControl, faultInfo, targetCanvas);

            // Add to canvas and track
            targetCanvas.Children.Add(faultControl);
            _activeFaultControls[faultKey] = faultControl;

            Console.WriteLine($"Displayed fault {faultInfo.FaultCode} on {systemType} canvas at stations: {string.Join(", ", faultInfo.Stations)}");
        }

        /// <summary>
        /// Clear fault display from canvas
        /// </summary>
        public void ClearFault(string faultCode, string systemType)
        {
            string faultKey = $"{systemType}_{faultCode}";

            if (_activeFaultControls.TryGetValue(faultKey, out var faultControl))
            {
                Canvas? targetCanvas = systemType.ToLower() switch
                {
                    "mainline" => _mainlineCanvas,
                    "inspection" => _inspectionCanvas,
                    _ => null
                };

                targetCanvas?.Children.Remove(faultControl);
                _activeFaultControls.Remove(faultKey);

                Console.WriteLine($"Cleared fault {faultCode} from {systemType} canvas");
            }
        }

        /// <summary>
        /// Clear all fault displays from both canvases
        /// </summary>
        public void ClearAllFaults()
        {
            foreach (var kvp in _activeFaultControls.ToList())
            {
                var systemType = kvp.Key.Split('_')[0];
                Canvas? targetCanvas = systemType.ToLower() switch
                {
                    "mainline" => _mainlineCanvas,
                    "inspection" => _inspectionCanvas,
                    _ => null
                };

                targetCanvas?.Children.Remove(kvp.Value);
            }

            _activeFaultControls.Clear();
            Console.WriteLine("Cleared all fault displays");
        }

        /// <summary>
        /// Position fault control based on affected stations
        /// </summary>
        private void PositionFaultControl(FaultDisplayControl faultControl, PlcFaultInfo faultInfo, Canvas canvas)
        {
            if (faultInfo.Stations.Count == 0)
            {
                // Default position if no stations specified
                faultControl.SetCanvasPosition(50, 10);
                return;
            }

            // Calculate position based on first affected station
            // This is a simplified positioning - in real implementation, 
            // you would get actual station positions from the layout
            int firstStation = faultInfo.Stations.First();
            double stationWidth = 120; // Approximate station width
            double stationSpacing = 140; // Approximate spacing between stations
            
            double x = (firstStation - 1) * stationSpacing + 20;
            double y = 10; // Top of canvas

            faultControl.SetCanvasPosition(x, y);
        }

        /// <summary>
        /// Update canvas references when layout changes
        /// </summary>
        public void OnLayoutChanged(string layoutName, Canvas? layoutCanvas)
        {
            switch (layoutName.ToLower())
            {
                case "mainline":
                    _mainlineCanvas = layoutCanvas;
                    break;
                case "inspection":
                    _inspectionCanvas = layoutCanvas;
                    break;
            }

            Console.WriteLine($"Updated canvas reference for {layoutName}");
        }
    }
}
