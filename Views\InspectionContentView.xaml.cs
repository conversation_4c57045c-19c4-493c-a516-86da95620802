using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ZoomableApp.Services;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Views
{
    public partial class InspectionContentView : UserControl
    {
        private bool _isDragging = false;
        private Point _lastMousePosition;
        private MainWindow _mainWindow;
        private InspectionViewModel _viewModel;
        private ILoggerService _logger;

        public InspectionContentView()
        {
            InitializeComponent();
            
            try
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger?.LogInfo("[InspectionContentView] Initializing Inspection Content View");

                // Initialize ViewModel
                _viewModel = new InspectionViewModel();
                DataContext = _viewModel;

                // Ensure child controls maintain their own DataContext
                this.Loaded += InspectionContentView_Loaded;

                // Get the MainWindow reference for button events
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    _mainWindow = mainWindow;
                    _logger?.LogInfo("[InspectionContentView] MainWindow reference set successfully");
                }
                else
                {
                    _logger?.LogWarning("[InspectionContentView] Could not get MainWindow reference");
                }

                // Load the Inspection layout into the canvas
                LoadInspectionLayout();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[InspectionContentView] Error initializing: {ex.Message}");
            }
        }

        #region Zoom/Pan Event Handlers

        private void ViewportBorder_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            try
            {
                var delta = e.Delta > 0 ? 1.1 : 0.9;
                ViewScaleTransform.ScaleX *= delta;
                ViewScaleTransform.ScaleY *= delta;

                // Limit zoom levels
                if (ViewScaleTransform.ScaleX < 0.1)
                {
                    ViewScaleTransform.ScaleX = 0.1;
                    ViewScaleTransform.ScaleY = 0.1;
                }
                else if (ViewScaleTransform.ScaleX > 5.0)
                {
                    ViewScaleTransform.ScaleX = 5.0;
                    ViewScaleTransform.ScaleY = 5.0;
                }

                _logger?.LogDebug($"[InspectionContentView] Zoom level: {ViewScaleTransform.ScaleX:F2}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error in mouse wheel: {ex.Message}");
            }
        }

        private void ViewportBorder_MouseDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    _isDragging = true;
                    _lastMousePosition = e.GetPosition(ViewportBorder);
                    ViewportBorder.CaptureMouse();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error in mouse down: {ex.Message}");
            }
        }

        private void ViewportBorder_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                if (_isDragging && e.LeftButton == MouseButtonState.Pressed)
                {
                    var currentPosition = e.GetPosition(ViewportBorder);
                    var deltaX = currentPosition.X - _lastMousePosition.X;
                    var deltaY = currentPosition.Y - _lastMousePosition.Y;

                    ViewTranslateTransform.X += deltaX;
                    ViewTranslateTransform.Y += deltaY;

                    _lastMousePosition = currentPosition;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error in mouse move: {ex.Message}");
            }
        }

        private void ViewportBorder_MouseUp(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (_isDragging)
                {
                    _isDragging = false;
                    ViewportBorder.ReleaseMouseCapture();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error in mouse up: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        private void InspectionContentView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Ensure DailyPlanChart maintains its own DataContext
                // This prevents inheritance from parent DataContext
                if (DailyPlanChart != null)
                {
                    Console.WriteLine("[InspectionContentView] Ensuring DailyPlanChart DataContext is properly set");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error in Loaded event: {ex.Message}");
            }
        }

        #endregion

        #region Button Event Handlers

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ViewScaleTransform.ScaleX = 0.5;
                ViewScaleTransform.ScaleY = 0.5;
                ViewTranslateTransform.X = 0;
                ViewTranslateTransform.Y = 0;
                _logger?.LogInfo("[InspectionContentView] View reset to default");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error resetting view: {ex.Message}");
            }
        }

        private void RefreshPlanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delegate to MainWindow's button handler
                //_mainWindow?.RefreshPlanButton_Click(sender, e);
                _logger?.LogInfo("[InspectionContentView] Plan refresh requested");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error refreshing plan: {ex.Message}");
            }
        }

        private void MarkCompleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delegate to MainWindow's button handler
                //_mainWindow?.MarkCompleteButton_Click(sender, e);
                _logger?.LogInfo("[InspectionContentView] Mark complete requested");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error marking complete: {ex.Message}");
            }
        }

        private void StartNextButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delegate to MainWindow's button handler
                //_mainWindow?.StartNextButton_Click(sender, e);
                _logger?.LogInfo("[InspectionContentView] Start next requested");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error starting next: {ex.Message}");
            }
        }

        #endregion

        #region Layout Loading

        private void LoadInspectionLayout()
        {
            try
            {
                // Clear existing content
                ZoomPanCanvas.Children.Clear();

                // Load Inspection layout - same as MainWindow LoadLayout("Inspection")
                var inspectionLayout = new Layouts.InspectionLayout();

                // Add to canvas
                ZoomPanCanvas.Children.Add(inspectionLayout);

                // Position the layout
                Canvas.SetLeft(inspectionLayout, 50);
                Canvas.SetTop(inspectionLayout, 50);

                _logger?.LogInfo("[InspectionContentView] Inspection layout loaded successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionContentView] Error loading Inspection layout: {ex.Message}", ex);
                Console.WriteLine($"[InspectionContentView] Error loading Inspection layout: {ex.Message}");
            }
        }

        #endregion
    }
}
