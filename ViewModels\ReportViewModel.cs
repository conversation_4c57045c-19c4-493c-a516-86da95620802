using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZoomableApp.Models;
using ZoomableApp.Services;
using ZoomableApp.PLC;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho trang báo cáo - uses DatabaseService for all database operations
    /// </summary>
    public class ReportViewModel : INotifyPropertyChanged
    {
        private readonly IDatabaseService _databaseService;
        private readonly CsvExportService _csvExportService;
        private readonly ReportExcelService _reportExcelService;
        private ObservableCollection<ProductionData> _reportData;
        private DateTime _fromDate;
        private DateTime _toDate;
        private string _selectedWorkShift;
        private string _selectedReportType;
        private string _statusMessage;
        private bool _isLoading;

        public ReportViewModel(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
            _csvExportService = new CsvExportService();
            _reportExcelService = new ReportExcelService();

            Console.WriteLine($"[ReportVM] Constructor - DatabaseService: {(_databaseService != null ? "Available" : "NULL")}");

            InitializeViewModel();

            // Load dữ liệu mặc định (sản lượng ca hiện tại)
            _ = Task.Run(LoadCurrentShiftProductionAsync);
        }

        // Constructor cho trường hợp lỗi config
        public ReportViewModel()
        {
            _databaseService = null;
            _csvExportService = new CsvExportService();
            _reportExcelService = new ReportExcelService();

            InitializeViewModel();

            // Hiển thị lỗi config
            ShowConfigError();
        }

        private void InitializeViewModel()
        {
            // Khởi tạo dữ liệu mặc định
            _reportData = new ObservableCollection<ProductionData>();
            _fromDate = DateTime.Today;
            _toDate = DateTime.Today;
            _selectedWorkShift = "Tất cả";
            _selectedReportType = "Production";
            _statusMessage = "Sẵn sàng";

            // Khởi tạo commands
            ProductionCommand = new RelayCommand(async () => await LoadProductionReportAsync());
            SlowOperationCommand = new RelayCommand(async () => await LoadSlowOperationAsync()); // Load from PLC and save
            MeasureOperationCommand = new RelayCommand(async () => await LoadMeasureOperationAsync()); // Load from PLC and save
            MonthlyReportCommand = new RelayCommand(async () => await LoadMonthlyReportAsync());
            ErrorHistoryCommand = new RelayCommand(async () => await LoadErrorHistoryReportAsync());
            SearchCommand = new RelayCommand(async () => await SearchReportDataAsync()); // Search based on SelectedReportType
            ExportExcelCommand = new RelayCommand(async () => await ExportToExcelAsync());
        }

        private void ShowConfigError()
        {
            StatusMessage = "Lỗi config file";

            // Thêm một bản ghi lỗi vào DataGrid
            var errorRecord = new ProductionData
            {
                Id = 1,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                Station = "System",
                Product_OK = 0,
                Product_NG = 0,
                Product_Total = 0,
                Time_Complete = 0,
                Time_Delay = 0,
                Time_Stop = 0,
                Number_Stop = 0,
                Error_Code = "CONFIG_ERROR",
                Error_Text = "Lỗi config file - Không thể tải cấu hình PLC",
                WorkShift = "System",
                ReportType = "Error",
                CreatedBy = "System",
                Notes = "Vui lòng kiểm tra file cấu hình PLC"
            };

            ReportData.Add(errorRecord);
        }

        #region Properties

        public ObservableCollection<ProductionData> ReportData
        {
            get => _reportData;
            set
            {
                _reportData = value;
                OnPropertyChanged();
            }
        }

        public DateTime FromDate
        {
            get => _fromDate;
            set
            {
                _fromDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime ToDate
        {
            get => _toDate;
            set
            {
                _toDate = value;
                OnPropertyChanged();
            }
        }

        public string SelectedWorkShift
        {
            get => _selectedWorkShift;
            set
            {
                _selectedWorkShift = value;
                OnPropertyChanged();
            }
        }

        public string SelectedReportType
        {
            get => _selectedReportType;
            set
            {
                _selectedReportType = value;
                OnPropertyChanged();
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            set
            {
                _statusMessage = value;
                OnPropertyChanged();
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        public List<string> WorkShifts { get; } = new List<string>
        {
            "Tất cả",
            "Ca sáng (06:00-11:00)",
            "Ca chiều (14:00-22:00)",
            "Ca hành chính (08:00-17:00)",
            "Ca đêm (22:00-06:00)"
        };

        public List<string> ReportTypes { get; } = new List<string>
        {
            "Production",
            "SlowOperation",
            "MeasureOperation",
            "MonthlyReport",
            "ErrorHistory"
        };

        #endregion

        #region Commands

        public ICommand ProductionCommand { get; private set; }
        public ICommand SlowOperationCommand { get; private set; }
        public ICommand MeasureOperationCommand { get; private set; }
        public ICommand MonthlyReportCommand { get; private set; }
        public ICommand ErrorHistoryCommand { get; private set; }
        public ICommand SearchCommand { get; private set; }
        public ICommand ExportExcelCommand { get; private set; }

        #endregion

        #region Methods

        /// <summary>
        /// Load báo cáo sản lượng
        /// </summary>
        private async Task LoadProductionReportAsync()
        {
            if (_databaseService == null)
            {
                StatusMessage = "Database service not available";
                Console.WriteLine("[ReportVM] Database service is null");
                return;
            }

            try
            {
                IsLoading = true;
                StatusMessage = "Loading production report...";
                SelectedReportType = "Production";

                Console.WriteLine($"[ReportVM] Loading production data from {FromDate:yyyy-MM-dd} to {ToDate:yyyy-MM-dd}, shift: {SelectedWorkShift}");

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var data = await _databaseService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, "Production");

                Console.WriteLine($"[ReportVM] Retrieved {data.Count} production records");

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {data.Count} bản ghi sản lượng";
                Console.WriteLine($"[ReportVM] Production report loaded successfully: {data.Count} records");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
                Console.WriteLine($"[ReportVM] Error loading production report: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }



        /// <summary>
        /// Load current slow operation data from PLC and display immediately
        /// </summary>
        public async Task LoadSlowOperationAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang đọc dữ liệu thao tác chậm từ PLC...";
                SelectedReportType = "SlowOperation";

                // Clear existing data
                ReportData.Clear();

                // Check PLC mode
                var plcMode = ConfigurationService.GetPlcMode();
                if (plcMode == PlcMode.Mock)
                {
                    await LoadSlowOperationMockDataAsync();
                }
                else
                {
                    await LoadSlowOperationFromPlcAsync();
                }

                StatusMessage = $"Đã tải {ReportData.Count} bản ghi thao tác chậm từ PLC";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
                Console.WriteLine($"ReportViewModel: Error loading slow operation: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load slow operation data from PLC registers (Timedelayst1-26)
        /// </summary>
        private async Task LoadSlowOperationFromPlcAsync()
        {
            try
            {
                var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
                if (plcManager == null || !plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    Console.WriteLine("ReportViewModel: PLC not connected, using mock data");
                    await LoadSlowOperationMockDataAsync();
                    return;
                }

                var plcService = plcManager.GetPlcService("PLC_MainLine");
                if (plcService == null || !plcService.IsConnected)
                {
                    Console.WriteLine("ReportViewModel: PLC service not available, using mock data");
                    await LoadSlowOperationMockDataAsync();
                    return;
                }

                var currentTime = DateTime.Now;
                var currentShift = UserSession.CurrentShift?.Name ?? "Unknown";

                // Read time delay data from all 26 stations
                for (int station = 1; station <= 26; station++)
                {
                    try
                    {
                        // Get the corresponding PlcDeviceAddress for this station
                        var timeDelayAddress = GetTimeDelayAddress(station);
                        var result = await plcService.ReadAsync(timeDelayAddress);

                        if (result.IsSuccess)
                        {
                            var timeDelaySeconds = Convert.ToInt32(result.Value);

                            // Create production data for this station
                            var productionData = new ProductionData
                            {
                                Id = station, // Use station number as temporary ID for display
                                Timestamp = currentTime.ToString("yyyy-MM-dd HH:mm:ss"),
                                Station = $"ST{station:D2}",
                                Time_Delay = timeDelaySeconds, // Time delay in seconds from PLC
                                WorkShift = currentShift,
                                Notes = $"Slow operation at station {station}",
                                ReportType = "SlowOperation",
                                CreatedBy = UserSession.CurrentUser?.Username ?? "System",
                                // Other fields set to 0 as requested
                                Product_OK = 0,
                                Product_NG = 0,
                                Product_Total = 0,
                                Time_Complete = 0,
                                Time_Stop = 0,
                                Number_Stop = 0,
                                Error_Code = "",
                                Error_Text = ""
                            };

                            // Add to display immediately
                            ReportData.Add(productionData);

                            // Save to database
                            await _databaseService.SaveProductionDataAsync(productionData);

                            Console.WriteLine($"ReportViewModel: Station {station} - Time delay: {timeDelaySeconds}s");
                        }
                        else
                        {
                            Console.WriteLine($"ReportViewModel: Failed to read time delay for station {station}: {result.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"ReportViewModel: Error reading station {station}: {ex.Message}");
                    }
                }

                Console.WriteLine($"ReportViewModel: Loaded slow operation data for 26 stations");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ReportViewModel: Error in LoadSlowOperationFromPlcAsync: {ex.Message}");
                await LoadSlowOperationMockDataAsync();
            }
        }

        /// <summary>
        /// Load mock slow operation data for testing
        /// </summary>
        private async Task LoadSlowOperationMockDataAsync()
        {
            try
            {
                await Task.Delay(500); // Simulate PLC read delay

                var currentTime = DateTime.Now;
                var currentShift = UserSession.CurrentShift?.Name ?? "Morning";
                var random = new Random();

                // Generate mock data for all 26 stations
                for (int station = 1; station <= 26; station++)
                {
                    var timeDelaySeconds = random.Next(5, 60); // Random delay between 5-60 seconds

                    var productionData = new ProductionData
                    {
                        Id = station, // Use station number as temporary ID for display
                        Timestamp = currentTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        Station = $"ST{station:D2}",
                        Time_Delay = timeDelaySeconds,
                        WorkShift = currentShift,
                        Notes = $"Mock slow operation at station {station}",
                        ReportType = "SlowOperation",
                        CreatedBy = UserSession.CurrentUser?.Username ?? "MockSystem",
                        // Other fields set to 0 as requested
                        Product_OK = 0,
                        Product_NG = 0,
                        Product_Total = 0,
                        Time_Complete = 0,
                        Time_Stop = 0,
                        Number_Stop = 0,
                        Error_Code = "",
                        Error_Text = ""
                    };

                    // Add to display immediately
                    ReportData.Add(productionData);

                    // Save to database
                    await _databaseService.SaveProductionDataAsync(productionData);
                }

                Console.WriteLine("ReportViewModel: Loaded mock slow operation data for 26 stations");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ReportViewModel: Error in LoadSlowOperationMockDataAsync: {ex.Message}");
            }
        }

        /// <summary>
        /// Get the PlcDeviceAddress for time delay register of a specific station
        /// </summary>
        private PlcDeviceAddress GetTimeDelayAddress(int station)
        {
            return station switch
            {
                1 => PlcDeviceAddress.Timedelayst1,
                2 => PlcDeviceAddress.Timedelayst2,
                3 => PlcDeviceAddress.Timedelayst3,
                4 => PlcDeviceAddress.Timedelayst4,
                5 => PlcDeviceAddress.Timedelayst5,
                6 => PlcDeviceAddress.Timedelayst6,
                7 => PlcDeviceAddress.Timedelayst7,
                8 => PlcDeviceAddress.Timedelayst8,
                9 => PlcDeviceAddress.Timedelayst9,
                10 => PlcDeviceAddress.Timedelayst10,
                11 => PlcDeviceAddress.Timedelayst11,
                12 => PlcDeviceAddress.Timedelayst12,
                13 => PlcDeviceAddress.Timedelayst13,
                14 => PlcDeviceAddress.Timedelayst14,
                15 => PlcDeviceAddress.Timedelayst15,
                16 => PlcDeviceAddress.Timedelayst16,
                17 => PlcDeviceAddress.Timedelayst17,
                18 => PlcDeviceAddress.Timedelayst18,
                19 => PlcDeviceAddress.Timedelayst19,
                20 => PlcDeviceAddress.Timedelayst20,
                21 => PlcDeviceAddress.Timedelayst21,
                22 => PlcDeviceAddress.Timedelayst22,
                23 => PlcDeviceAddress.Timedelayst23,
                24 => PlcDeviceAddress.Timedelayst24,
                25 => PlcDeviceAddress.Timedelayst25,
                26 => PlcDeviceAddress.Timedelayst26,
                _ => throw new ArgumentException($"Invalid station number: {station}. Valid range is 1-26.")
            };
        }

        /// <summary>
        /// Load current measure operation data from PLC and display immediately
        /// </summary>
        public async Task LoadMeasureOperationAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang đọc dữ liệu đo thao tác từ PLC...";
                SelectedReportType = "MeasureOperation";

                // Clear existing data
                ReportData.Clear();

                // Check PLC mode
                var plcMode = ConfigurationService.GetPlcMode();
                if (plcMode == PlcMode.Mock)
                {
                    await LoadMeasureOperationMockDataAsync();
                }
                else
                {
                    await LoadMeasureOperationFromPlcAsync();
                }

                StatusMessage = $"Đã tải {ReportData.Count} bản ghi đo thao tác từ PLC";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
                Console.WriteLine($"ReportViewModel: Error loading measure operation: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load measure operation data from PLC registers (Timecompletest1-26)
        /// </summary>
        private async Task LoadMeasureOperationFromPlcAsync()
        {
            try
            {
                var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
                if (plcManager == null || !plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    Console.WriteLine("ReportViewModel: PLC not connected, using mock data");
                    await LoadMeasureOperationMockDataAsync();
                    return;
                }

                var plcService = plcManager.GetPlcService("PLC_MainLine");
                if (plcService == null || !plcService.IsConnected)
                {
                    Console.WriteLine("ReportViewModel: PLC service not available, using mock data");
                    await LoadMeasureOperationMockDataAsync();
                    return;
                }

                var currentTime = DateTime.Now;
                var currentShift = UserSession.CurrentShift?.Name ?? "Unknown";

                // Read time completion data from all 26 stations
                for (int station = 1; station <= 26; station++)
                {
                    try
                    {
                        // Get the corresponding PlcDeviceAddress for this station
                        var timeCompleteAddress = GetTimeCompleteAddress(station);
                        var result = await plcService.ReadAsync(timeCompleteAddress);

                        if (result.IsSuccess)
                        {
                            var timeCompleteSeconds = Convert.ToSingle(result.Value);

                            // Create production data for this station
                            var productionData = new ProductionData
                            {
                                Id = station, // Use station number as temporary ID for display
                                Timestamp = currentTime.ToString("yyyy-MM-dd HH:mm:ss"),
                                Station = $"ST{station:D2}",
                                Time_Complete = timeCompleteSeconds, // Time in seconds from PLC
                                WorkShift = currentShift,
                                Notes = $"Measure operation at station {station}",
                                ReportType = "MeasureOperation",
                                CreatedBy = UserSession.CurrentUser?.Username ?? "System",
                                // Other fields set to 0 as requested
                                Product_OK = 0,
                                Product_NG = 0,
                                Product_Total = 0,
                                Time_Delay = 0,
                                Time_Stop = 0,
                                Number_Stop = 0,
                                Error_Code = "",
                                Error_Text = ""
                            };

                            // Add to display immediately
                            ReportData.Add(productionData);

                            // Save to database
                            await _databaseService.SaveProductionDataAsync(productionData);

                            Console.WriteLine($"ReportViewModel: Station {station} - Time complete: {timeCompleteSeconds}s");
                        }
                        else
                        {
                            Console.WriteLine($"ReportViewModel: Failed to read time complete for station {station}: {result.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"ReportViewModel: Error reading station {station}: {ex.Message}");
                    }
                }

                Console.WriteLine($"ReportViewModel: Loaded measure operation data for 26 stations");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ReportViewModel: Error in LoadMeasureOperationFromPlcAsync: {ex.Message}");
                await LoadMeasureOperationMockDataAsync();
            }
        }

        /// <summary>
        /// Load mock measure operation data for testing
        /// </summary>
        private async Task LoadMeasureOperationMockDataAsync()
        {
            try
            {
                await Task.Delay(500); // Simulate PLC read delay

                var currentTime = DateTime.Now;
                var currentShift = UserSession.CurrentShift?.Name ?? "Morning";
                var random = new Random();

                // Generate mock data for all 26 stations
                for (int station = 1; station <= 26; station++)
                {
                    var timeCompleteSeconds = random.Next(30, 120); // Random time between 30-120 seconds

                    var productionData = new ProductionData
                    {
                        Id = station, // Use station number as temporary ID for display
                        Timestamp = currentTime.ToString("yyyy-MM-dd HH:mm:ss"),
                        Station = $"ST{station:D2}",
                        Time_Complete = timeCompleteSeconds,
                        WorkShift = currentShift,
                        Notes = $"Mock measure operation at station {station}",
                        ReportType = "MeasureOperation",
                        CreatedBy = UserSession.CurrentUser?.Username ?? "MockSystem",
                        // Other fields set to 0 as requested
                        Product_OK = 0,
                        Product_NG = 0,
                        Product_Total = 0,
                        Time_Delay = 0,
                        Time_Stop = 0,
                        Number_Stop = 0,
                        Error_Code = "",
                        Error_Text = ""
                    };

                    // Add to display immediately
                    ReportData.Add(productionData);

                    // Save to database
                    await _databaseService.SaveProductionDataAsync(productionData);
                }

                Console.WriteLine("ReportViewModel: Loaded mock measure operation data for 26 stations");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ReportViewModel: Error in LoadMeasureOperationMockDataAsync: {ex.Message}");
            }
        }

        /// <summary>
        /// Get the PlcDeviceAddress for time complete register of a specific station
        /// </summary>
        private PlcDeviceAddress GetTimeCompleteAddress(int station)
        {
            return station switch
            {
                1 => PlcDeviceAddress.Timecompletest1,
                2 => PlcDeviceAddress.Timecompletest2,
                3 => PlcDeviceAddress.Timecompletest3,
                4 => PlcDeviceAddress.Timecompletest4,
                5 => PlcDeviceAddress.Timecompletest5,
                6 => PlcDeviceAddress.Timecompletest6,
                7 => PlcDeviceAddress.Timecompletest7,
                8 => PlcDeviceAddress.Timecompletest8,
                9 => PlcDeviceAddress.Timecompletest9,
                10 => PlcDeviceAddress.Timecompletest10,
                11 => PlcDeviceAddress.Timecompletest11,
                12 => PlcDeviceAddress.Timecompletest12,
                13 => PlcDeviceAddress.Timecompletest13,
                14 => PlcDeviceAddress.Timecompletest14,
                15 => PlcDeviceAddress.Timecompletest15,
                16 => PlcDeviceAddress.Timecompletest16,
                17 => PlcDeviceAddress.Timecompletest17,
                18 => PlcDeviceAddress.Timecompletest18,
                19 => PlcDeviceAddress.Timecompletest19,
                20 => PlcDeviceAddress.Timecompletest20,
                21 => PlcDeviceAddress.Timecompletest21,
                22 => PlcDeviceAddress.Timecompletest22,
                23 => PlcDeviceAddress.Timecompletest23,
                24 => PlcDeviceAddress.Timecompletest24,
                25 => PlcDeviceAddress.Timecompletest25,
                26 => PlcDeviceAddress.Timecompletest26,
                _ => throw new ArgumentException($"Invalid station number: {station}. Valid range is 1-26.")
            };
        }



        /// <summary>
        /// Load báo cáo tổng hợp tháng và fill vào Excel template
        /// </summary>
        private async Task LoadMonthlyReportAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải báo cáo tổng hợp tháng...";
                SelectedReportType = "MonthlyReport";

                // Lấy dữ liệu cả tháng
                var startOfMonth = new DateTime(FromDate.Year, FromDate.Month, 1);
                var endOfMonth = startOfMonth.AddMonths(1).AddDays(-1);

                var data = await _databaseService.GetProductionDataAsync(
                    startOfMonth, endOfMonth, null, "Production");

                // Filter out unwanted data types from production report
                // Exclude: delay hour, stop time, stop hour, error history, complete time
                var filteredData = data.Where(d =>
                    d.ReportType == "Production" &&
                    string.IsNullOrEmpty(d.Error_Code) && // Exclude error history
                    d.Time_Delay == 0 && // Exclude delay data
                    d.Time_Stop == 0 && // Exclude stop time data
                    d.Number_Stop == 0 // Exclude stop count data
                ).ToList();

                ReportData.Clear();
                foreach (var item in filteredData)
                    ReportData.Add(item);

                // Fill the MonthlyDataAnalytics Excel template
                if (_reportExcelService != null)
                {
                    try
                    {
                        string filePath = _reportExcelService.FillMonthlyDataAnalytics(filteredData, FromDate.Month, FromDate.Year);
                        StatusMessage = $"Đã tải {filteredData.Count} bản ghi tháng {FromDate.Month}/{FromDate.Year} và xuất ra file Excel: {filePath}";

                        // Open the Excel file
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = filePath,
                            UseShellExecute = true
                        });
                    }
                    catch (Exception exExcel)
                    {
                        StatusMessage = $"Đã tải {filteredData.Count} bản ghi tháng {FromDate.Month}/{FromDate.Year}, nhưng xuất Excel lỗi: {exExcel.Message}";
                    }
                }
                else
                {
                    StatusMessage = $"Đã tải {filteredData.Count} bản ghi tháng {FromDate.Month}/{FromDate.Year}, nhưng không thể xuất Excel (ReportExcelService không khả dụng)";
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load báo cáo lịch sử lỗi
        /// </summary>
        private async Task LoadErrorHistoryReportAsync()
        {
            if (_databaseService == null)
            {
                StatusMessage = "Database service not available";
                Console.WriteLine("[ReportVM] Database service is null for error history");
                return;
            }

            try
            {
                IsLoading = true;
                StatusMessage = "Đang tải lịch sử lỗi...";
                SelectedReportType = "ErrorHistory";

                Console.WriteLine($"[ReportVM] Loading error history from {FromDate:yyyy-MM-dd} to {ToDate:yyyy-MM-dd}, shift: {SelectedWorkShift}");

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                var data = await _databaseService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, "ErrorHistory");

                Console.WriteLine($"[ReportVM] Retrieved {data.Count} total records for error history");

                // Lọc chỉ những bản ghi có lỗi
                var errorData = data.Where(d => !string.IsNullOrEmpty(d.Error_Code)).ToList();

                Console.WriteLine($"[ReportVM] Filtered to {errorData.Count} error records");

                ReportData.Clear();
                foreach (var item in errorData)
                    ReportData.Add(item);

                StatusMessage = $"Đã tải {errorData.Count} bản ghi lỗi";
                Console.WriteLine($"[ReportVM] Error history loaded successfully: {errorData.Count} records");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi: {ex.Message}";
                Console.WriteLine($"[ReportVM] Error loading error history: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Tìm kiếm dữ liệu báo cáo
        /// </summary>
        private async Task SearchReportDataAsync()
        {
            if (_databaseService == null)
            {
                StatusMessage = "Database service not available";
                Console.WriteLine("[ReportVM] Database service is null for search");
                return;
            }

            try
            {
                IsLoading = true;
                StatusMessage = "Đang tìm kiếm...";

                Console.WriteLine($"[ReportVM] Search - FromDate: {FromDate:yyyy-MM-dd}, ToDate: {ToDate:yyyy-MM-dd}, WorkShift: {SelectedWorkShift}, ReportType: {SelectedReportType}");

                var workShift = SelectedWorkShift == "Tất cả" ? null : SelectedWorkShift;
                // Use the currently selected report type for filtering
                var reportType = SelectedReportType;

                // Special handling for ErrorHistory - filter by records with errors
                if (SelectedReportType == "ErrorHistory")
                {
                    reportType = null; // Get all records, then filter by error codes
                }

                var data = await _databaseService.GetProductionDataAsync(
                    FromDate, ToDate, workShift, reportType);

                Console.WriteLine($"[ReportVM] Search found {data.Count} records");

                // Apply additional filtering for ErrorHistory
                if (SelectedReportType == "ErrorHistory")
                {
                    data = data.Where(d => !string.IsNullOrEmpty(d.Error_Code)).ToList();
                    Console.WriteLine($"[ReportVM] After error filtering: {data.Count} records");
                }

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Tìm thấy {data.Count} bản ghi";
                Console.WriteLine($"[ReportVM] Search completed successfully: {data.Count} records");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi tìm kiếm: {ex.Message}";
                Console.WriteLine($"[ReportVM] Search error: {ex.Message}");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Xuất Excel
        /// </summary>
        private async Task ExportToExcelAsync()
        {
            try
            {
                IsLoading = true;
                StatusMessage = "Đang xuất Excel...";

                var data = ReportData.ToList();
                if (!data.Any())
                {
                    StatusMessage = "Không có dữ liệu để xuất";
                    return;
                }

                string filePath = "";
                
                switch (SelectedReportType)
                {
                    case "Production":
                        filePath = _csvExportService.ExportProductionReport(data, FromDate, ToDate);
                        break;
                    case "SlowOperation":
                        filePath = _csvExportService.ExportSlowOperationReport(data);
                        break;
                    case "MeasureOperation":
                        filePath = _csvExportService.ExportMeasureOperationReport(data);
                        break;
                    case "MonthlyReport":
                        filePath = _csvExportService.ExportMonthlyReport(data, FromDate.Month, FromDate.Year);
                        break;
                    case "ErrorHistory":
                        filePath = _csvExportService.ExportErrorHistoryReport(data);
                        break;
                    default:
                        filePath = _csvExportService.ExportProductionReport(data, FromDate, ToDate);
                        break;
                }

                StatusMessage = $"Đã xuất file: {System.IO.Path.GetFileName(filePath)}";
                
                // Mở thư mục chứa file
                System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{filePath}\"");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi xuất Excel: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Load sản lượng ca hiện tại (mặc định)
        /// </summary>
        private async Task LoadCurrentShiftProductionAsync()
        {
            if (_databaseService == null)
            {
                // Database service not available
                return;
            }

            try
            {
                var currentShift = WorkShiftHelper.GetShiftName(WorkShiftHelper.GetCurrentShift());
                var data = await _databaseService.GetProductionDataAsync(
                    DateTime.Today, DateTime.Today, currentShift, "Production");

                ReportData.Clear();
                foreach (var item in data)
                    ReportData.Add(item);

                StatusMessage = $"Sản lượng {currentShift} - {data.Count} bản ghi";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Lỗi tải dữ liệu mặc định: {ex.Message}";
            }
        }

        #endregion

        #region INotifyPropertyChanged

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }


}
