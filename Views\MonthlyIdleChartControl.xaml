﻿<UserControl x:Class="ZoomableApp.Views.MonthlyIdleChartControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             xmlns:local="clr-namespace:ZoomableApp.Views">
    <Border CornerRadius="8" Padding="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10" HorizontalAlignment="Left">
                <TextBlock Text="📊 Thời gian dừng hàng tháng" FontSize="18" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="{Binding LastUpdated, StringFormat=' (Updated: {0})'}" FontSize="12" Foreground="#BDC3C7" VerticalAlignment="Bottom" Margin="5,0,0,2"/>
            </StackPanel>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Footer Info -->
                <StackPanel Grid.Column="0" Orientation="Vertical" HorizontalAlignment="Center" Margin="8,0,0,0" MinHeight="140" VerticalAlignment="Center">
                    <TextBlock Text="Đã dừng: " Foreground="White" FontSize="16" FontWeight="Bold" MinHeight="35"/>
                    <TextBlock Text="{Binding ActualIdleHours, StringFormat='{}{0:F0}h'}" Foreground="#E74C3C" FontSize="16" FontWeight="Bold" MinHeight="35"/>
                    <TextBlock Text="Còn lại: " Foreground="White" FontSize="16" FontWeight="Bold" MinHeight="35"/>
                    <TextBlock Text="{Binding PlanIdleHours, StringFormat='{}{0:F0}h'}" Foreground="#348F50" FontSize="16" FontWeight="Bold" MinHeight="35"/>
                </StackPanel>

                <!-- Chart -->
                <lvc:PieChart Grid.Column="1" x:Name="MonthlyIdleChart"
                              Width="Auto"
                              Height="Auto"
                              Series="{Binding Series}"
                              LegendPosition="Bottom"
                              InitialRotation="-225"
                              MaxAngle="270"
                              Background="Transparent">
                </lvc:PieChart>
            </Grid>
        </Grid>
    </Border>
</UserControl>
