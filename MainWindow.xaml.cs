using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices; // For CallerMemberName
using System.Windows.Threading;      // For DispatcherTimer
using ZoomableApp.Models;
using ZoomableApp.Layouts;
using ZoomableApp.SharedControls;
using ZoomableApp.Services;
using ZoomableApp.PLC;
using ZoomableApp.ViewModels;
using System.Windows.Media.Animation;
using LiveChartsCore;
using SkiaSharp;
using LiveChartsCore.SkiaSharpView.Painting;
using System.IO;
using System.Linq;

namespace ZoomableApp
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private ToastNotificationPanel _toastNotification;
        private PlcConnectionManager _plcManager;
        private DataAggregatorService _dataAggregator;
        private IDatabaseService _databaseService;
        private ProductionDataReader _productionDataReader;
        private DispatcherTimer _plcDataReaderTimer; // Timer để đọc dữ liệu từ PLC định kỳ
        private DispatcherTimer _shiftChangeTimer; // Timer để kiểm tra chuyển ca
        private DispatcherTimer _hourlyDataSaveTimer; // Timer để lưu dữ liệu mỗi giờ
        private HomeDailyPlanViewModel _dailyPlanViewModel;
        private PlanViewModel _planViewModel;
        private ILoggerService _logger;
        private DashboardDataService _dashboardDataService;
        private DataSavingService _dataSavingService;
        private CanvasFaultDisplayService _canvasFaultDisplayService;

        // Dashboard Chart ViewModels (now handled by individual controls)
        private DateTime _lastShiftChangeCheck = DateTime.MinValue;

        public ObservableCollection<PlcConnectionUIData> PlcConnections { get; set; }

        // Instances of the layouts to manage event subscriptions and product transfer
        private MainlineLayout _mainlineLayoutInstance;
        private InspectionLayout _inspectionLayoutInstance;
        // A general reference to the currently loaded UserControl layout
        private UserControl _currentLoadedLayout;

        private ObservableCollection<ErrorNotification> _allErrors = new ObservableCollection<ErrorNotification>();
        public ObservableCollection<ErrorNotification> AllErrors
        {
            get => _allErrors;
            set { _allErrors = value; OnPropertyChanged(); }
        }
        private int _newErrorCount;
        public int NewErrorCount
        {
            get => _newErrorCount;
            set { _newErrorCount = value; OnPropertyChanged(); OnPropertyChanged(nameof(HasNewErrors)); }
        }

        public bool HasNewErrors => NewErrorCount > 0;

        private bool _isErrorListPopupOpen;
        public bool IsErrorListPopupOpen
        {
            get => _isErrorListPopupOpen;
            set { _isErrorListPopupOpen = value; OnPropertyChanged(); }
        }

        private bool _isSidebarOpen = false;
        public bool IsSidebarOpen
        {
            get => _isSidebarOpen;
            set { _isSidebarOpen = value; OnPropertyChanged(); }
        }

        // Properties for Daily Plan section bindings
        public string TodayDateText => _dailyPlanViewModel?.TodayDateText ?? "Đang tính...";
        public string CurrentProductText => _dailyPlanViewModel?.CurrentProductText ?? "Đang tải...";
        public ObservableCollection<ViewModels.DailyPlanItem> DailyPlanItems
        {
            get
            {
                Console.WriteLine("[MainWindow] UI is requesting DailyPlanItems. Returning HomeVM's collection.");
                return _dailyPlanViewModel?.DailyPlanItems;
            }
        }

        public object SelectedItem
        {
            get => _dailyPlanViewModel?.SelectedItem;
            set { if (_dailyPlanViewModel != null) _dailyPlanViewModel.SelectedItem = value as ViewModels.DailyPlanItem; }
        }

        // Commands for Daily Plan
        public ICommand RefreshCommand => _dailyPlanViewModel?.RefreshCommand;
        public ICommand MarkCompleteCommand => _dailyPlanViewModel?.MarkCompleteCommand;
        public ICommand StartNextCommand => _dailyPlanViewModel?.StartNextCommand;
        public ICommand StartEditModelCommand => _dailyPlanViewModel?.StartEditModelCommand;
        public ICommand SaveModelCommand => _dailyPlanViewModel?.SaveModelCommand;
        public ICommand CancelEditModelCommand => _dailyPlanViewModel?.CancelEditModelCommand;

        // Properties for Model Editing
        public string EditableModelName
        {
            get => _dailyPlanViewModel?.EditableModelName ?? "";
            set { if (_dailyPlanViewModel != null) _dailyPlanViewModel.EditableModelName = value; }
        }

        public bool IsEditingModel => _dailyPlanViewModel?.IsEditingModel ?? false;

        // Properties for PlanPage bindings
        public string ExcelFilePath => _planViewModel?.ExcelFilePath ?? "";
        public string ExcelFileStatus => _planViewModel?.ExcelFileStatus ?? "";
        public string ExcelRowCount => _planViewModel?.ExcelRowCount ?? "";
        public string LastUpdated => _planViewModel?.LastUpdated ?? "";
        public string PlanStatus => _planViewModel?.PlanStatus ?? "";
        public System.Data.DataTable MonthlyPlanData => _planViewModel?.MonthlyPlanData;
        public System.Data.DataTable DailyPlanData => _planViewModel?.DailyPlanData;
        public ObservableCollection<ISeries> Series { get; set; }
        public SolidColorPaint LegendPaint { get; } = new SolidColorPaint(SKColors.White);

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this; // Set DataContext for bindings to MainWindow properties

            Wpf.Ui.Appearance.ApplicationThemeManager.Apply(
              Wpf.Ui.Appearance.ApplicationTheme.Light, // Theme type
              Wpf.Ui.Controls.WindowBackdropType.Mica,  // Background type
              true                                      // Whether to change accents automatically
            );
            // Animation for the loop gradient brush
            var brush1 = (VisualBrush)Application.Current.Resources["LoopGradientBrush1"];
            var brush2 = (VisualBrush)Application.Current.Resources["LoopGradientBrush2"];
            var anim = new RectAnimation
            {
                From = new Rect(0, 0, 1, 1),
                To = new Rect(0, 1, 1, 1),
                Duration = new Duration(TimeSpan.FromSeconds(2)),
                RepeatBehavior = RepeatBehavior.Forever
            };
            brush1.BeginAnimation(VisualBrush.ViewportProperty, anim, HandoffBehavior.Compose);
            brush2.BeginAnimation(VisualBrush.ViewportProperty, anim, HandoffBehavior.Compose);

            // Update user info and menu visibility based on current session
            UpdateUserInfo();

            // Ensure sidebar is closed on startup
            IsSidebarOpen = false;
            SidebarPanel.Visibility = Visibility.Collapsed;
            SidebarColumn.Width = new GridLength(0, GridUnitType.Pixel);

            InitializeEssentialServices();

            // Load layout based on configuration
            var layoutSettings = ConfigurationService.GetLayoutSettings();
            var targetLayout = layoutSettings?.DefaultLayout ?? "Mainline";
            Console.WriteLine($"[MainWindow] LayoutSettings.DefaultLayout: {targetLayout}");

            // Use unified layout loading method
            LoadLayoutUnified(targetLayout);

            this.Closing += MainWindow_Closing;

            _ = Task.Run(async () =>
            {
                await InitializeLazyServices();
            });
        }
        private void InitializeEssentialServices()
        {
            try
            {
                Console.WriteLine("[MainWindow] InitializeEssentialServices() started");

                // Get logger service (ServiceContainer already initialized)
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger.LogInfo("MainWindow: Essential services initialization started");

                var plcMode = ConfigurationService.GetPlcMode();
                Console.WriteLine($"[MainWindow] PLC mode: {plcMode}");

                // Get PLC Manager from ServiceContainer (already initialized)
                Console.WriteLine("[MainWindow] Getting PLC Manager from ServiceContainer");
                _plcManager = ServiceContainer.GetService<PlcConnectionManager>();

                if (_plcManager != null)
                {
                    var logMsg = $"[MainWindow] PLC Manager initialized successfully: {_plcManager.GetType().Name}";
                    Console.WriteLine(logMsg);
                    _logger.LogInfo(logMsg);
                }
                else
                {
                    var logMsg = "[MainWindow] PLC Manager is null!";
                    Console.WriteLine(logMsg);
                    _logger.LogWarning(logMsg);
                }

                PlcConnections = new ObservableCollection<PlcConnectionUIData>();

                _plcManager.PlcConnectionFailed += PlcManager_PlcConnectionFailed;

                //Cập nhật UI (PlcConnections là ObservableCollection<PlcConnectionUIData>)
                foreach (var configInfo in _plcManager.GetAllPlcConnectionInfos())
                {
                    PlcConnections.Add(new PlcConnectionUIData(configInfo));
                }

                Console.WriteLine("[MainWindow] InitializeEssentialServices() completed successfully");
            }
            catch (Exception ex)
            {
                // Get logger service if not already available
                if (_logger == null)
                {
                    _logger = ServiceContainer.GetService<ILoggerService>();
                }

                _logger?.LogError($"Error getting PLC Manager from ServiceContainer: {ex.Message}", ex);

                // Try to create fallback PLC Manager
                try
                {
                    var emptyConfigs = new List<PlcConnectionInfo>();
                    var fallbackPlcMode = PlcMode.Mock; // Fallback to Mock mode on error
                    _plcManager = new PlcConnectionManager(emptyConfigs, fallbackPlcMode);
                    PlcConnections = new ObservableCollection<PlcConnectionUIData>();
                    _plcManager.PlcConnectionFailed += PlcManager_PlcConnectionFailed;
                }
                catch (Exception fallbackEx)
                {
                    _logger?.LogError($"Failed to create fallback PLC Manager: {fallbackEx.Message}", fallbackEx);
                }
            }

            // Khởi tạo toast notification
            InitializeToastNotification();
        }

        private async Task InitializeLazyServices()
        {
            try
            {
                await Task.Delay(500); // Đợi UI load xong

                // Get services from ServiceContainer
                Dispatcher.Invoke(() =>
                {
                    _dataAggregator = new DataAggregatorService();
                    _databaseService = ServiceContainer.GetService<IDatabaseService>();
                    _dataAggregator.RecordCompleted += DataAggregator_RecordCompleted;

                    // Initialize ProductionDataReader for PLC data reading
                    _productionDataReader = new ProductionDataReader(_plcManager);

                    _dataSavingService = new DataSavingService(_databaseService, _productionDataReader);
                });

                // Initialize database async
                await _databaseService.InitializeDatabaseAsync();

                // Khởi tạo timers
                Dispatcher.Invoke(() =>
                {
                    //InitializePlcDataReaderTimer();
                    UpdatePlcDataReaderTimerState();
                    InitializeShiftChangeTimer();
                    InitializeHourlyDataSaveTimer();

                    // Initialize shared ViewModels - use ServiceContainer's PlanViewModel
                    _planViewModel = ServiceContainer.GetService<PlanViewModel>();
                    _dailyPlanViewModel = new HomeDailyPlanViewModel(_planViewModel, _dataAggregator);

                    // Get dashboard service from container
                    _dashboardDataService = ServiceContainer.GetService<DashboardDataService>();
                    _canvasFaultDisplayService = ServiceContainer.GetService<CanvasFaultDisplayService>();

                    // Initialize dashboard chart ViewModels
                    InitializeDashboardCharts();
                    OnPropertyChanged(nameof(DailyPlanItems));

                    // Subscribe to ViewModel property changes to update MainWindow bindings
                    _planViewModel.PropertyChanged += (s, e) => {
                        Console.WriteLine($"[MainWindow] Received PropertyChanged from PlanVM for: {e.PropertyName}");
                        NotifyMainWindowPropertyChanges();
                    };
                    _dailyPlanViewModel.PropertyChanged += (s, e) => {
                        Console.WriteLine($"[MainWindow] Received PropertyChanged from HomeVM for: {e.PropertyName}");
                        NotifyMainWindowPropertyChanges();
                    };

                    // Set up page DataContexts
                    SetupPageDataContexts();

                    // Note: PLC manager is now set directly in MainlineContentView.LoadMainlineLayout()
                    // No need to set it here since MainlineLayout instances are created in ContentViews

                    // Update canvas references for fault display service (with delay to ensure layout is loaded)
                    if (_canvasFaultDisplayService != null)
                    {
                        var currentLayoutName = _currentLoadedLayout == _mainlineLayoutInstance ? "Mainline" :
                                              _currentLoadedLayout == _inspectionLayoutInstance ? "Inspection" : "Unknown";

                        // Delay to ensure ContentView and Layout are fully loaded
                        _ = Task.Run(async () =>
                        {
                            await Task.Delay(1000); // Wait for layout to be fully loaded
                            Dispatcher.Invoke(() => UpdateCanvasFaultDisplayReferences(currentLayoutName));
                        });
                    }

                    // Initial property change notification
                    NotifyMainWindowPropertyChanges();
                });

                // Load dashboard data
                //await LoadDashboardDataAsync();

                // Save production data on login
                //await _dataSavingService.SaveProductionDataOnLoginAsync();

                // Auto-connect PLCs
                //await Task.Delay(1000);
                //await AutoConnectPlcsOnStartup();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in lazy services initialization: {ex.Message}");
                _logger?.LogError("Error in lazy services initialization", ex);
            }
        }

        private void SetupPageDataContexts()
        {
            try
            {
                // Set DataContext for PlanPage using the shared PlanViewModel from ServiceContainer
                var planPageContent = ((ContentControl)PlanPage).Content as Views.PlanPage;
                if (planPageContent != null)
                {
                    planPageContent.DataContext = _planViewModel; // This is now the ServiceContainer's PlanViewModel
                }

                // Set up ReportPage with DatabaseService
                var reportPageContent = ((ContentControl)ReportPage).Content as Views.ReportPage;
                if (reportPageContent != null)
                {
                    reportPageContent.DataContext = new ViewModels.ReportViewModel(_databaseService);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error setting up page DataContexts: {ex.Message}", ex);
                _logger?.LogError("Error setting up page DataContexts", ex);
            }
        }

        private void NotifyMainWindowPropertyChanges()
        {
            OnPropertyChanged(nameof(TodayDateText));
            OnPropertyChanged(nameof(CurrentProductText));
            OnPropertyChanged(nameof(DailyPlanItems));
            OnPropertyChanged(nameof(SelectedItem));
            OnPropertyChanged(nameof(RefreshCommand));
            OnPropertyChanged(nameof(MarkCompleteCommand));
            OnPropertyChanged(nameof(StartNextCommand));
            OnPropertyChanged(nameof(StartEditModelCommand));
            OnPropertyChanged(nameof(SaveModelCommand));
            OnPropertyChanged(nameof(CancelEditModelCommand));
            OnPropertyChanged(nameof(EditableModelName));
            OnPropertyChanged(nameof(IsEditingModel));
            OnPropertyChanged(nameof(ExcelFilePath));
            OnPropertyChanged(nameof(ExcelFileStatus));
            OnPropertyChanged(nameof(ExcelRowCount));
            OnPropertyChanged(nameof(LastUpdated));
            OnPropertyChanged(nameof(PlanStatus));
            OnPropertyChanged(nameof(MonthlyPlanData));
            OnPropertyChanged(nameof(DailyPlanData));
        }

        private void OpenSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            IsSidebarOpen = true;
            AnimateSidebar(true);
        }

        private void CloseSidebarButton_Click(object sender, RoutedEventArgs e)
        {
            IsSidebarOpen = false;
            AnimateSidebar(false);
        }

        private void SidebarMenuListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (SidebarMenuListBox.SelectedItem is ListBoxItem selectedItem)
            {
                string menuText = selectedItem.Content.ToString();
                Console.WriteLine($"Menu selected: '{menuText}'");
                SwitchToPage(menuText);

                // Đóng sidebar sau khi chọn menu (tùy chọn)
                IsSidebarOpen = false;
                AnimateSidebar(false);
            }
        }

        private void SwitchToPage(string pageName)
        {
            Console.WriteLine($"SwitchToPage called with: '{pageName}'");

            // Ẩn tất cả các trang
            MainContentArea.Visibility = Visibility.Collapsed; // Hide main content area (Mainline/Inspection)
            PlanPage.Visibility = Visibility.Collapsed;
            ReportPage.Visibility = Visibility.Collapsed;
            MaintenancePage.Visibility = Visibility.Collapsed;
            SettingsPage.Visibility = Visibility.Collapsed;
            UsersPage.Visibility = Visibility.Collapsed;

            // Xử lý text menu (loại bỏ emoji và khoảng trắng thừa)
            string cleanPageName = pageName?.Trim();
            if (cleanPageName != null)
            {
                // Loại bỏ emoji ở đầu bằng cách tìm khoảng trắng đầu tiên
                int spaceIndex = cleanPageName.IndexOf(' ');
                if (spaceIndex > 0)
                {
                    cleanPageName = cleanPageName.Substring(spaceIndex + 1).Trim();
                }
            }

            Console.WriteLine($"Clean page name: '{cleanPageName}'");

            // Hiển thị trang được chọn và cập nhật tiêu đề
            switch (cleanPageName)
            {
                case "Trang chủ":
                    MainContentArea.Visibility = Visibility.Visible; // Show main content area (Mainline/Inspection)
                    UpdatePageTitle("Trang chủ");
                    break;
                case "Kế hoạch":
                    PlanPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Kế hoạch");
                    break;
                case "Báo cáo":
                    ReportPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Báo cáo");
                    break;
                case "Bảo dưỡng":
                    MaintenancePage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Bảo dưỡng");
                    break;
                case "Cài đặt":
                    SettingsPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Cài đặt");
                    break;
                case "Người dùng":
                    UsersPage.Visibility = Visibility.Visible;
                    UpdatePageTitle("Quản lý người dùng");
                    break;
                default:
                    UpdatePageTitle("Trang chủ");
                    Console.WriteLine($"Unknown page name: '{cleanPageName}', defaulting to Trang chủ");
                    break;
            }
        }

        private void UpdatePageTitle(string title)
        {
            if (PageTitleTextBlock != null)
            {
                PageTitleTextBlock.Text = title;
                Console.WriteLine($"Page title updated to: '{title}'");
            }
        }

        private void AnimateSidebar(bool open)
        {
            // Kích thước mong muốn của sidebar
            double targetWidth = open ? 200 : 0; // 200px khi mở, 0px khi đóng
            Duration duration = new Duration(TimeSpan.FromSeconds(0.3));

            if (open)
            {
                SidebarPanel.Visibility = Visibility.Visible;
                // Set width ngay lập tức để có thể animate
                SidebarColumn.Width = new GridLength(targetWidth, GridUnitType.Pixel);

                // Animate opacity từ 0 đến 1
                var opacityAnimation = new DoubleAnimation
                {
                    From = 0,
                    To = 1,
                    Duration = duration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
                };

                SidebarPanel.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
            else
            {
                // Animate opacity từ 1 đến 0
                var opacityAnimation = new DoubleAnimation
                {
                    From = 1,
                    To = 0,
                    Duration = duration,
                    EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseIn }
                };

                opacityAnimation.Completed += (s, e) =>
                {
                    SidebarPanel.Visibility = Visibility.Collapsed;
                    SidebarColumn.Width = new GridLength(0, GridUnitType.Pixel);
                };

                SidebarPanel.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            }
        }

        private void PlcManager_PlcConnectionFailed(object sender, PlcErrorEventArgs e)
        {
            // Đảm bảo chạy trên UI thread nếu event được kích hoạt từ thread khác
            Dispatcher.Invoke(() =>
            {
                AddNewError(e.PlcId, e.ErrorMessage); // Gọi AddNewError để hiển thị toast và notification
            });
        }

        private void DataAggregator_RecordCompleted(object sender, ProductRecordEventArgs e)
        {
            Console.WriteLine($"MainWindow: Received completed record from Aggregator: {e.Record}");
            _logger?.LogInfo($"MainWindow: Received completed record from Aggregator: {e.Record}");
            // Gọi service để lưu vào DB
            _databaseService.SaveProductRecordAsync(e.Record); // Có thể await nếu cần xử lý kết quả
        }

        private async Task AutoConnectPlcsOnStartup()
        {
            if (_plcManager != null)
            {
                await _plcManager.ConnectAllAutoConnectPlcsAsync();
                // Sau khi kết nối xong, cập nhật lại UI
                RefreshPlcConnectionStatusUI();
                UpdatePlcDataReaderTimerState(); // Cập nhật trạng thái timer sau khi auto-connect
            }
        }

        // Phương thức mới để làm mới trạng thái UI từ manager
        private void RefreshPlcConnectionStatusUI()
        {
            if (_plcManager == null || PlcConnections == null) return;

            foreach (var uiData in PlcConnections)
            {
                var serviceInfo = _plcManager.GetPlcConnectionInfo(uiData.PlcId);
                if (serviceInfo != null)
                {
                    uiData.UpdateFromServiceInfo(serviceInfo);
                }
            }
        }
        private void InitializePlcDataReaderTimer()
        {
            _plcDataReaderTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromSeconds(5) // Đọc PLC mỗi 5 giây
            };
            _plcDataReaderTimer.Tick += PlcDataReaderTimer_Tick;
            // _plcDataReaderTimer.Start(); // Sẽ start sau khi PLC kết nối thành công
        }

        private void InitializeShiftChangeTimer()
        {
            _shiftChangeTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMinutes(1) // Kiểm tra chuyển ca mỗi phút
            };
            _shiftChangeTimer.Tick += ShiftChangeTimer_Tick;
            _shiftChangeTimer.Start();
        }

        private void InitializeHourlyDataSaveTimer()
        {
            _hourlyDataSaveTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromHours(1) // Lưu dữ liệu mỗi giờ
            };
            _hourlyDataSaveTimer.Tick += HourlyDataSaveTimer_Tick;
            _hourlyDataSaveTimer.Start();
        }

        private void UpdatePlcDataReaderTimerState()
        {
            if (_plcDataReaderTimer == null || _plcManager == null)
            {
                // Chưa sẵn sàng để cập nhật trạng thái timer
                return;
            }

            // Kiểm tra xem có PLC nào đang thực sự kết nối không
            // bằng cách duyệt qua thông tin kết nối trong PlcConnectionManager
            bool anyPlcConnected = _plcManager.GetAllPlcConnectionInfos().Any(info => info.IsConnected);

            if (anyPlcConnected)
            {
                if (!_plcDataReaderTimer.IsEnabled) // Nếu có PLC kết nối và timer chưa chạy
                {
                    _plcDataReaderTimer.Start();
                    Console.WriteLine("PLC Data Reader Timer STARTED because at least one PLC is connected.");
                    _logger?.LogInfo("PLC Data Reader Timer STARTED because at least one PLC is connected.");
                }
            }
            else // Không có PLC nào đang kết nối
            {
                if (_plcDataReaderTimer.IsEnabled) // Nếu không có PLC kết nối và timer đang chạy
                {
                    _plcDataReaderTimer.Stop();
                    Console.WriteLine("PLC Data Reader Timer STOPPED because no PLCs are connected.");
                    _logger?.LogInfo("PLC Data Reader Timer STOPPED because no PLCs are connected.");
                }
            }
        }

        private async void PlcDataReaderTimer_Tick(object sender, EventArgs e)
        {
            if (_plcManager == null) return;

            // Lấy danh sách tất cả các PLC ID đã được cấu hình và đang có service (đã thử kết nối)
            var allConfiguredPlcIds = _plcManager.GetAllPlcConnectionInfos().Select(info => info.Id).ToList();

            foreach (var plcId in allConfiguredPlcIds)
            {
                var plcService = _plcManager.GetPlcService(plcId);
                var plcConfigInfo = _plcManager.GetPlcConnectionInfo(plcId); // Lấy thêm config info để biết Name

                if (plcService == null || !plcService.IsConnected || plcConfigInfo == null)
                {
                    // Bỏ qua nếu PLC không có service, không kết nối, hoặc không có config info
                    continue;
                }

                Console.WriteLine($"PlcDataReaderTimer_Tick: Processing PLC ID '{plcId}' ({plcConfigInfo.Name})");

                // PLC1_Reader: Đọc mã sản phẩm
                if (plcConfigInfo.Id.Equals("PLC1_Reader", StringComparison.OrdinalIgnoreCase) ||
                    plcConfigInfo.Name.Contains("Đọc Mã", StringComparison.OrdinalIgnoreCase))
                {
                    await ReadProductCodeFromPLC1(plcService, plcId, plcConfigInfo);
                }

                // PLC8_Tester: Đọc kết quả test, điện áp, và số thứ tự
                if (plcConfigInfo.Id.Equals("PLC8_Tester", StringComparison.OrdinalIgnoreCase))
                {
                    await ReadTestDataFromPLC8(plcService, plcId, plcConfigInfo);
                }
            }

            // Đọc dữ liệu cho báo cáo từ PLC_MainLine
            await ReadProductionDataFromPlc();

            _dataAggregator.CleanupStaleRecords(TimeSpan.FromMinutes(5));
        }

        /// <summary>
        /// Read product code from PLC1_Reader
        /// </summary>
        private async Task ReadProductCodeFromPLC1(IPlcService plcService, string plcId, PlcConnectionInfo plcConfigInfo)
        {
            try
            {
                PlcReadResult productCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.ProductCode_ReaderStation);
                if (!productCodeReadResult.IsSuccess)
                {
                    AddNewError($"{plcId}_ReadFail_Code", $"{plcConfigInfo.Name}: Read Product Code Error. Details: {productCodeReadResult.Message}");
                    return;
                }

                string productCode = PlcDataMapper.MapToApplicationType<string>(productCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.ProductCode_ReaderStation));
                if (string.IsNullOrWhiteSpace(productCode))
                    return;

                PlcReadResult sequenceForCodeReadResult = await plcService.ReadAsync(PlcDeviceAddress.CurrentProductSequenceNumberWord);
                if (!sequenceForCodeReadResult.IsSuccess)
                {
                    AddNewError($"{plcId}_ReadFail_SeqForCode", $"{plcConfigInfo.Name}: Read Sequence for Code Error. Details: {sequenceForCodeReadResult.Message}");
                    return;
                }

                short seqForCodeShort = PlcDataMapper.MapToApplicationType<short>(sequenceForCodeReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.CurrentProductSequenceNumberWord));
                if (seqForCodeShort > 0)
                {
                    _dataAggregator.ReceiveProductCode(seqForCodeShort, productCode.Trim());

                    // Update ProductCode display for Inspection layout only
                    if (_currentLoadedLayout == _inspectionLayoutInstance)
                    {
                        UpdateProductCodeDisplay(productCode.Trim());
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading product code from PLC1_Reader: {ex.Message}");
            }
        }

        /// <summary>
        /// Read test data from PLC8_Tester
        /// </summary>
        private async Task ReadTestDataFromPLC8(IPlcService plcService, string plcId, PlcConnectionInfo plcConfigInfo)
        {
            try
            {
                // Read test result (0 = NG, 1 = OK)
                PlcReadResult testResultReadResult = await plcService.ReadAsync(PlcDeviceAddress.TestResultFromTester);
                if (!testResultReadResult.IsSuccess)
                {
                    AddNewError($"{plcId}_ReadFail_TestResult", $"{plcConfigInfo.Name}: Read Test Result Error. Details: {testResultReadResult.Message}");
                    return;
                }

                // Read sequence number
                PlcReadResult sequenceReadResult = await plcService.ReadAsync(PlcDeviceAddress.SequenceNumberAtTest);
                if (!sequenceReadResult.IsSuccess)
                {
                    AddNewError($"{plcId}_ReadFail_Sequence", $"{plcConfigInfo.Name}: Read Sequence Number Error. Details: {sequenceReadResult.Message}");
                    return;
                }

                // Read voltage
                PlcReadResult voltageReadResult = await plcService.ReadAsync(PlcDeviceAddress.VoltageFromTester);
                if (!voltageReadResult.IsSuccess)
                {
                    AddNewError($"{plcId}_ReadFail_Voltage", $"{plcConfigInfo.Name}: Read Voltage Error. Details: {voltageReadResult.Message}");
                    return;
                }

                // Convert to application types
                int testResult = PlcDataMapper.MapToApplicationType<int>(testResultReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.TestResultFromTester));
                int sequenceNumber = PlcDataMapper.MapToApplicationType<int>(sequenceReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.SequenceNumberAtTest));
                int voltageRaw = PlcDataMapper.MapToApplicationType<int>(voltageReadResult.Value, plcService.GetRegisterInfo(PlcDeviceAddress.VoltageFromTester));

                // Convert voltage from raw value (scaled by 10) to actual voltage
                double voltage = voltageRaw / 10.0;

                // Update test data display for Inspection layout only
                if (_currentLoadedLayout == _inspectionLayoutInstance && sequenceNumber > 0)
                {
                    UpdateTestDataDisplay(testResult, voltage, sequenceNumber);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading test data from PLC8_Tester: {ex.Message}");
            }
        }

        /// <summary>
        /// Update product code display in InspectionContentView and ProductVisualControl
        /// </summary>
        private void UpdateProductCodeDisplay(string productCode)
        {
            try
            {
                // Update CurrentProductText in InspectionViewModel
                if (MainContentArea.Content is Views.InspectionContentView inspectionContentView)
                {
                    Dispatcher.Invoke(() =>
                    {
                        if (inspectionContentView.DataContext is ViewModels.InspectionViewModel inspectionViewModel)
                        {
                            inspectionViewModel.CurrentProductText = productCode;
                        }
                    });

                    // Find the InspectionLayout in the ZoomPanCanvas
                    var zoomPanCanvas = FindChildByName<Canvas>(inspectionContentView, "ZoomPanCanvas");
                    if (zoomPanCanvas != null)
                    {
                        var inspectionLayout = zoomPanCanvas.Children.OfType<Layouts.InspectionLayout>().FirstOrDefault();
                        if (inspectionLayout != null)
                        {
                            // Update all ProductVisualControls with the new product code
                            inspectionLayout.UpdateAllProductCodes(productCode);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating product code display: {ex.Message}");
            }
        }

        /// <summary>
        /// Update test data display in ProductVisualControl
        /// </summary>
        private void UpdateTestDataDisplay(int testResult, double voltage, int sequenceNumber)
        {
            try
            {
                // Find the InspectionLayout in the ZoomPanCanvas
                if (MainContentArea.Content is Views.InspectionContentView inspectionContentView)
                {
                    var zoomPanCanvas = FindChildByName<Canvas>(inspectionContentView, "ZoomPanCanvas");
                    if (zoomPanCanvas != null)
                    {
                        var inspectionLayout = zoomPanCanvas.Children.OfType<Layouts.InspectionLayout>().FirstOrDefault();
                        if (inspectionLayout != null)
                        {
                            // Update all ProductVisualControls with the new test data
                            inspectionLayout.UpdateAllTestData(testResult, voltage, sequenceNumber);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating test data display: {ex.Message}");
            }
        }

        /// <summary>
        /// Timer để kiểm tra chuyển ca và lưu dữ liệu tự động
        /// </summary>
        private async void ShiftChangeTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                var now = DateTime.Now;

                // Check if it's 5 minutes before shift change
                if (WorkShiftHelper.IsBeforeShiftChange(5))
                {
                    if ((now - _lastShiftChangeCheck).TotalMinutes >= 1)
                    {
                        _lastShiftChangeCheck = now;
                        await _dataSavingService.SaveProductionDataBeforeShiftChangeAsync();
                        Console.WriteLine($"5 minutes before shift change at {now:HH:mm:ss}, production data saved.");
                    }
                }

                // Check if it's shift change time (start of new shift)
                if (WorkShiftHelper.IsShiftChangeTime())
                {
                    if ((now - _lastShiftChangeCheck).TotalMinutes >= 1)
                    {
                        _lastShiftChangeCheck = now;
                        await _dataSavingService.SaveProductionDataOnNewShiftStartAsync();
                        Console.WriteLine($"New shift started at {now:HH:mm:ss}, production data saved.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ShiftChangeTimer_Tick: {ex.Message}");
            }
        }

        /// <summary>
        /// Read production data from PLC and check for errors
        /// </summary>
        private async Task ReadProductionDataFromPlc()
        {
            try
            {
                if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                    return;

                var productionData = await _productionDataReader.ReadProductionDataFromPlcAsync("ALL");

                // Check for new errors and save immediately
                if (!string.IsNullOrEmpty(productionData.Error_Code))
                {
                    await _dataSavingService.SaveProductionDataOnErrorAsync(productionData);
                    Console.WriteLine($"New error detected: {productionData.Error_Code}, data saved immediately.");
                }

                // Check for PLC errors and display them
                await CheckPlcErrorsAndDisplay();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error reading production data from PLC: {ex.Message}");
            }
        }

        /// <summary>
        /// Check PLC error registers and display errors using fault mapping
        /// </summary>
        private async Task CheckPlcErrorsAndDisplay()
        {
            try
            {
                if (!_plcManager.IsPlcConnected("PLC_MainLine"))
                    return;

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                if (plcService == null || !plcService.IsConnected)
                    return;

                // Check all M4000-M4090 error registers
                for (int i = 0; i <= 90; i++)
                {
                    try
                    {
                        var errorEnumName = $"M40{i:D2}";
                        if (Enum.TryParse<PlcDeviceAddress>(errorEnumName, out var errorEnum))
                        {
                            var errorResult = await plcService.ReadAsync(errorEnum);
                            if (errorResult.IsSuccess && Convert.ToBoolean(errorResult.Value))
                            {
                                // Error is active, check fault mapping and display
                                await ProcessActiveError(errorEnumName);
                            }
                            else if (errorResult.IsSuccess)
                            {
                                // Error is inactive, clear fault display
                                await ProcessInactiveError(errorEnumName);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error reading PLC register M40{i:D2}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in CheckPlcErrorsAndDisplay: {ex.Message}");
            }
        }

        /// <summary>
        /// Process an active error by looking up fault mapping and displaying it
        /// </summary>
        private async Task ProcessActiveError(string errorCode)
        {
            try
            {
                // Load fault mapping from JSON file
                var faultMapping = await LoadFaultMappingAsync();
                if (faultMapping != null && faultMapping.ContainsKey(errorCode))
                {
                    var faultInfo = faultMapping[errorCode];

                    // Parse stations array from JSON
                    var stations = new List<int> { 27 }; // Default to station 27
                    if (faultInfo.ContainsKey("stations") && faultInfo["stations"] is System.Text.Json.JsonElement stationsElement)
                    {
                        if (stationsElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            stations = new List<int>();
                            foreach (var station in stationsElement.EnumerateArray())
                            {
                                if (station.TryGetInt32(out int stationNum))
                                {
                                    stations.Add(stationNum);
                                }
                            }
                            if (stations.Count == 0) stations.Add(27); // Fallback if no valid stations
                        }
                    }

                    var message = faultInfo.ContainsKey("message") ? faultInfo["message"].ToString() : $"Unknown error: {errorCode}";

                    // Create error message for display
                    var stationText = stations.Count > 0 ? $"Trạm {string.Join(", ", stations)}" : "Hệ thống";
                    var errorMessage = $"🚨 LỖI PLC\nMã lỗi: {errorCode} - {stationText} : {message}";

                    // Display error using AddNewError method
                    AddNewError($"PLC_{errorCode}", errorMessage);

                    // Show red border on affected stations in virtual layout
                    SetStationErrorStatus(stations, true);

                    // Display fault on canvas using CanvasFaultDisplayService
                    if (_canvasFaultDisplayService != null)
                    {
                        var plcFaultInfo = new PlcFaultInfo(errorCode, stations, message ?? "Unknown error", true);

                        // Determine system type based on current layout
                        string systemType = _currentLoadedLayout == _mainlineLayoutInstance ? "mainline" :
                                          _currentLoadedLayout == _inspectionLayoutInstance ? "inspection" : "mainline";

                        _canvasFaultDisplayService.DisplayFault(plcFaultInfo, systemType);
                    }

                    Console.WriteLine($"PLC Error detected: {errorCode} - {message} at stations: {string.Join(", ", stations)}");
                }
                else
                {
                    // Fallback for unmapped errors
                    var errorMessage = $"🚨 LỖI PLC\nMã lỗi: {errorCode}-Trạm 27-Lỗi chưa được định nghĩa";
                    AddNewError($"PLC_{errorCode}", errorMessage);

                    // Show red border on default station 27 for unmapped errors
                    SetStationErrorStatus(new List<int> { 27 }, true);

                    // Display fault on canvas for unmapped errors
                    if (_canvasFaultDisplayService != null)
                    {
                        var plcFaultInfo = new PlcFaultInfo(errorCode, new List<int> { 27 }, "Lỗi chưa được định nghĩa", true);

                        // Determine system type based on current layout
                        string systemType = _currentLoadedLayout == _mainlineLayoutInstance ? "mainline" :
                                          _currentLoadedLayout == _inspectionLayoutInstance ? "inspection" : "mainline";

                        _canvasFaultDisplayService.DisplayFault(plcFaultInfo, systemType);
                    }

                    Console.WriteLine($"Unmapped PLC Error detected: {errorCode}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing active error {errorCode}: {ex.Message}");
            }
        }

        /// <summary>
        /// Process an inactive error by clearing fault display
        /// </summary>
        private async Task ProcessInactiveError(string errorCode)
        {
            try
            {
                // Clear fault display from canvas
                if (_canvasFaultDisplayService != null)
                {
                    // Determine system type based on current layout
                    string systemType = _currentLoadedLayout == _mainlineLayoutInstance ? "mainline" :
                                      _currentLoadedLayout == _inspectionLayoutInstance ? "inspection" : "mainline";

                    _canvasFaultDisplayService.ClearFault(errorCode, systemType);
                }

                // Clear red border from stations (we need to load fault mapping to know which stations to clear)
                var faultMapping = await LoadFaultMappingAsync();
                if (faultMapping != null && faultMapping.ContainsKey(errorCode))
                {
                    var faultInfo = faultMapping[errorCode];

                    // Parse stations array from JSON
                    var stations = new List<int> { 27 }; // Default to station 27
                    if (faultInfo.ContainsKey("stations") && faultInfo["stations"] is System.Text.Json.JsonElement stationsElement)
                    {
                        if (stationsElement.ValueKind == System.Text.Json.JsonValueKind.Array)
                        {
                            stations = new List<int>();
                            foreach (var station in stationsElement.EnumerateArray())
                            {
                                if (station.TryGetInt32(out int stationNum))
                                {
                                    stations.Add(stationNum);
                                }
                            }
                            if (stations.Count == 0) stations.Add(27); // Fallback if no valid stations
                        }
                    }

                    // Clear red border on affected stations
                    SetStationErrorStatus(stations, false);
                }
                else
                {
                    // Clear red border on default station 27 for unmapped errors
                    SetStationErrorStatus(new List<int> { 27 }, false);
                }

                Console.WriteLine($"PLC Error cleared: {errorCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error processing inactive error {errorCode}: {ex.Message}");
            }
        }

        /// <summary>
        /// Set error status for stations in virtual layout
        /// </summary>
        /// <param name="stationNumbers">List of station numbers to set error status</param>
        /// <param name="hasError">True to show error (red border), false to clear error</param>
        private void SetStationErrorStatus(List<int> stationNumbers, bool hasError)
        {
            try
            {
                foreach (var stationNumber in stationNumbers)
                {
                    // Update Mainline layout stations (1-26)
                    if (stationNumber >= 1 && stationNumber <= 26 && _mainlineLayoutInstance != null)
                    {
                        var station = _mainlineLayoutInstance.GetStationByNumber(stationNumber.ToString("00"));
                        if (station != null)
                        {
                            station.DeviceStatus = hasError ? DeviceOperationalStatus.Error : DeviceOperationalStatus.Idle;
                            station.UpdateStationDisplayBrush();
                            Console.WriteLine($"MainlineLayout: Station {stationNumber} error status set to {hasError}");
                        }
                        else
                        {
                            Console.WriteLine($"MainlineLayout: Station {stationNumber} not found");
                        }
                    }

                    // Update Inspection layout stations (27-62)
                    if (stationNumber >= 27 && stationNumber <= 62 && _inspectionLayoutInstance != null)
                    {
                        var station = _inspectionLayoutInstance.GetStationByNumber(stationNumber.ToString());
                        if (station != null)
                        {
                            station.DeviceStatus = hasError ? DeviceOperationalStatus.Error : DeviceOperationalStatus.Idle;
                            station.UpdateStationDisplayBrush();
                            Console.WriteLine($"InspectionLayout: Station {stationNumber} error status set to {hasError}");
                        }
                        else
                        {
                            Console.WriteLine($"InspectionLayout: Station {stationNumber} not found");
                        }
                    }

                    // If station number doesn't match any layout, log it
                    if (stationNumber < 1 || stationNumber > 62)
                    {
                        Console.WriteLine($"Invalid station number: {stationNumber}. Valid range is 1-62.");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error setting station error status: {ex.Message}");
            }
        }

        /// <summary>
        /// Load fault mapping from JSON file
        /// </summary>
        private async Task<Dictionary<string, Dictionary<string, object>>> LoadFaultMappingAsync()
        {
            try
            {
                var faultMappingPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config", "fault_mapping.json");
                if (File.Exists(faultMappingPath))
                {
                    var jsonContent = await File.ReadAllTextAsync(faultMappingPath);
                    var options = new System.Text.Json.JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };
                    var faultMapping = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, Dictionary<string, object>>>(jsonContent, options);
                    return faultMapping;
                }
                else
                {
                    Console.WriteLine($"Fault mapping file not found: {faultMappingPath}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading fault mapping: {ex.Message}");
                return null;
            }
        }



        /// <summary>
        /// Timer để lưu dữ liệu mỗi giờ
        /// </summary>
        private async void HourlyDataSaveTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                await _dataSavingService.SaveProductionDataOnHourlyScheduleAsync();
                Console.WriteLine($"Hourly production data saved at {DateTime.Now:HH:mm:ss}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in HourlyDataSaveTimer_Tick: {ex.Message}");
            }
        }

        /// <summary>
        /// Get optimized shift actual calculation
        /// Current shift actual = Total daily actual - Previous shifts actual (from database)
        /// </summary>
        public async Task<int> GetOptimizedShiftActualAsync(string shiftName)
        {
            try
            {
                return await _productionDataReader.GetOptimizedShiftActualAsync(shiftName, _databaseService);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error calculating optimized shift actual: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Save slow operation data (called from Slow Operation button)
        /// </summary>
        public async Task SaveSlowOperationData()
        {
            try
            {
                // Read data from 26 stations
                for (int i = 1; i <= 26; i++)
                {
                    var stationData = await _productionDataReader.ReadProductionDataFromPlcAsync($"ST{i}");
                    stationData.ReportType = "SlowOperation";
                    stationData.Notes = $"Slow operation at station ST{i}";

                    // Always save manual operation data
                    await _databaseService.SaveProductionDataAsync(stationData);
                }

                Console.WriteLine("Slow operation data saved for all 18 stations.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving slow operation data: {ex.Message}");
            }
        }

        /// <summary>
        /// Save measure operation data (called from Measure Operation button)
        /// </summary>
        public async Task SaveMeasureOperationData()
        {
            try
            {
                // Measure 10 times consecutively for 26 positions
                for (int station = 1; station <= 26; station++)
                {
                    for (int measurement = 1; measurement <= 10; measurement++)
                    {
                        var measureData = await _productionDataReader.ReadProductionDataFromPlcAsync($"ST{station}");
                        measureData.ReportType = "MeasureOperation";
                        measureData.Notes = $"Measurement {measurement}/10 at station ST{station}";

                        // Always save manual operation data
                        await _databaseService.SaveProductionDataAsync(measureData);

                        // Small delay between measurements
                        await Task.Delay(100);
                    }
                }

                Console.WriteLine("Measure operation data saved: 10 measurements for 26 stations.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving measure operation data: {ex.Message}");
            }
        }
        private async void MainWindow_Closing(object sender, CancelEventArgs e)
        {
            // Stop all timers
            _plcDataReaderTimer?.Stop();
            _shiftChangeTimer?.Stop();
            _hourlyDataSaveTimer?.Stop();

            // Disconnect all PLCs
            Console.WriteLine("MainWindow closing. Disconnecting all PLCs...");
            if (_plcManager != null)
            {
                await _plcManager.DisconnectAllAsync();
            }
            Console.WriteLine("All PLCs disconnect command sent.");
        }

        private void InitializeToastNotification()
        {
            _toastNotification = ToastNotificationPanel.Instance;
            _toastNotification.SetContainer(ToastNotificationContainer);
            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.Duration = 3000;
            _toastNotification.AnimationDuration = 300;
            _toastNotification.CornerRadius = 10;
            _toastNotification.EnableShadow = true;
        }

        private void InitializeDashboardCharts()
        {
            try
            {
                // Dashboard charts are now handled by individual UserControls
                // Each control manages its own ViewModel and data binding
                _logger?.LogDebug("Dashboard charts initialized successfully (via UserControls)");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error initializing dashboard charts: {ex.Message}", ex);
            }
        }

        public void ShowToast(string title, string message, ToastType type)
        {
            try
            {
                string fullMessage = string.IsNullOrEmpty(title) ? message : $"{title}: {message}";
                _toastNotification.Show(fullMessage, type);
                _logger?.LogDebug($"Toast shown: {type} - {fullMessage}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error showing toast: {ex.Message}", ex);
            }
        }
        public void AddNewError(string errorId, string message)
        {
            // Ensure execution on UI thread if called from elsewhere
            if (!Dispatcher.CheckAccess())
            {
                Dispatcher.Invoke(() => AddNewError(errorId, message));
                return;
            }

            var newError = new ErrorNotification(errorId, message);
            AllErrors.Insert(0, newError); // Add to top of the list
            NewErrorCount++;

            _toastNotification.Position = ToastPosition.BottomRight;
            _toastNotification.ShowError(message);
            _logger?.LogError($"New Error: {errorId} - {message}");
        }

        private void ErrorBellButton_Click(object sender, RoutedEventArgs e)
        {
            IsErrorListPopupOpen = !IsErrorListPopupOpen;
            if (IsErrorListPopupOpen)
            {
                // Mark viewed errors as not new, but only if they were new
                foreach (var err in AllErrors.Where(er => er.IsNew).ToList()) // ToList to avoid modification issues
                {
                    err.IsNew = false;
                }
                NewErrorCount = 0; // Reset badge count
                // The BellShakeStoryboard will stop via DataTrigger ExitActions
            }
        }
        private void ClearAllErrorsButton_Click(object sender, RoutedEventArgs e)
        {
            // This only clears the display. PLC errors would need PLC acknowledgement.
            AllErrors.Clear();
            NewErrorCount = 0;
            IsErrorListPopupOpen = false; // Optionally close popup
        }

        // --- INotifyPropertyChanged Implementation ---
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
        /// <summary>
        /// Unified layout loading method that handles both content views and layout instances
        /// This replaces the duplicated LoadLayout and LoadApplicationBasedOnSettings methods
        /// </summary>
        private void LoadLayoutUnified(string layoutName)
        {
            try
            {
                Console.WriteLine($"[MainWindow] Loading unified layout: {layoutName}");

                // 1. Clean up previous layout and event subscriptions
                if (_currentLoadedLayout != null)
                {
                    if (_currentLoadedLayout == _mainlineLayoutInstance && _mainlineLayoutInstance != null)
                    {
                        _mainlineLayoutInstance.ProductExitingLine -= Mainline_ProductExitingLine;
                        Console.WriteLine("Unsubscribed from MainlineLayout.ProductExitingLine");
                    }
                }

                // Reset references
                _currentLoadedLayout = null;
                _mainlineLayoutInstance = null;
                _inspectionLayoutInstance = null;

                // 2. Load content view and create layout instance based on layout name
                switch (layoutName?.ToLower())
                {
                    case "mainline":
                        // Load Mainline content view - the layout will be created inside the ContentView
                        var mainlineContentView = new Views.MainlineContentView(this);
                        MainContentArea.Content = mainlineContentView;
                        Console.WriteLine("[MainWindow] Loaded Mainline content view with MainWindow reference");

                        // Note: MainlineLayout instance will be created inside MainlineContentView
                        // and will get PLC manager from MainWindow.GetPlcManager()
                        break;

                    case "inspection":
                        // Load Inspection content view
                        var inspectionContentView = new Views.InspectionContentView();
                        MainContentArea.Content = inspectionContentView;
                        Console.WriteLine("[MainWindow] Loaded Inspection content view");

                        // Create Inspection layout instance
                        _inspectionLayoutInstance = new InspectionLayout();
                        _currentLoadedLayout = _inspectionLayoutInstance;
                        Console.WriteLine("Created InspectionLayout instance");
                        break;

                    default:
                        // Default fallback - load Mainline
                        Console.WriteLine($"[MainWindow] Unknown layout: {layoutName}. Loading Mainline as fallback.");
                        var fallbackMainlineContentView = new Views.MainlineContentView(this);
                        MainContentArea.Content = fallbackMainlineContentView;

                        _mainlineLayoutInstance = new MainlineLayout();
                        _mainlineLayoutInstance.ProductExitingLine += Mainline_ProductExitingLine;
                        _currentLoadedLayout = _mainlineLayoutInstance;
                        Console.WriteLine("Loaded fallback Mainline layout");
                        break;
                }

                Console.WriteLine($"Layout '{layoutName}' loaded successfully with unified method.");

                // Update canvas references after layout is loaded
                if (_canvasFaultDisplayService != null)
                {
                    _ = Task.Run(async () =>
                    {
                        await Task.Delay(500); // Wait for layout to be fully rendered
                        Dispatcher.Invoke(() => UpdateCanvasFaultDisplayReferences(layoutName));
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[MainWindow] Error in LoadLayoutUnified: {ex.Message}");

                // Fallback to Mainline
                try
                {
                    var fallbackMainlineContentView = new Views.MainlineContentView(this);
                    MainContentArea.Content = fallbackMainlineContentView;
                    _mainlineLayoutInstance = new MainlineLayout();
                    _mainlineLayoutInstance.ProductExitingLine += Mainline_ProductExitingLine;
                    _currentLoadedLayout = _mainlineLayoutInstance;
                    Console.WriteLine("[MainWindow] Fallback to Mainline successful");
                }
                catch (Exception fallbackEx)
                {
                    Console.WriteLine($"[MainWindow] Fallback failed: {fallbackEx.Message}");
                }
            }
        }

        /// <summary>
        /// Cập nhật canvas references cho fault display service
        /// </summary>
        private void UpdateCanvasFaultDisplayReferences(string layoutName)
        {
            if (_canvasFaultDisplayService == null) return;

            Canvas? mainlineCanvas = null;
            Canvas? inspectionCanvas = null;

            // Tìm ProductLayerCanvas trong ContentView hiện tại
            if (MainContentArea.Content != null)
            {
                if (layoutName == "Mainline" && MainContentArea.Content is Views.MainlineContentView mainlineContentView)
                {
                    // Tìm ZoomPanCanvas trong MainlineContentView
                    var zoomPanCanvas = FindChildByName<Canvas>(mainlineContentView, "ZoomPanCanvas");
                    if (zoomPanCanvas != null)
                    {
                        // Tìm MainlineLayout trong ZoomPanCanvas
                        var mainlineLayout = zoomPanCanvas.Children.OfType<Layouts.MainlineLayout>().FirstOrDefault();
                        if (mainlineLayout != null)
                        {
                            // Tìm ProductLayerCanvas trong MainlineLayout
                            mainlineCanvas = FindChildByName<Canvas>(mainlineLayout, "ProductLayerCanvas");
                        }
                    }
                }
                else if (layoutName == "Inspection" && MainContentArea.Content is Views.InspectionContentView inspectionContentView)
                {
                    // Tìm ZoomPanCanvas trong InspectionContentView
                    var zoomPanCanvas = FindChildByName<Canvas>(inspectionContentView, "ZoomPanCanvas");
                    if (zoomPanCanvas != null)
                    {
                        // Tìm InspectionLayout trong ZoomPanCanvas
                        var inspectionLayout = zoomPanCanvas.Children.OfType<Layouts.InspectionLayout>().FirstOrDefault();
                        if (inspectionLayout != null)
                        {
                            // Tìm ProductLayerCanvas trong InspectionLayout
                            inspectionCanvas = FindChildByName<Canvas>(inspectionLayout, "ProductLayerCanvas");
                        }
                    }
                }
            }

            // Cập nhật references
            _canvasFaultDisplayService.SetCanvasReferences(mainlineCanvas, inspectionCanvas);

            Console.WriteLine($"Updated canvas references for {layoutName} - " +
                $"Mainline: {(mainlineCanvas != null ? "Found" : "Not found")}, " +
                $"Inspection: {(inspectionCanvas != null ? "Found" : "Not found")}");
        }

        /// <summary>
        /// Tìm Canvas với tên cụ thể trong UserControl
        /// </summary>
        private Canvas? FindCanvasInLayout(UserControl layout, string canvasName)
        {
            return FindChildByName<Canvas>(layout, canvasName);
        }

        /// <summary>
        /// Tìm child control theo tên
        /// </summary>
        private T? FindChildByName<T>(DependencyObject parent, string name) where T : FrameworkElement
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T element && element.Name == name)
                {
                    return element;
                }

                var result = FindChildByName<T>(child, name);
                if (result != null)
                {
                    return result;
                }
            }

            return null;
        }
        private void Mainline_ProductExitingLine(object sender, ProductItemEventArgs e)
        {
            Console.WriteLine($"Product {e.Product.ProductId} received by MainWindow from Mainline.");

            // Check if the InspectionLayout is currently loaded AND is the _inspectionLayoutInstance we expect
            if (_currentLoadedLayout == _inspectionLayoutInstance && _inspectionLayoutInstance != null)
            {
                _inspectionLayoutInstance.AcceptProduct(e.Product);
                Console.WriteLine($"Product {e.Product.ProductId} passed to active InspectionLayout.");
            }
            else
            {
                Console.WriteLine($"Product {e.Product.ProductId} exited Mainline. InspectionLayout is not the current view or is invalid. Product discarded from UI flow.");
                // Product is effectively lost from the UI simulation at this point if not handled
            }
        }

        public class RelayCommand<T> : ICommand
        {
            private readonly Action<T> _execute;
            private readonly Func<T, bool> _canExecute;

            public event EventHandler CanExecuteChanged
            {
                add { CommandManager.RequerySuggested += value; }
                remove { CommandManager.RequerySuggested -= value; }
            }

            public RelayCommand(Action<T> execute, Func<T, bool> canExecute = null)
            {
                _execute = execute ?? throw new ArgumentNullException(nameof(execute));
                _canExecute = canExecute;
            }

            public bool CanExecute(object parameter)
            {
                return _canExecute == null || _canExecute((T)parameter);
            }

            public void Execute(object parameter)
            {
                _execute((T)parameter);
            }
        }

        private void UpdateUserInfo()
        {
            if (UserSession.IsLoggedIn)
            {
                UserFullNameTextBlock.Text = UserSession.CurrentUser!.Fullname;
                // Hiển thị ca làm việc với xuống dòng: "Ca hành chính\n8 giờ - 17 giờ"
                var shift = UserSession.CurrentShift!;
                UserShiftTextBlock.Text = $"{shift.Name}\n{shift.TimeRange}";

                // Update menu visibility based on user role
                UpdateMenuVisibility();
            }
            else
            {
                UserFullNameTextBlock.Text = "Chưa đăng nhập";
                UserShiftTextBlock.Text = "";
            }
        }

        private void UpdateMenuVisibility()
        {
            // Only Admin can see Users management
            var isAdmin = UserSession.CurrentUser?.Role == "Administrator";

            // Find the Users menu item and hide/show based on permission
            foreach (ListBoxItem item in SidebarMenuListBox.Items)
            {
                if (item.Content?.ToString()?.Contains("Người dùng") == true)
                {
                    item.Visibility = isAdmin ? Visibility.Visible : Visibility.Collapsed;
                    break;
                }
            }
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                if (_dashboardDataService == null) return;

                // Load idle time data from Excel (allowed time)
                var idleTimeData = await _dashboardDataService.GetIdleTimeDataAsync();

                // Read current idle time from PLC registers
                var currentPlcIdleTime = await ReadCurrentIdleTimeFromPlcAsync();
                if (currentPlcIdleTime.HasValue)
                {
                    idleTimeData.DailyUsedIdleTime = currentPlcIdleTime.Value;
                    Console.WriteLine($"Dashboard: Updated daily idle time from PLC: {currentPlcIdleTime.Value.TotalMinutes:F1} minutes");
                }

                // Update monthly idle time with PLC data
                await UpdateMonthlyIdleTimeAsync(currentPlcIdleTime);

                // Get updated monthly idle time from database
                var monthlyIdleTime = await _databaseService.GetMonthlyIdleTimeFromDatabaseAsync();
                idleTimeData.MonthlyUsedIdleTime = monthlyIdleTime;

                // Load production data for today
                var todayProduction = await _dashboardDataService.GetDailyProductionSummaryAsync(DateTime.Today);

                // Load production data for current shift
                var currentShift = UserSession.CurrentShift?.Name ?? "Ca hành chính";
                var shiftProduction = await _dashboardDataService.GetShiftProductionSummaryAsync(DateTime.Today, currentShift);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading dashboard data: {ex.Message}");
            }
        }

        /// <summary>
        /// Reads current idle time from PLC registers (Timestopst1 through Timestopst18)
        /// </summary>
        /// <returns>Total idle time from all stations, or null if PLC not available</returns>
        private async Task<TimeSpan?> ReadCurrentIdleTimeFromPlcAsync()
        {
            try
            {
                if (_plcManager == null || !_plcManager.IsPlcConnected("PLC_MainLine"))
                {
                    Console.WriteLine("Dashboard: PLC_MainLine not connected, cannot read idle time");
                    return null;
                }

                var plcService = _plcManager.GetPlcService("PLC_MainLine");
                if (plcService == null)
                {
                    Console.WriteLine("Dashboard: PLC service not available");
                    return null;
                }

                int totalIdleTimeSeconds = 0;
                var stationIdleRegisters = new[]
                {
                    PlcDeviceAddress.Timestopst1, PlcDeviceAddress.Timestopst2, PlcDeviceAddress.Timestopst3,
                    PlcDeviceAddress.Timestopst4, PlcDeviceAddress.Timestopst5, PlcDeviceAddress.Timestopst6,
                    PlcDeviceAddress.Timestopst7, PlcDeviceAddress.Timestopst8, PlcDeviceAddress.Timestopst9,
                    PlcDeviceAddress.Timestopst10, PlcDeviceAddress.Timestopst11, PlcDeviceAddress.Timestopst12,
                    PlcDeviceAddress.Timestopst13, PlcDeviceAddress.Timestopst14, PlcDeviceAddress.Timestopst15,
                    PlcDeviceAddress.Timestopst16, PlcDeviceAddress.Timestopst17, PlcDeviceAddress.Timestopst18,
                    PlcDeviceAddress.Timestopst19, PlcDeviceAddress.Timestopst20, PlcDeviceAddress.Timestopst21,
                    PlcDeviceAddress.Timestopst22, PlcDeviceAddress.Timestopst23, PlcDeviceAddress.Timestopst24,
                    PlcDeviceAddress.Timestopst25, PlcDeviceAddress.Timestopst26
                };

                // Read idle time from all stations
                foreach (var register in stationIdleRegisters)
                {
                    try
                    {
                        var result = await plcService.ReadAsync(register);
                        if (result.IsSuccess)
                        {
                            var stationIdleTime = Convert.ToInt32(result.Value);
                            totalIdleTimeSeconds += stationIdleTime;
                            Console.WriteLine($"Dashboard: {register} = {stationIdleTime} seconds");
                        }
                        else
                        {
                            Console.WriteLine($"Dashboard: Failed to read {register}: {result.Message}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Dashboard: Error reading {register}: {ex.Message}");
                    }
                }

                var totalIdleTime = TimeSpan.FromSeconds(totalIdleTimeSeconds);
                Console.WriteLine($"Dashboard: Total idle time from PLC: {totalIdleTime.TotalMinutes:F1} minutes");
                return totalIdleTime;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Dashboard: Error reading idle time from PLC: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Updates monthly idle time by saving today's data and recalculating monthly total
        /// </summary>
        private async Task UpdateMonthlyIdleTimeAsync(TimeSpan? currentDailyIdleTime)
        {
            try
            {
                if (!currentDailyIdleTime.HasValue) return;

                // Save today's idle time to database
                await _databaseService.SaveDailyIdleTimeSummaryAsync(DateTime.Today, currentDailyIdleTime.Value);

                // Recalculate monthly idle time from database
                var monthlyIdleTime = await _databaseService.GetMonthlyIdleTimeFromDatabaseAsync();

                Console.WriteLine($"Dashboard: Monthly idle time calculated: {monthlyIdleTime.TotalHours:F1} hours");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Dashboard: Error updating monthly idle time: {ex.Message}");
            }
        }

        // Idle time methods moved to DatabaseService

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "Bạn có chắc chắn muốn đăng xuất?",
                "Xác nhận đăng xuất",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                UserSession.Logout();

                // Đóng MainWindow và mở LoginWindow
                LoginWindow loginWindow = new();
                loginWindow.Show();
                this.Close();
            }
        }

        /// <summary>
        /// Get PlanViewModel for dashboard charts
        /// </summary>
        /// <returns>PlanViewModel instance or null if not initialized</returns>
        public PlanViewModel GetPlanViewModel()
        {
            return _planViewModel;
        }

        /// <summary>
        /// Get PlcConnectionManager for dashboard charts
        /// </summary>
        /// <returns>PlcConnectionManager instance or null if not initialized</returns>
        public PlcConnectionManager GetPlcManager()
        {
            var logMessage = $"[MainWindow] GetPlcManager() called, returning: {(_plcManager != null ? _plcManager.GetType().Name : "null")}";
            Console.WriteLine(logMessage);
            _logger?.LogInfo(logMessage);
            return _plcManager;
        }



    }
}