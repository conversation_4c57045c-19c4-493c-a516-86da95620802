﻿<UserControl x:Class="ZoomableApp.Views.StopTimesChartControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp.Views"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border CornerRadius="8" Padding="15" Height="Auto">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="122*"/>
                <ColumnDefinition Width="263*"/>
            </Grid.ColumnDefinitions>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <DockPanel Grid.Row="0" Margin="0,0,0,10" Grid.ColumnSpan="2">
                <StackPanel DockPanel.Dock="Left" Orientation="Horizontal">
                    <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"
                          Fill="#E74C3C" Width="auto" Height="auto" VerticalAlignment="Top"/>
                    <TextBlock Text="Tổng số lần dừng máy" Foreground="White" FontSize="16" FontWeight="Bold" VerticalAlignment="Top"/>
                </StackPanel>
                <TextBlock Text="{Binding LastUpdated, StringFormat='Cập nhật: {0}'}"
                          Foreground="#BDC3C7" FontSize="11" DockPanel.Dock="Right"
                          HorizontalAlignment="Right" VerticalAlignment="Center"/>
            </DockPanel>

            <!-- Summary Info -->
            <Border Background="#34495E" CornerRadius="4" Padding="8" Margin="0,28,0,0" Grid.ColumnSpan="2" Grid.RowSpan="2">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="Tổng số lần dừng: " Foreground="#BDC3C7" FontSize="12"/>
                    <TextBlock Text="{Binding TotalStopTimes}" Foreground="#E74C3C" FontSize="12" FontWeight="Bold"/>
                    <TextBlock Text=" lần" Foreground="#BDC3C7" FontSize="12"/>
                </StackPanel>
            </Border>

            <!-- Chart -->
            <lvc:CartesianChart Grid.Row="1"
                               Series="{Binding Series}"
                               XAxes="{Binding XAxes}"
                               Background="Transparent" Grid.ColumnSpan="2" Grid.RowSpan="3"/>

            <!-- Footer -->
            <!-- <TextBlock Grid.Row="3"
                      Text="Top 10 trạm có nhiều lần dừng nhất"
                      Foreground="#95A5A6"
                      FontSize="10"
                      HorizontalAlignment="Left"
                      Margin="65,5,0,0" Grid.Column="1"/> -->
        </Grid>
    </Border>
</UserControl>
