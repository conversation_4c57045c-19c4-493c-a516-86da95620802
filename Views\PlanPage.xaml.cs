using System.Windows.Controls;
using ZoomableApp.ViewModels;
using ZoomableApp.Services;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for PlanPage.xaml
    /// </summary>
    public partial class PlanPage : UserControl
    {
        public PlanPage()
        {
            InitializeComponent();

            // Don't set DataContext here - it will be set by MainWindow after ServiceContainer is initialized
            // this.DataContext will be set in MainWindow.SetupPageDataContexts()
        }
    }
}
