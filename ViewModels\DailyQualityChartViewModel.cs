﻿using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Daily Quality Chart (Panel 2 - Right)
    /// Hi<PERSON>n thị OK/NG/Rework ca hiện tại dưới dạng pie chart
    /// </summary>
    public class DailyQualityChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";
        public event PropertyChangedEventHandler? PropertyChanged;
        public ObservableCollection<ISeries> Series { get; set; } = new();
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }
        private int _okQuantity;
        public int OkQuantity
        {
            get => _okQuantity;
            set
            {
                if (_okQuantity != value)
                {
                    _okQuantity = value;
                    OnPropertyChanged(nameof(OkQuantity));
                }
            }
        }
        private int _ngQuantity;
        public int NgQuantity
        {
            get => _ngQuantity;
            set
            {
                if (_ngQuantity != value)
                {
                    _ngQuantity = value;
                    OnPropertyChanged(nameof(NgQuantity));
                }
            }
        }
        private int _reworkQuantity;
        public int ReworkQuantity
        {
            get => _reworkQuantity;
            set
            {
                if (_reworkQuantity != value)
                {
                    _reworkQuantity = value;
                    OnPropertyChanged(nameof(ReworkQuantity));
                }
            }
        }
        private int _totalQuantity;
        public int TotalQuantity
        {
            get => _totalQuantity;
            set
            {
                if (_totalQuantity != value)
                {
                    _totalQuantity = value;
                    OnPropertyChanged(nameof(TotalQuantity));
                }
            }
        }

        private double _qualityRate;
        public double QualityRate
        {
            get => _qualityRate;
            set
            {
                if (_qualityRate != value)
                {
                    _qualityRate = value;
                    OnPropertyChanged(nameof(QualityRate));
                }
            }
        }
        public string CurrentDay { get; private set; } = "";
        public DailyQualityChartViewModel()
        {
            _mockDataService = new MockDashboardDataService();
            CurrentDay = DateTime.Now.ToString("dd/MM/yyyy");
            InitializeTimer();
            LoadDataAsync();
        }
        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }
        private async Task LoadDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Kiểm tra PLC mode
                    var plcMode = ConfigurationService.GetPlcMode();

                    if (plcMode == PlcMode.Mock)
                    {
                        LoadMockData();
                    }
                    else
                    {
                        // TODO: Load real data from PLC/Database
                        // Fallback to mock data for now
                        LoadMockData();
                    }

                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ShiftQualityChart: Error loading data: {ex.Message}");
                    LoadMockData(); // Fallback
                }
            });
        }
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void LoadMockData()
        {
            var qualityData = _mockDataService.GetMockTodayQualityData(); // Today data
            OkQuantity = qualityData.OK;
            NgQuantity = qualityData.NG;
            ReworkQuantity = qualityData.Rework;
            TotalQuantity = OkQuantity + NgQuantity + ReworkQuantity;
            QualityRate = TotalQuantity > 0 ? (double)OkQuantity / TotalQuantity * 100 : 0;
            Series.Clear();
            // Tạo pie chart cho OK/NG/Rework
            if (OkQuantity > 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { OkQuantity },
                    Name = $"OK ({OkQuantity})",
                    Fill = new SolidColorPaint(SKColors.Green),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });
            }

            if (NgQuantity > 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { NgQuantity },
                    Name = $"NG ({NgQuantity})",
                    Fill = new SolidColorPaint(SKColors.Red),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });
            }

            if (ReworkQuantity > 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { ReworkQuantity },
                    Name = $"Rework ({ReworkQuantity})",
                    Fill = new SolidColorPaint(SKColors.Orange),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });
            }

            // Nếu không có data, hiển thị placeholder
            if (TotalQuantity == 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { 1 },
                    Name = "Chưa có dữ liệu",
                    Fill = new SolidColorPaint(SKColors.Gray),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => "No Data"
                });
            }

            // Update properties for binding
            OnPropertyChanged(nameof(OkQuantity));
            OnPropertyChanged(nameof(NgQuantity));
            OnPropertyChanged(nameof(ReworkQuantity));
            OnPropertyChanged(nameof(TotalQuantity));
            OnPropertyChanged(nameof(QualityRate));

            Console.WriteLine($"ShiftQuality: OK={OkQuantity}, NG={NgQuantity}, Rework={ReworkQuantity}, Rate={QualityRate:F1}%");
        }

    }
}
