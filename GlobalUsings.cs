// Global using directives to resolve ambiguous references
global using System.Windows.Controls;
global using System.Windows.Media;
global using System.Windows.Input;
global using System.Windows;
global using Point = System.Windows.Point;
global using Color = System.Windows.Media.Color;
global using Brush = System.Windows.Media.Brush;
global using Brushes = System.Windows.Media.Brushes;
global using Panel = System.Windows.Controls.Panel;
global using UserControl = System.Windows.Controls.UserControl;
global using Application = System.Windows.Application;
global using MouseEventArgs = System.Windows.Input.MouseEventArgs;
global using KeyEventArgs = System.Windows.Input.KeyEventArgs;
global using Button = System.Windows.Controls.Button;
global using MessageBox = System.Windows.MessageBox;
global using OpenFileDialog = Microsoft.Win32.OpenFileDialog;
global using Size = System.Windows.Size;
global using Cursors = System.Windows.Input.Cursors;
