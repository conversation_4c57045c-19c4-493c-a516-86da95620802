﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace ZoomableApp.Models
{
    public class ErrorNotification : INotifyPropertyChanged
    {
        public string ErrorId { get; } // Unique ID or PLC address string
        public string Message { get; }
        public DateTime Timestamp { get; }

        private bool _isNew;
        public bool IsNew
        {
            get => _isNew;
            set
            {
                if (_isNew != value)
                {
                    _isNew = value;
                    OnPropertyChanged(nameof(IsNew));
                }
            }
        }

        public ErrorNotification(string errorId, string message, bool isNew = true)
        {
            ErrorId = errorId;
            Message = message;
            Timestamp = DateTime.Now;
            IsNew = isNew;
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public override string ToString() // For ListBox display
        {
            return $"{Timestamp:HH:mm:ss} - [{ErrorId}] {Message}{(IsNew ? " (New)" : "")}";
        }
    }
}
