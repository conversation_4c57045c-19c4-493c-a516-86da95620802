<UserControl x:Class="ZoomableApp.SharedControls.FaultDisplayControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="60" d:DesignWidth="300">
    
    <Border x:Name="FaultBorder" 
            Background="#FF6B6B" 
            BorderBrush="#FF4444" 
            BorderThickness="2" 
            CornerRadius="5"
            Padding="10,5"
            Opacity="0.9">
        <StackPanel Orientation="Vertical">
            <TextBlock x:Name="FaultCodeText" 
                       Text="ERROR CODE" 
                       FontWeight="Bold" 
                       FontSize="12" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
            <TextBlock x:Name="FaultMessageText" 
                       Text="Error Message" 
                       FontSize="10" 
                       Foreground="White" 
                       TextWrapping="Wrap"
                       HorizontalAlignment="Center"
                       Margin="0,2,0,0"/>
            <TextBlock x:Name="FaultStationsText" 
                       Text="Stations: 1, 2, 3" 
                       FontSize="9" 
                       Foreground="#FFE6E6" 
                       HorizontalAlignment="Center"
                       Margin="0,2,0,0"/>
        </StackPanel>
    </Border>
</UserControl>
