﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace ZoomableApp.Converters
{
    [ValueConversion(typeof(bool), typeof(bool))]
    public class InverseBooleanConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (targetType != typeof(bool) && targetType != typeof(bool?)) // Hỗ trợ cả nullable bool
                throw new InvalidOperationException("The target must be a boolean");

            if (value is bool boolValue)
            {
                return !boolValue;
            }
            return false; // Hoặc true, tùy theo logic bạn muốn cho giá trị null hoặc không phải bool
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (targetType != typeof(bool) && targetType != typeof(bool?))
                throw new InvalidOperationException("The target must be a boolean");

            if (value is bool boolValue)
            {
                return !boolValue; // ConvertBack cũng đảo ngược
            }
            return false;
        }
    }
}
