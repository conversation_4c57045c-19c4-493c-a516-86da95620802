<PERSON><PERSON> chế Zoom/Pan (MainWindow & ViewportBorder, ZoomPanCanvas):
<PERSON><PERSON><PERSON> năng tái sử dụng: RẤT CAO.
ViewportBorder và ZoomPanCanvas là một cơ chế zoom/pan chung. Bạn có thể dễ dàng sao chép và dán phần XAML và code-behind liên quan vào bất kỳ cửa sổ hoặc UserControl nào khác nếu bạn muốn chức năng zoom/pan cho một khu vực hiển thị khác. Nó không phụ thuộc vào nội dung bên trong ZoomPanCanvas.

Cơ chế Thông báo lỗi (Bell, Popup, Toasts):
<PERSON><PERSON><PERSON> năng tái sử dụng: CAO.
ErrorNotification (Model): <PERSON><PERSON> thể tái sử dụng nguyên vẹn trong bất kỳ ứng dụng WPF nào cần hiển thị thông báo lỗi.
ToastNotificationControl (UserControl): <PERSON><PERSON> một UserControl chung để hiển thị thông báo toast. <PERSON><PERSON> thể tùy chỉnh màu sắc, icon, và dễ dàng dùng lại ở các ứng dụng khác.
Logic quản lý lỗi (AddNewError, AllErrors, NewErrorCount, ShowToastNotification trong MainWindow): Phần này có thể được đóng gói vào một ErrorNotificationViewModel hoặc một NotificationService riêng biệt. Nếu làm được điều đó, bạn chỉ cần tạo một instance của ViewModel/Service này và binding/gọi các phương thức từ bất kỳ cửa sổ nào bạn muốn hiển thị thông báo.
XAML cho Bell, Badge, Popup: Có thể đóng gói vào một NotificationBarControl.xaml (UserControl) và sau đó đặt nó ở bất kỳ đâu trong ứng dụng.

Cơ chế Đăng nhập (LoginWindow):
Khả năng tái sử dụng: TRUNG BÌNH CAO.
LoginWindow.xaml/.cs là một cửa sổ độc lập. Bạn có thể tái sử dụng nó trong các ứng dụng WPF khác cần màn hình đăng nhập đơn giản.
Phần logic kiểm tra mã RFID là placeholder, khi thay thế bằng logic xác thực thực tế (database, API), nó vẫn giữ được tính tái sử dụng cao cho lớp đăng nhập.

Các Enum (StationType, TestResultStatus, DeviceOperationalStatus, PlcDeviceAddress, PlcDataType):
Khả năng tái sử dụng: CAO.
Các Enum này định nghĩa các trạng thái, loại, địa chỉ phổ biến trong ngữ cảnh tự động hóa và PLC. Chúng có thể được sử dụng lại trong bất kỳ project tự động hóa nào khác.

StationControl.xaml/.cs (UserControl hiển thị trạm):
Khả năng tái sử dụng: CAO.
Là một UserControl độc lập, có thể tái sử dụng trong các layout HMI khác nhau mà cần hiển thị các "trạm" với trạng thái tương tự. Các Dependency Property và logic UpdateStationDisplayBrush làm cho nó linh hoạt.

ProductItem.cs (Model sản phẩm):
Khả năng tái sử dụng: CAO.
Là một model dữ liệu thuần túy cho sản phẩm. Có thể dùng trong bất kỳ hệ thống quản lý sản phẩm nào.
ProductVisualControl.xaml/.cs (UserControl hiển thị sản phẩm trên dây chuyền):
Khả năng tái sử dụng: CAO.
Là một UserControl hiển thị trực quan cho một sản phẩm. Nó hoạt động độc lập và có thể được sử dụng trên bất kỳ Canvas nào để mô phỏng chuyển động.

PlcRegisterInfo.cs (Model thông tin thanh ghi):
Khả năng tái sử dụng: RẤT CAO.
Một model trừu tượng hóa thông tin về một thanh ghi PLC, độc lập với nhà sản xuất PLC. Rất hữu ích trong bất kỳ dự án PLC nào.

PlcDataMapper.cs (Service chuyển đổi dữ liệu PLC):
Khả năng tái sử dụng: RẤT CAO.
Là một lớp tiện ích tĩnh, nó xử lý việc chuyển đổi kiểu dữ liệu giữa PLC và ứng dụng. Đây là một thành phần cốt lõi có thể được đưa vào bất kỳ thư viện giao tiếp PLC nào.
IPlcService.cs, ConnectionResult, PlcReadResult, PlcWriteResult (Interface và các model kết quả):
Khả năng tái sử dụng: RẤT CAO.

Đây là một interface chuẩn hóa việc giao tiếp với PLC, độc lập với công nghệ (HSL, MX Component, OPC UA,...). Bất kỳ service PLC cụ thể nào cũng có thể implement interface này, cho phép bạn dễ dàng hoán đổi công nghệ PLC sau này mà không ảnh hưởng đến phần còn lại của ứng dụng.

MitsubishiPlcService.cs (Implement HSLCommunication):
Khả năng tái sử dụng: TRUNG BÌNH CAO.
Implement cụ thể cho PLC Mitsubishi sử dụng HSLCommunication. Bạn có thể sử dụng lại lớp này trong bất kỳ ứng dụng nào cần giao tiếp với Mitsubishi PLC qua HSL. Tuy nhiên, nó là một implement cụ thể, không phải là một thành phần chung cho mọi loại PLC.

PlcConnectionManager.cs (Quản lý nhiều kết nối PLC):
Khả năng tái sử dụng: CAO.
Lớp này trừu tượng hóa việc quản lý và điều phối nhiều kết nối PLC (IPlcService). Bạn có thể sử dụng lại nó trong bất kỳ ứng dụng nào cần quản lý một "farm" PLC. Nó có thể được cải tiến để linh hoạt hơn trong việc thêm/xóa PLC trong runtime nếu cần.

DataAggregatorService.cs (Tổng hợp dữ liệu không đồng bộ):
Khả năng tái sử dụng: RẤT CAO.
Lớp này giải quyết bài toán tổng hợp dữ liệu từ nhiều nguồn không đồng bộ dựa trên một ID chung. Đây là một pattern rất hữu ích trong nhiều lĩnh vực ngoài tự động hóa (ví dụ: xử lý sự kiện, data warehousing).

SimulatedDatabaseService.cs và IDatabaseService.cs:
Khả năng tái sử dụng: RẤT CAO.
IDatabaseService là một interface mẫu cho tầng persistence. SimulatedDatabaseService là một implement giả lập. Bạn có thể dùng lại IDatabaseService trong mọi dự án cần tách biệt logic lưu trữ dữ liệu.

PlcConnectionUIData.cs và RelayCommand.cs:
Khả năng tái sử dụng: RẤT CAO.
PlcConnectionUIData là một ViewModel đơn giản, nhưng pattern này và việc sử dụng INotifyPropertyChanged là rất chung và có thể tái sử dụng.
RelayCommand là một implement chuẩn của ICommand cho MVVM, được tái sử dụng rộng rãi trong mọi ứng dụng WPF.

Những điểm có thể cải thiện để tăng khả năng tái sử dụng hơn nữa (nâng cao):
Dependency Injection (DI): Sử dụng một framework DI (như Microsoft.Extensions.DependencyInjection, Autofac, Unity) để quản lý việc tạo và cung cấp các service (như IPlcService, IDatabaseService, DataAggregatorService). Điều này làm cho code dễ test hơn, dễ thay đổi implement, và giảm sự phụ thuộc cứng giữa các lớp.
MVVM Framework: Sử dụng một MVVM framework (như MVVM Light, Caliburn.Micro, Prism) sẽ giúp quản lý MainWindow và các ViewModel phức tạp hơn một cách có cấu trúc hơn, làm cho các thành phần ViewModel có thể tái sử dụng dễ dàng hơn.
Tách biệt nhỏ hơn: Một số logic hiện đang nằm trong MainWindow.xaml.cs (ví dụ: InitializeErrorSimulation, PlcDataReaderTimer_Tick) có thể được di chuyển vào các ViewModel hoặc service chuyên biệt hơn để tăng tính mô-đun hóa.

Giờ ta sẽ chuyển sang làm trang báo cáo :
- Giao diện : dòng đầu tiên được chia đôi, bên trái là các nút : Sản lượng, Thao tác chậm, Đo thao tác, Tổng hợp tháng, Lịch sử lỗi. Bên phải có chọn ngày "Từ" ..." Đến"...,  Chọn ca làm việc(dropdown) , nút Tìm kiếm , nút Xuất excel. Từ dưới trở xuống là data table sẽ hiển thị mặc định sản lượng của ca hiện tại. sau đó nó sẽ thay đổi tùy theo nút bấm + filter ngày tháng. 
- Nghiệp vụ : 
+ dự vào file plc_map.js (PLC_MainLine) hãy đăng kí các thanh ghi trong PlcEnum (PlcDeviceAddress)
+ Sau khi kết nối PLC thành công thì sẽ đọc dữ liệu các thanh ghi cần thiết phục vụ lưu dữ liệu theo yêu cầu bên dưới 
+ Bảng để lưu dữ liệu : 
CREATE TABLE IF NOT EXISTS ProductionData (
    Timestamp VARCHAR(255), -- Thời gian lưu bản ghi
    Station VARCHAR(255), -- Trạm (ST1-ST18)
    Product_OK INT, -- Số sản phẩm đạt chất lượng (D1000)
    Product_NG INT, -- Số sản phẩm không đạt chất lượng (D1002)
    Product_Total INT, -- Tổng số sản phẩm (D1004)
    Time_Complete REAL, -- Thời gian hoàn thành thao tác (D72-D79)
    Time_Delay INT, -- Thời gian trễ (D1080-D1114)
    Time_Stop INT, -- Tổng thời gian dừng (D1132-D1182)
    Number_Stop INT, -- Số lần dừng (D1190-D1215)
    Error_Code VARCHAR(255), -- Mã lỗi (M0, X06-X1610)
    Error_Text VARCHAR(255) -- Mô tả lỗi
);
+ Vào các thời điểm chuyển ca phải lưu dữ liệu lúc đó : 
Ca sáng: 06:00:00-11:00:00.
Ca chiều: 14:00:00-22:00:00.
Ca hành chính: 08:00:00-17:00:00.
Ca đêm: 22:00:00-06:00:00 (hôm sau).
+ Ngoài ra hãy lưu dữ liệu khi có những điều kiện sau : Có lỗi mới xuất hiện, khi được yêu cầu hiển thị thời gian thao tác chậm của công nhân tại 18 vị trí (yêu cầu bởi nút Thao tác chậm), khi được yêu cầu đo thao tác 18 vị trí làm việc trong 10 lần liên tiếp (10 bản ghi mỗi trạm từ plc, yêu cầu bởi nút Đo thao tác), còn những điều kiện khác sẽ thêm sau
+ Các file xuất ra được lưu trong thư mục cài đặt (hiện tại sẽ cài ở file config là E:\Project-Dat\PANA), file sinh ra có cầu trúc  DDMMYY_Loại báo cáo.xlsx 
+ hãy nhớ làm đồng bộ giao diện với những trang khác. phần nghiệp vụ nên được chạy ngay từ khi đăng nhập vào trang chủ + điều kiện kết nối thành công plc.


Next quest :
✓ Thêm 1 số setting vào trang settings (thêm cài đặt file excel số giờ được phép offline trong tháng - chỉnh sửa logic tại trang chủ để load lên chart)
✓ Kiểm tra lại hệ thống log, log đang không hoạt động
✓ Tự định nghĩa enum rồi đọc giá trị từ enum đó để cập nhật trạng thái lên trang chủ (các trạng thái của hệ thống gồm gì?)
✓ Ở trang dashboard,phần kế hoạch hôm nay, cho phép người dùng chỉnh sửa Model hiện tại > user nhập model nhấn enter > lưu model vào biến để sử dụng sau này.
✓ Thiết kế lại trang dashboard thành 2 phần: 1 là mainline như hiện tại, 1 trang khác là inspection sẽ có layout tương tự nhưng khác thông tin (thông tin này tùy chỉnh sau, bây giờ sẽ để là "maintaining"), việc load mainline hay inspection sẽ dựa vào file config chứ không dựa vào selectbox như hiện tại nữa
✓ Về việc thông báo lỗi trên layout -> cần phương án
- Làm thêm 1 mini app có thể bật bằng 1 nút nằm trong phần cài đặt plc, app này sẽ giả lập tín hiệu của PLC phục vụ test chương trình khi trong mode "Mock"
- Gộp các file config vào 1 chỗ (folder config) và check ảnh hưởng

- Thêm config về phần mô phỏng chuyển động, cài đặt thời gian chuyền dịch chuyển.
- Product code trên Product control chính là Model hiện tại, sửa logic để lấy model hiện tại hiển thị lên Product control
- Logic đọc dữ liệu từ thanh ghi lưu dữ liệu vào database nằm tại mainwindow file cs , hàm PlcDataReaderTimer_Tick.
- Plans trong DailyPlanActualChartControl của cả 2 layout phải được truyền vào, nó được tính bằng cách tính tổng Quantity trong ngày hoặc tháng ở chart monthly plans từ mainwindow (đã lấy được danh sách plan trong ngày trước đó) rồi đổ dữ liệu vào chart này. (hoặc bạn có cách làm ngắn hơn? logic gọn hơn?)

mem: 
# Project Description
- User is developing a zoomable layout app with plans for additional features, currently working on implementing a sidebar in the main window.
- User prefers free/open-source tools over paid/licensed ones and is open to custom implementations when licensing becomes an issue.
- User prefers consistent implementation patterns across the codebase, specifically wants uniform file handling approaches (should use same Excel processing method in both PlanViewModel and MaintenanceViewModel rather than mixing .xlsx and .csv handling).

# UI/UX Preferences
- User prefers a fixed header with a sidebar button, page title centered, and a bell icon.
- User wants the layout selector and reset button moved below, along with zoom/pan controls.
- User prefers better-designed close buttons over simple 'X' text.
- User wants sidebar tabs to navigate to different pages with placeholder 'Maintaining' content for future development.
- User prefers sidebar menu to be centered relative to the entire sidebar width, not just the remaining space after other elements.
- User wants larger font sizes for better readability.
- User prefers consolidating all .md files into a single Readme.md file, extending it with summary information instead of creating separate summary files.
- User prefers separating pages into ViewModel folder instead of putting everything in MainWindow.
- User prefers sidebar to show full content when expanded.
- User prefers consistent modal designs across pages.
- User prefers shorter button text (e.g., 'Thêm mới' vs 'Thêm bản ghi mới').
- Remove PLC connection display.
- User prefers toast notifications positioned at bottom-right of main content area (not header).
- User prefers confirm buttons to always be enabled with validation notifications rather than being disabled when conditions aren't met.
- User prefers 3D button styling with depth and shadow effects over flat 2D design, and wants proper text fitting within button boundaries.

# Login System
- User wants a login system with 2 tabs (RFID card and account login).
- Uses SQLite database with users table (id, username, password, role, fullname, rfid) in Data/panaDB.
- Includes shift selection (4 shifts: administrative 8-17h, morning 6-11h, afternoon 14-22h, night 22-6h).
- User prefers to use existing panaDB.db file in Data folder rather than auto-creating database with sample data.
- User info should be passed to MainWindow sidebar showing avatar, fullname, and shift details.
- User wants RFID authentication to compare against 'rfid' field in users table.

# Plan Page
- Plan page should auto-load Excel files, extract columns (header + content) to display in datatable, with file path stored in config file (example: E:\Project-Dat\test.xlsx).
- User wants Excel data to preserve leading zeros (0530 not 530).
- DataGrid should have minimum size with scrollbars when app is small and auto-expand when larger.
- Plan page split into monthly plan (top) and daily plan extracted from monthly data based on current date (bottom).
- User wants daily plan section with color coding (yellow=current, green=completed, blue=selected), and interactive buttons for done/next actions with refresh functionality.

# Homepage
- Homepage layout should be 1/3 for zoom/pan area and 2/3 for daily plans, and wants to keep div3 and div4 sections for future development.
- Homepage daily plan should only display data that has been processed/analyzed from the Plan page for today.
- Homepage refresh button should only update data from Plan page to Homepage.

# Maintenance Page
- User wants a Maintenance page with maintenance reminder system, Excel-based maintenance list management, maintenance workflow with result input/confirmation, automatic next maintenance calculation, history tracking with date range selection, and Excel export functionality with app-consistent color scheme.
- Maintenance page layout: 10% header / 20% filter section / 70% data table.
- Excel integration from config path.
- Automatic maintenance reminders 2 days before due date with 60-minute intervals.
- Specific date format mm/dd/YYYY for Excel files.
- User wants current user name for MaintainedBy field.
- User wants automatic Excel saving after maintenance completion.
- User wants separate Excel export functionality for current data.
- User prefers maintenance data table to be read-only for display purposes only, wants table design consistent with Plan page, and expects maintenance reminder warnings when equipment is due for maintenance.

# Security
- User prefers password-based role authentication for data modification operations across management pages (máy, OEE, thời gian dừng).

# PLC Integration
- PLC_MainLine has detailed register mappings including D registers (D0-D1499 for status, timing, product counts, station completion times, error checking) and M registers (M0-M4999 for system status, manual/auto modes, alarms) that need to be configured in plc_map.json.

# Post-Request Workflow
- User wants a post-request summary workflow: after completing each request, summarize errors made and solutions, then save this analysis to a memory.txt file for future reference.

### Lưu ý quan trọng
- Các thời điểm lưu dữ liệu database :
+ Khi vừa đăng nhập 
+ Trước khi chuyển ca 5 phút 
+ Khi mới vào ca
+ Khi check các thanh ghi có lỗi 
+ Ngoài ra cập nhật mỗi 1 tiếng 1 lần ? có nên không?
- Check lại toàn bộ logic của mainwindow.xaml.cs : có method nào đang thừa, không ref tới phương thức nào cả, logic nào đang không sử dụng? Logic nào có thể sử dụng được cho task hiện tại ?
- Không lưu database bằng Tiếng Việt, chỉ lưu English
- Logic check Actual/Plan : 
+ Plan : Mỗi ca sẽ check plan như hiện tại. 
+ Actual : Khi đã lưu các bản ghi vào đầu ca, cuối ca rồi ta có dữ liệu của ca trước, vậy muốn tính ca tiếp theo sẽ lấy Acutal của toàn bộ ngày trừ đi ca trước đó.
+ Tối ưu để tránh tính toán lại nhiều lần?
- Service lưu database đang để bừa bãi , rải rác ở các service khác, thậm chí là ở mainwindow code-behide :
+ Chuyển đổi SimulatedDatabaseService về DatabaseService
+ Bổ sung IDatabaseService?
+ Đưa hết logic liên quan tới truy vấn database về service trên
+ Các service khác cũng chứa logic truy vấn database , đưa hết về DatabaseService ,UserService cũng create câu lệnh và truy vấn trực tiếp vào DB > Check lại toàn bộ , ngoài DatabaseService không được có service nào truy vấn vào database
