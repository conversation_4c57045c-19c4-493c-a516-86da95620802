using System.Windows;
using System.Windows.Controls;
using ZoomableApp.Services;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Views
{
    public partial class UsersPage : UserControl
    {
        private UsersViewModel? _viewModel;
        private readonly IRfidReaderService _rfidReaderService;
        public UsersPage()
        {
            InitializeComponent();
            _viewModel = new UsersViewModel();
            DataContext = _viewModel;

            _rfidReaderService = new OmnikeyRfidReaderService();
            _rfidReaderService.CardScanned += RfidReaderService_CardScanned;

            Loaded += async (_, __) => await _rfidReaderService.StartListeningAsync();
            Unloaded += (_, __) => _rfidReaderService.Dispose();
        }

        private void NewUserPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_viewModel != null && sender is PasswordBox passwordBox)
            {
                _viewModel.NewUser.Password = passwordBox.Password;
            }
        }

        private void EditUserPasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
        {
            if (_viewModel != null && sender is PasswordBox passwordBox)
            {
                // Only update password if user entered something
                if (!string.IsNullOrEmpty(passwordBox.Password))
                {
                    _viewModel.EditingUser.Password = passwordBox.Password;
                }
            }
        }
        private void RfidReaderService_CardScanned(string code)
        {
            if (_viewModel?.IsAddUserDialogOpen == true)
            {
                Dispatcher.Invoke(() =>
                {
                    _viewModel.NewUser.Rfid = code;
                });
            }

            if (_viewModel?.IsEditUserDialogOpen == true)
            {
                Dispatcher.Invoke(() =>
                {
                    _viewModel.EditingUser.Rfid = code;
                });
            }
        }
    }
}
