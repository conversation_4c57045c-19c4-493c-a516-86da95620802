﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media;
using System.Windows.Threading;

namespace ZoomableApp.SharedControls
{
    class ToastNotificationPanel : ContentControl
    {
        #region Singleton

        private static ToastNotificationPanel _instance;
        private static readonly object _lock = new object();

        public static ToastNotificationPanel Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new ToastNotificationPanel();
                        }
                    }
                }
                return _instance;
            }
        }

        #endregion

        #region Dependency Properties

        public static readonly DependencyProperty CornerRadiusProperty =
            DependencyProperty.Register("CornerRadius", typeof(double), typeof(ToastNotificationPanel),
                new PropertyMetadata(10.0));

        public static readonly DependencyProperty DurationProperty =
            DependencyProperty.Register("Duration", typeof(int), typeof(ToastNotificationPanel),
                new PropertyMetadata(3000));

        public static readonly DependencyProperty PositionProperty =
            DependencyProperty.Register("Position", typeof(ToastPosition), typeof(ToastNotificationPanel),
                new PropertyMetadata(ToastPosition.BottomRight));

        public static readonly DependencyProperty AnimationDurationProperty =
            DependencyProperty.Register("AnimationDuration", typeof(double), typeof(ToastNotificationPanel),
                new PropertyMetadata(300.0));

        public static readonly DependencyProperty EnableShadowProperty =
            DependencyProperty.Register("EnableShadow", typeof(bool), typeof(ToastNotificationPanel),
                new PropertyMetadata(true));

        public static readonly DependencyProperty ShadowDepthProperty =
            DependencyProperty.Register("ShadowDepth", typeof(double), typeof(ToastNotificationPanel),
                new PropertyMetadata(5.0));

        public static readonly DependencyProperty ShadowOpacityProperty =
            DependencyProperty.Register("ShadowOpacity", typeof(double), typeof(ToastNotificationPanel),
                new PropertyMetadata(0.3));

        public static readonly DependencyProperty ShadowColorProperty =
            DependencyProperty.Register("ShadowColor", typeof(Color), typeof(ToastNotificationPanel),
                new PropertyMetadata(Colors.Black));

        public static readonly DependencyProperty SuccessBackgroundProperty =
            DependencyProperty.Register("SuccessBackground", typeof(Brush), typeof(ToastNotificationPanel),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(76, 175, 80))));

        public static readonly DependencyProperty ErrorBackgroundProperty =
            DependencyProperty.Register("ErrorBackground", typeof(Brush), typeof(ToastNotificationPanel),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(244, 67, 54))));

        public static readonly DependencyProperty WarningBackgroundProperty =
            DependencyProperty.Register("WarningBackground", typeof(Brush), typeof(ToastNotificationPanel),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(255, 193, 7))));

        public static readonly DependencyProperty InfoBackgroundProperty =
            DependencyProperty.Register("InfoBackground", typeof(Brush), typeof(ToastNotificationPanel),
                new PropertyMetadata(new SolidColorBrush(Color.FromRgb(33, 150, 243))));

        #endregion

        #region Properties

        /// <summary>
        /// Bán kính bo góc
        /// </summary>
        public double CornerRadius
        {
            get { return (double)GetValue(CornerRadiusProperty); }
            set { SetValue(CornerRadiusProperty, value); }
        }

        /// <summary>
        /// Thời gian hiển thị (ms)
        /// </summary>
        public int Duration
        {
            get { return (int)GetValue(DurationProperty); }
            set { SetValue(DurationProperty, value); }
        }

        /// <summary>
        /// Vị trí hiển thị
        /// </summary>
        public ToastPosition Position
        {
            get { return (ToastPosition)GetValue(PositionProperty); }
            set { SetValue(PositionProperty, value); }
        }

        /// <summary>
        /// Thời gian chuyển động (ms)
        /// </summary>
        public double AnimationDuration
        {
            get { return (double)GetValue(AnimationDurationProperty); }
            set { SetValue(AnimationDurationProperty, value); }
        }

        /// <summary>
        /// Bật/tắt đổ bóng
        /// </summary>
        public bool EnableShadow
        {
            get { return (bool)GetValue(EnableShadowProperty); }
            set { SetValue(EnableShadowProperty, value); }
        }

        /// <summary>
        /// Độ sâu của bóng đổ
        /// </summary>
        public double ShadowDepth
        {
            get { return (double)GetValue(ShadowDepthProperty); }
            set { SetValue(ShadowDepthProperty, value); }
        }

        /// <summary>
        /// Độ mờ của bóng đổ
        /// </summary>
        public double ShadowOpacity
        {
            get { return (double)GetValue(ShadowOpacityProperty); }
            set { SetValue(ShadowOpacityProperty, value); }
        }

        /// <summary>
        /// Màu sắc của bóng đổ
        /// </summary>
        public Color ShadowColor
        {
            get { return (Color)GetValue(ShadowColorProperty); }
            set { SetValue(ShadowColorProperty, value); }
        }

        /// <summary>
        /// Màu nền thông báo thành công
        /// </summary>
        public Brush SuccessBackground
        {
            get { return (Brush)GetValue(SuccessBackgroundProperty); }
            set { SetValue(SuccessBackgroundProperty, value); }
        }

        /// <summary>
        /// Màu nền thông báo lỗi
        /// </summary>
        public Brush ErrorBackground
        {
            get { return (Brush)GetValue(ErrorBackgroundProperty); }
            set { SetValue(ErrorBackgroundProperty, value); }
        }

        /// <summary>
        /// Màu nền thông báo cảnh báo
        /// </summary>
        public Brush WarningBackground
        {
            get { return (Brush)GetValue(WarningBackgroundProperty); }
            set { SetValue(WarningBackgroundProperty, value); }
        }

        /// <summary>
        /// Màu nền thông báo thông tin
        /// </summary>
        public Brush InfoBackground
        {
            get { return (Brush)GetValue(InfoBackgroundProperty); }
            set { SetValue(InfoBackgroundProperty, value); }
        }

        #endregion

        #region Private Fields

        private Panel _container;
        private Queue<ToastItem> _toastQueue;
        private List<Border> _activeToasts;
        private const int MaxVisibleToasts = 5;
        private const int ToastSpacing = 10;

        #endregion

        #region Constructor

        static ToastNotificationPanel()
        {
            DefaultStyleKeyProperty.OverrideMetadata(typeof(ToastNotificationPanel),
                new FrameworkPropertyMetadata(typeof(ToastNotificationPanel)));
        }

        private ToastNotificationPanel()
        {
            _toastQueue = new Queue<ToastItem>();
            _activeToasts = new List<Border>();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Hiển thị thông báo thành công
        /// </summary>
        /// <param name="message">Nội dung thông báo</param>
        /// <param name="duration">Thời gian hiển thị (ms)</param>
        public void ShowSuccess(string message, int? duration = null)
        {
            Show(message, ToastType.Success, duration);
        }

        /// <summary>
        /// Hiển thị thông báo lỗi
        /// </summary>
        /// <param name="message">Nội dung thông báo</param>
        /// <param name="duration">Thời gian hiển thị (ms)</param>
        public void ShowError(string message, int? duration = null)
        {
            Show(message, ToastType.Error, duration);
        }

        /// <summary>
        /// Hiển thị thông báo cảnh báo
        /// </summary>
        /// <param name="message">Nội dung thông báo</param>
        /// <param name="duration">Thời gian hiển thị (ms)</param>
        public void ShowWarning(string message, int? duration = null)
        {
            Show(message, ToastType.Warning, duration);
        }

        /// <summary>
        /// Hiển thị thông báo thông tin
        /// </summary>
        /// <param name="message">Nội dung thông báo</param>
        /// <param name="duration">Thời gian hiển thị (ms)</param>
        public void ShowInfo(string message, int? duration = null)
        {
            Show(message, ToastType.Info, duration);
        }

        /// <summary>
        /// Hiển thị thông báo
        /// </summary>
        /// <param name="message">Nội dung thông báo</param>
        /// <param name="type">Loại thông báo</param>
        /// <param name="duration">Thời gian hiển thị (ms)</param>
        public void Show(string message, ToastType type = ToastType.Info, int? duration = null)
        {
            if (string.IsNullOrEmpty(message))
                return;

            ToastItem toast = new ToastItem
            {
                Message = message,
                Type = type,
                Duration = duration ?? Duration
            };

            _toastQueue.Enqueue(toast);
            ProcessQueue();
        }

        /// <summary>
        /// Thiết lập container cho toast notification
        /// </summary>
        /// <param name="container">Panel chứa toast notification</param>
        public void SetContainer(Panel container)
        {
            _container = container;
        }

        #endregion

        #region Private Methods

        private void ProcessQueue()
        {
            while (_toastQueue.Count > 0 && _activeToasts.Count < MaxVisibleToasts && _container != null)
            {
                ToastItem toast = _toastQueue.Dequeue();
                ShowToast(toast);
            }
        }

        private void ShowToast(ToastItem toast)
        {
            // Tạo toast UI
            Border toastBorder = new Border
            {
                CornerRadius = new CornerRadius(CornerRadius),
                Padding = new Thickness(15, 10, 15, 10),
                Margin = new Thickness(10, 5, 10, 5),
                HorizontalAlignment = System.Windows.HorizontalAlignment.Right,
                VerticalAlignment = VerticalAlignment.Bottom,
                MaxWidth = 400,
                MinWidth = 250
            };

            // Thiết lập màu nền dựa trên loại thông báo
            switch (toast.Type)
            {
                case ToastType.Success:
                    toastBorder.Background = SuccessBackground;
                    break;
                case ToastType.Error:
                    toastBorder.Background = ErrorBackground;
                    break;
                case ToastType.Warning:
                    toastBorder.Background = WarningBackground;
                    break;
                case ToastType.Info:
                default:
                    toastBorder.Background = InfoBackground;
                    break;
            }

            // Thiết lập đổ bóng
            if (EnableShadow)
            {
                DropShadowEffect shadowEffect = new DropShadowEffect
                {
                    Color = ShadowColor,
                    Direction = 315,
                    ShadowDepth = ShadowDepth,
                    BlurRadius = ShadowDepth * 2,
                    Opacity = ShadowOpacity
                };

                toastBorder.Effect = shadowEffect;
            }

            // Tạo nội dung thông báo
            TextBlock textBlock = new TextBlock
            {
                Text = toast.Message,
                TextWrapping = TextWrapping.Wrap,
                Foreground = Brushes.White,
                FontWeight = FontWeights.Medium
            };

            toastBorder.Child = textBlock;

            // Tính toán vị trí dựa trên số lượng toast hiện tại
            CalculateToastPosition(toastBorder);

            // Thêm vào container và danh sách active
            _container.Children.Add(toastBorder);
            _activeToasts.Add(toastBorder);

            // Hiệu ứng hiển thị
            toastBorder.Opacity = 0;
            toastBorder.RenderTransform = new TranslateTransform(50, 0);

            DoubleAnimation opacityAnimation = new DoubleAnimation
            {
                From = 0,
                To = 1,
                Duration = TimeSpan.FromMilliseconds(AnimationDuration)
            };

            DoubleAnimation translateAnimation = new DoubleAnimation
            {
                From = 50,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(AnimationDuration)
            };

            toastBorder.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            ((TranslateTransform)toastBorder.RenderTransform).BeginAnimation(TranslateTransform.XProperty, translateAnimation);

            // Thiết lập timer để ẩn thông báo
            DispatcherTimer timer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(toast.Duration)
            };

            timer.Tick += (sender, e) =>
            {
                timer.Stop();
                HideToast(toastBorder);
            };

            timer.Start();
        }

        private void CalculateToastPosition(Border toastBorder)
        {
            // Tính toán margin bottom dựa trên số lượng toast hiện tại
            double bottomMargin = _activeToasts.Count * (60 + ToastSpacing); // 60 là chiều cao ước tính của toast
            toastBorder.Margin = new Thickness(10, 5, 10, bottomMargin + 10);
        }

        private void HideToast(Border toastBorder)
        {
            if (!_activeToasts.Contains(toastBorder))
                return;

            // Hiệu ứng ẩn
            DoubleAnimation opacityAnimation = new DoubleAnimation
            {
                From = 1,
                To = 0,
                Duration = TimeSpan.FromMilliseconds(AnimationDuration)
            };

            DoubleAnimation translateAnimation = new DoubleAnimation
            {
                From = 0,
                To = 50,
                Duration = TimeSpan.FromMilliseconds(AnimationDuration)
            };

            opacityAnimation.Completed += (s, args) =>
            {
                _container.Children.Remove(toastBorder);
                _activeToasts.Remove(toastBorder);

                // Cập nhật vị trí các toast còn lại
                UpdateToastPositions();

                // Xử lý queue tiếp theo
                ProcessQueue();
            };

            toastBorder.BeginAnimation(UIElement.OpacityProperty, opacityAnimation);
            ((TranslateTransform)toastBorder.RenderTransform).BeginAnimation(TranslateTransform.XProperty, translateAnimation);
        }

        private void UpdateToastPositions()
        {
            for (int i = 0; i < _activeToasts.Count; i++)
            {
                var toast = _activeToasts[i];
                double bottomMargin = i * (60 + ToastSpacing);

                // Animate to new position
                ThicknessAnimation marginAnimation = new ThicknessAnimation
                {
                    From = toast.Margin,
                    To = new Thickness(10, 5, 10, bottomMargin + 10),
                    Duration = TimeSpan.FromMilliseconds(200)
                };

                toast.BeginAnimation(FrameworkElement.MarginProperty, marginAnimation);
            }
        }

        #endregion
    }

    /// <summary>
    /// Loại thông báo
    /// </summary>
    public enum ToastType
    {
        Success,
        Error,
        Warning,
        Info
    }

    /// <summary>
    /// Vị trí hiển thị thông báo
    /// </summary>
    public enum ToastPosition
    {
        TopLeft,
        TopCenter,
        TopRight,
        BottomLeft,
        BottomCenter,
        BottomRight,
        Center
    }

    /// <summary>
    /// Thông tin thông báo
    /// </summary>
    internal class ToastItem
    {
        public string Message { get; set; }
        public ToastType Type { get; set; }
        public int Duration { get; set; }
    }
}
