using Microsoft.Data.Sqlite;
using System.IO;

namespace ZoomableApp.Services
{
    public static class DatabaseInitializer
    {
        public static async Task InitializeDatabaseAsync()
        {
            var dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            if (!Directory.Exists(dataDirectory))
            {
                Directory.CreateDirectory(dataDirectory);
            }

            var dbPath = Path.Combine(dataDirectory, "panaDB.db");
            var connectionString = $"Data Source={dbPath}";

            try
            {
                using var connection = new SqliteConnection(connectionString);
                await connection.OpenAsync();

                // Tạo bảng users
                var createTableCommand = connection.CreateCommand();
                createTableCommand.CommandText = @"
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER NOT NULL UNIQUE,
                        username TEXT,
                        password TEXT,
                        role TEXT,
                        fullname TEXT,
                        rfid TEXT,
                        PRIMARY KEY(id AUTOINCREMENT)
                    )";
                await createTableCommand.ExecuteNonQueryAsync();

                // Tạo bảng ProductionData cho báo cáo
                var createProductionDataCommand = connection.CreateCommand();
                createProductionDataCommand.CommandText = @"
                    CREATE TABLE IF NOT EXISTS ProductionData (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Timestamp VARCHAR(255) NOT NULL,
                        Station VARCHAR(255) NOT NULL,
                        Product_OK INTEGER DEFAULT 0,
                        Product_NG INTEGER DEFAULT 0,
                        Product_Total INTEGER DEFAULT 0,
                        Time_Complete REAL DEFAULT 0,
                        Time_Delay INTEGER DEFAULT 0,
                        Time_Stop INTEGER DEFAULT 0,
                        Number_Stop INTEGER DEFAULT 0,
                        Error_Code VARCHAR(255) DEFAULT '',
                        Error_Text VARCHAR(255) DEFAULT '',
                        WorkShift VARCHAR(255) DEFAULT '',
                        ReportType VARCHAR(255) DEFAULT 'Production',
                        CreatedBy VARCHAR(255) DEFAULT '',
                        Notes VARCHAR(255) DEFAULT ''
                    )";
                await createProductionDataCommand.ExecuteNonQueryAsync();

                // Thêm cột rfid nếu chưa có (cho database cũ)
                var addRfidColumnCommand = connection.CreateCommand();
                addRfidColumnCommand.CommandText = @"
                    ALTER TABLE users ADD COLUMN rfid TEXT";
                try
                {
                    await addRfidColumnCommand.ExecuteNonQueryAsync();
                }
                catch (SqliteException ex) when (ex.Message.Contains("duplicate column"))
                {
                    // Cột đã tồn tại, bỏ qua lỗi
                    Console.WriteLine("RFID column already exists");
                }

                // Kiểm tra xem đã có dữ liệu chưa
                var checkDataCommand = connection.CreateCommand();
                checkDataCommand.CommandText = "SELECT COUNT(*) FROM users";
                var count = Convert.ToInt32(await checkDataCommand.ExecuteScalarAsync());

                if (count == 0)
                {
                    // Thêm dữ liệu mẫu
                    var insertCommand = connection.CreateCommand();
                    insertCommand.CommandText = @"
                        INSERT INTO users (username, password, role, fullname, rfid) VALUES
                        ('admin', 'admin123', 'Administrator', 'Quản trị viên', '1234567890'),
                        ('operator1', 'op123', 'Operator', 'Nguyễn Văn A', 'ABCDEF1234'),
                        ('operator2', 'op456', 'Operator', 'Trần Thị B', 'FEDCBA5678'),
                        ('supervisor', 'sup789', 'Supervisor', 'Lê Văn C', '9876543210'),
                        ('technician', 'tech123', 'Technician', 'Phạm Thị D', 'TECH123456')";
                    await insertCommand.ExecuteNonQueryAsync();

                    Console.WriteLine("Database initialized with sample data.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error initializing database: {ex.Message}");
            }
        }
    }
}
