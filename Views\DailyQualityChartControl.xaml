﻿<UserControl x:Class="ZoomableApp.Views.DailyQualityChartControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             xmlns:local="clr-namespace:ZoomableApp.Views">

    <Border BorderThickness="2" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10,8">
                <Path Data="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"
                      Fill="#2ECC71" Width="auto" Height="auto" Margin="0,0,8,0" VerticalAlignment="Center"/>
                <TextBlock Text="Hôm nay - Chất lượng sản phẩm" FontWeight="Bold" Foreground="White" FontSize="12" VerticalAlignment="Center"/>
            </StackPanel>

            <!-- Chart -->
            <lvc:PieChart Grid.Row="1" x:Name="DailyQualityChart"
                          Width="Auto"
                          Height="Auto"
                          Series="{Binding Series}"
                          LegendPosition="Left"
                          Background="Transparent">
            </lvc:PieChart>

            <!-- Footer Info -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Margin="5">
                <TextBlock Text="Tỷ lệ OK: " Foreground="#BDC3C7" FontSize="14"/>
                <TextBlock Text="{Binding QualityRate, StringFormat='{}{0:F1}%'}" Foreground="#2ECC71" FontSize="14" FontWeight="Bold"/>
                <TextBlock Text=" | Tổng: " Foreground="#BDC3C7" FontSize="14" Margin="8,0,0,0"/>
                <TextBlock Text="{Binding TotalQuantity}" Foreground="#F39C12" FontSize="14" FontWeight="Bold"/>
            </StackPanel>
        </Grid>
    </Border>
</UserControl>
