using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Text.Json;
using System.Windows.Input;
using ZoomableApp.Services;
using Microsoft.Extensions.Configuration;
using System.Diagnostics;

namespace ZoomableApp.ViewModels
{
    public class PlcConfigItem : INotifyPropertyChanged
    {
        private string _id = "";
        private string _name = "";
        private string _ipAddress = "";
        private int _port = 5007;
        private bool _autoConnect = true;
        private bool _isEnabled = true;

        public string Id
        {
            get => _id;
            set { _id = value; OnPropertyChanged(nameof(Id)); }
        }

        public string Name
        {
            get => _name;
            set { _name = value; OnPropertyChanged(nameof(Name)); }
        }

        public string IpAddress
        {
            get => _ipAddress;
            set { _ipAddress = value; OnPropertyChanged(nameof(IpAddress)); }
        }

        public int Port
        {
            get => _port;
            set { _port = value; OnPropertyChanged(nameof(Port)); }
        }

        public bool AutoConnect
        {
            get => _autoConnect;
            set { _autoConnect = value; OnPropertyChanged(nameof(AutoConnect)); }
        }

        public bool IsEnabled
        {
            get => _isEnabled;
            set { _isEnabled = value; OnPropertyChanged(nameof(IsEnabled)); }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class SettingsViewModel : INotifyPropertyChanged
    {
        private string _planFilePath = "";
        private string _maintenancePlanPath = "";
        private string _idlePlansFilePath = "";
        private string _databasePath = "";
        private bool _autoConnectAllPlcs = true;
        private int _dataRefreshInterval = 2000;

        public SettingsViewModel()
        {
            PlcConfigs = new ObservableCollection<PlcConfigItem>();
            SaveAllCommand = new RelayCommand(SaveAllSettings);
            LoadSettings();
        }

        // Excel Settings
        public string PlanFilePath
        {
            get => _planFilePath;
            set { _planFilePath = value; OnPropertyChanged(nameof(PlanFilePath)); }
        }

        public string MaintenancePlanPath
        {
            get => _maintenancePlanPath;
            set { _maintenancePlanPath = value; OnPropertyChanged(nameof(MaintenancePlanPath)); }
        }

        public string IdlePlansFilePath
        {
            get => _idlePlansFilePath;
            set { _idlePlansFilePath = value; OnPropertyChanged(nameof(IdlePlansFilePath)); }
        }

        // General Settings
        public string DatabasePath
        {
            get => _databasePath;
            set { _databasePath = value; OnPropertyChanged(nameof(DatabasePath)); }
        }

        public bool AutoConnectAllPlcs
        {
            get => _autoConnectAllPlcs;
            set { _autoConnectAllPlcs = value; OnPropertyChanged(nameof(AutoConnectAllPlcs)); }
        }

        public int DataRefreshInterval
        {
            get => _dataRefreshInterval;
            set { _dataRefreshInterval = value; OnPropertyChanged(nameof(DataRefreshInterval)); }
        }

        // PLC Settings
        public ObservableCollection<PlcConfigItem> PlcConfigs { get; set; }

        // Commands
        public ICommand SaveAllCommand { get; }

        private void LoadSettings()
        {
            try
            {
                // Load Excel Settings
                var excelSettings = ConfigLoader.LoadExcelSettings();
                PlanFilePath = excelSettings.PlanFilePath;
                MaintenancePlanPath = excelSettings.MaintenancePlanPath;
                IdlePlansFilePath = excelSettings.IdlePlansFilePath ?? "C:\\Project-Dat\\PANA\\idleplans.xlsx";

                // Load PLC Settings
                LoadPlcConfigs();

                // Load General Settings (from appsettings.json or default values)
                DatabasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                AutoConnectAllPlcs = true;
                DataRefreshInterval = 2000;

                Console.WriteLine("Settings loaded successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading settings: {ex.Message}");
            }
        }

        private void LoadPlcConfigs()
        {
            try
            {
                string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plc_configs.json");
                if (File.Exists(filePath))
                {
                    string jsonString = File.ReadAllText(filePath);
                    var configEntries = JsonSerializer.Deserialize<List<PlcConfigFileEntry>>(jsonString, 
                        new JsonSerializerOptions { PropertyNameCaseInsensitive = true });

                    PlcConfigs.Clear();
                    if (configEntries != null)
                    {
                        foreach (var entry in configEntries)
                        {
                            PlcConfigs.Add(new PlcConfigItem
                            {
                                Id = entry.Id,
                                Name = entry.Name,
                                IpAddress = entry.IpAddress,
                                Port = entry.Port,
                                AutoConnect = entry.AutoConnect,
                                IsEnabled = entry.IsEnabled
                            });
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading PLC configs: {ex.Message}");
            }
        }

        public void SaveAllSettings()
        {
            try
            {
                SaveExcelSettings();
                SavePlcConfigs();
                SaveGeneralSettings();
                
                Console.WriteLine("All settings saved successfully");
                
                // Show success message
                System.Windows.MessageBox.Show(
                    "Cài đặt đã được lưu thành công!\n\nLưu ý: Một số thay đổi có thể yêu cầu khởi động lại ứng dụng để có hiệu lực.",
                    "Lưu cài đặt",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error saving settings: {ex.Message}");
                System.Windows.MessageBox.Show(
                    $"Có lỗi xảy ra khi lưu cài đặt:\n{ex.Message}",
                    "Lỗi",
                    System.Windows.MessageBoxButton.OK,
                    System.Windows.MessageBoxImage.Error);
            }
        }

        private void SaveExcelSettings()
        {
            string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
            
            var settings = new
            {
                ExcelSettings = new
                {
                    PlanFilePath = PlanFilePath,
                    MaintenancePlanPath = MaintenancePlanPath,
                    IdlePlansFilePath = IdlePlansFilePath
                },
                Logging = new
                {
                    LogLevel = new
                    {
                        Default = "Information",
                        Microsoft_AspNetCore = "Warning"
                    }
                }
            };

            string jsonString = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(filePath, jsonString);
        }

        private void SavePlcConfigs()
        {
            string filePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "plc_configs.json");
            
            var configEntries = new List<PlcConfigFileEntry>();
            foreach (var config in PlcConfigs)
            {
                configEntries.Add(new PlcConfigFileEntry
                {
                    Id = config.Id,
                    Name = config.Name,
                    IpAddress = config.IpAddress,
                    Port = config.Port,
                    AutoConnect = config.AutoConnect,
                    IsEnabled = config.IsEnabled
                });
            }

            string jsonString = JsonSerializer.Serialize(configEntries, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(filePath, jsonString);
        }

        private void SaveGeneralSettings()
        {
            // For now, general settings are saved in memory
            // In a real application, you might want to save these to a separate config file
            Console.WriteLine($"General settings saved: DatabasePath={DatabasePath}, AutoConnect={AutoConnectAllPlcs}, RefreshInterval={DataRefreshInterval}");
        }

        public void AddNewPlc()
        {
            var newPlc = new PlcConfigItem
            {
                Id = $"PLC_{PlcConfigs.Count + 1}",
                Name = $"PLC mới {PlcConfigs.Count + 1}",
                IpAddress = "*************",
                Port = 5007,
                AutoConnect = true,
                IsEnabled = true
            };
            PlcConfigs.Add(newPlc);
        }

        public void RemovePlc(PlcConfigItem plc)
        {
            if (plc != null)
            {
                PlcConfigs.Remove(plc);
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
