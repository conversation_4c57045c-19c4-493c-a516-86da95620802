using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using System.IO;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Centralized database service - ALL database operations go through here
    /// </summary>
    public class DatabaseService : IDatabaseService
    {
        private readonly string _connectionString;

        public DatabaseService()
        {
            _connectionString = DatabaseHelper.GetConnectionString();
        }

        #region Database Initialization

        public async Task InitializeDatabaseAsync()
        {
            try
            {
                var dataDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                if (!Directory.Exists(dataDirectory))
                {
                    Directory.CreateDirectory(dataDirectory);
                }

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Create users table
                await ExecuteNonQueryInternalAsync(connection, @"
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER NOT NULL UNIQUE,
                        username TEXT,
                        password TEXT,
                        role TEXT,
                        fullname TEXT,
                        rfid TEXT,
                        PRIMARY KEY(id AUTOINCREMENT)
                    )");

                // Create ProductionData table
                await ExecuteNonQueryInternalAsync(connection, @"
                    CREATE TABLE IF NOT EXISTS ProductionData (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Timestamp VARCHAR(255) NOT NULL,
                        Station VARCHAR(255) NOT NULL,
                        Product_OK INTEGER DEFAULT 0,
                        Product_NG INTEGER DEFAULT 0,
                        Product_Total INTEGER DEFAULT 0,
                        Time_Complete REAL DEFAULT 0,
                        Time_Delay INTEGER DEFAULT 0,
                        Time_Stop INTEGER DEFAULT 0,
                        Number_Stop INTEGER DEFAULT 0,
                        Error_Code VARCHAR(255) DEFAULT '',
                        Error_Text VARCHAR(255) DEFAULT '',
                        WorkShift VARCHAR(255) DEFAULT '',
                        ReportType VARCHAR(255) DEFAULT 'Production',
                        CreatedBy VARCHAR(255) DEFAULT '',
                        Notes VARCHAR(255) DEFAULT ''
                    )");

                // Add sample data if empty
                await InitializeSampleDataAsync(connection);

                Console.WriteLine("DatabaseService: Database initialized successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error initializing database: {ex.Message}");
                throw;
            }
        }

        private async Task InitializeSampleDataAsync(SqliteConnection connection)
        {
            var count = await ExecuteScalarInternalAsync<int>(connection, "SELECT COUNT(*) FROM users");
            
            if (count == 0)
            {
                await ExecuteNonQueryInternalAsync(connection, @"
                    INSERT INTO users (username, password, role, fullname, rfid) VALUES
                    ('admin', 'admin123', 'Administrator', 'Administrator', '1234567890'),
                    ('operator1', 'op123', 'Operator', 'Operator One', 'ABCDEF1234'),
                    ('operator2', 'op456', 'Operator', 'Operator Two', 'FEDCBA5678'),
                    ('supervisor', 'sup789', 'Supervisor', 'Supervisor', '9876543210'),
                    ('technician', 'tech123', 'Technician', 'Technician', 'TECH123456')");
                
                Console.WriteLine("DatabaseService: Sample data initialized");
            }
        }

        #endregion

        #region User Management

        public async Task<User> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM users WHERE username = @username AND password = @password";
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@username", username);
                command.Parameters.AddWithValue("@password", password);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new User
                    {
                        Id = Convert.ToInt32(reader["id"]),
                        Username = reader["username"].ToString() ?? "",
                        Password = reader["password"].ToString() ?? "",
                        Role = reader["role"].ToString() ?? "",
                        Fullname = reader["fullname"].ToString() ?? "",
                        Rfid = reader["rfid"].ToString() ?? ""
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error authenticating user: {ex.Message}");
                return null;
            }
        }

        public async Task<User> AuthenticateUserByRfidAsync(string rfid)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM users WHERE rfid = @rfid";
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@rfid", rfid);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new User
                    {
                        Id = Convert.ToInt32(reader["id"]),
                        Username = reader["username"].ToString() ?? "",
                        Password = reader["password"].ToString() ?? "",
                        Role = reader["role"].ToString() ?? "",
                        Fullname = reader["fullname"].ToString() ?? "",
                        Rfid = reader["rfid"].ToString() ?? ""
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error authenticating user by RFID: {ex.Message}");
                return null;
            }
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            var users = new List<User>();
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM users ORDER BY id";
                using var command = new SqliteCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                while (await reader.ReadAsync())
                {
                    users.Add(new User
                    {
                        Id = Convert.ToInt32(reader["id"]),
                        Username = reader["username"].ToString() ?? "",
                        Password = reader["password"].ToString() ?? "",
                        Role = reader["role"].ToString() ?? "",
                        Fullname = reader["fullname"].ToString() ?? "",
                        Rfid = reader["rfid"].ToString() ?? ""
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting all users: {ex.Message}");
            }
            return users;
        }

        public async Task<bool> SaveUserAsync(User user)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                string query;
                if (user.Id == 0)
                {
                    // Insert new user
                    query = @"INSERT INTO users (username, password, role, fullname, rfid) 
                             VALUES (@username, @password, @role, @fullname, @rfid)";
                }
                else
                {
                    // Update existing user
                    query = @"UPDATE users SET username = @username, password = @password, 
                             role = @role, fullname = @fullname, rfid = @rfid WHERE id = @id";
                }

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@username", user.Username);
                command.Parameters.AddWithValue("@password", user.Password);
                command.Parameters.AddWithValue("@role", user.Role);
                command.Parameters.AddWithValue("@fullname", user.Fullname);
                command.Parameters.AddWithValue("@rfid", user.Rfid);
                
                if (user.Id != 0)
                    command.Parameters.AddWithValue("@id", user.Id);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error saving user: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(int userId)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = "DELETE FROM users WHERE id = @id";
                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@id", userId);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error deleting user: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Production Data

        public async Task<bool> SaveProductionDataAsync(ProductionData data)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    INSERT INTO ProductionData
                    (Timestamp, Station, Product_OK, Product_NG, Product_Total, Time_Complete,
                     Time_Delay, Time_Stop, Number_Stop, Error_Code, Error_Text, WorkShift,
                     ReportType, CreatedBy, Notes)
                    VALUES
                    (@Timestamp, @Station, @Product_OK, @Product_NG, @Product_Total, @Time_Complete,
                     @Time_Delay, @Time_Stop, @Number_Stop, @Error_Code, @Error_Text, @WorkShift,
                     @ReportType, @CreatedBy, @Notes)";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@Timestamp", data.Timestamp);
                command.Parameters.AddWithValue("@Station", data.Station);
                command.Parameters.AddWithValue("@Product_OK", data.Product_OK);
                command.Parameters.AddWithValue("@Product_NG", data.Product_NG);
                command.Parameters.AddWithValue("@Product_Total", data.Product_Total);
                command.Parameters.AddWithValue("@Time_Complete", data.Time_Complete);
                command.Parameters.AddWithValue("@Time_Delay", data.Time_Delay);
                command.Parameters.AddWithValue("@Time_Stop", data.Time_Stop);
                command.Parameters.AddWithValue("@Number_Stop", data.Number_Stop);
                command.Parameters.AddWithValue("@Error_Code", data.Error_Code);
                command.Parameters.AddWithValue("@Error_Text", data.Error_Text);
                command.Parameters.AddWithValue("@WorkShift", data.WorkShift);
                command.Parameters.AddWithValue("@ReportType", data.ReportType);
                command.Parameters.AddWithValue("@CreatedBy", data.CreatedBy);
                command.Parameters.AddWithValue("@Notes", data.Notes);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error saving production data: {ex.Message}");
                return false;
            }
        }

        public async Task<List<ProductionData>> GetProductionDataAsync(DateTime? fromDate = null, DateTime? toDate = null, string workShift = null, string reportType = null)
        {
            var result = new List<ProductionData>();
            try
            {
                Console.WriteLine($"[DatabaseService] GetProductionDataAsync called - FromDate: {fromDate}, ToDate: {toDate}, WorkShift: {workShift}, ReportType: {reportType}");

                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = "SELECT * FROM ProductionData WHERE 1=1";
                var parameters = new List<SqliteParameter>();

                if (fromDate.HasValue)
                {
                    query += " AND DATE(Timestamp) >= @FromDate";
                    parameters.Add(new SqliteParameter("@FromDate", fromDate.Value.ToString("yyyy-MM-dd")));
                }

                if (toDate.HasValue)
                {
                    query += " AND DATE(Timestamp) <= @ToDate";
                    parameters.Add(new SqliteParameter("@ToDate", toDate.Value.ToString("yyyy-MM-dd")));
                }

                if (!string.IsNullOrEmpty(workShift))
                {
                    query += " AND WorkShift = @WorkShift";
                    parameters.Add(new SqliteParameter("@WorkShift", workShift));
                }

                if (!string.IsNullOrEmpty(reportType))
                {
                    query += " AND ReportType = @ReportType";
                    parameters.Add(new SqliteParameter("@ReportType", reportType));
                }

                query += " ORDER BY Timestamp DESC";

                Console.WriteLine($"[DatabaseService] Executing query: {query}");

                using var command = new SqliteCommand(query, connection);
                foreach (var param in parameters)
                    command.Parameters.Add(param);

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    result.Add(new ProductionData
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Timestamp = reader["Timestamp"].ToString() ?? "",
                        Station = reader["Station"].ToString() ?? "",
                        Product_OK = Convert.ToInt32(reader["Product_OK"]),
                        Product_NG = Convert.ToInt32(reader["Product_NG"]),
                        Product_Total = Convert.ToInt32(reader["Product_Total"]),
                        Time_Complete = Convert.ToSingle(reader["Time_Complete"]),
                        Time_Delay = Convert.ToInt32(reader["Time_Delay"]),
                        Time_Stop = Convert.ToInt32(reader["Time_Stop"]),
                        Number_Stop = Convert.ToInt32(reader["Number_Stop"]),
                        Error_Code = reader["Error_Code"].ToString() ?? "",
                        Error_Text = reader["Error_Text"].ToString() ?? "",
                        WorkShift = reader["WorkShift"].ToString() ?? "",
                        ReportType = reader["ReportType"].ToString() ?? "",
                        CreatedBy = reader["CreatedBy"].ToString() ?? "",
                        Notes = reader["Notes"].ToString() ?? ""
                    });
                }

                Console.WriteLine($"[DatabaseService] Retrieved {result.Count} records from database");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting production data: {ex.Message}");
            }
            return result;
        }

        public async Task<ProductionData> GetLastShiftChangeDataAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT * FROM ProductionData
                    WHERE ReportType = 'ShiftStart'
                    ORDER BY Timestamp DESC
                    LIMIT 1";

                using var command = new SqliteCommand(query, connection);
                using var reader = await command.ExecuteReaderAsync();

                if (await reader.ReadAsync())
                {
                    return new ProductionData
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Timestamp = reader["Timestamp"].ToString() ?? "",
                        Station = reader["Station"].ToString() ?? "",
                        Product_OK = Convert.ToInt32(reader["Product_OK"]),
                        Product_NG = Convert.ToInt32(reader["Product_NG"]),
                        Product_Total = Convert.ToInt32(reader["Product_Total"]),
                        Time_Complete = Convert.ToSingle(reader["Time_Complete"]),
                        Time_Delay = Convert.ToInt32(reader["Time_Delay"]),
                        Time_Stop = Convert.ToInt32(reader["Time_Stop"]),
                        Number_Stop = Convert.ToInt32(reader["Number_Stop"]),
                        Error_Code = reader["Error_Code"].ToString() ?? "",
                        Error_Text = reader["Error_Text"].ToString() ?? "",
                        WorkShift = reader["WorkShift"].ToString() ?? "",
                        ReportType = reader["ReportType"].ToString() ?? "",
                        CreatedBy = reader["CreatedBy"].ToString() ?? "",
                        Notes = reader["Notes"].ToString() ?? ""
                    };
                }
                return null;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting last shift change data: {ex.Message}");
                return null;
            }
        }

        public async Task<int> GetTodayPreviousShiftsActualAsync(string currentShiftName)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // For night shift, we need to handle the date correctly
                var queryDate = WorkShiftHelper.GetShiftPlanDate(DateTime.Now, currentShiftName);
                var today = queryDate.ToString("yyyy-MM-dd");

                Console.WriteLine($"DatabaseService: GetTodayPreviousShiftsActual - Current shift: {currentShiftName}, Query date: {queryDate:yyyy-MM-dd}");

                var query = @"
                    SELECT Product_Total FROM ProductionData
                    WHERE ReportType = 'ShiftStart'
                    AND DATE(Timestamp) = @today
                    AND WorkShift != @currentShift
                    ORDER BY Timestamp DESC
                    LIMIT 1";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@today", today);
                command.Parameters.AddWithValue("@currentShift", currentShiftName);

                var result = await command.ExecuteScalarAsync();
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting previous shifts actual: {ex.Message}");
                return 0;
            }
        }

        #endregion

        #region Production Summary

        public async Task<ProductionSummaryData> GetDailyProductionSummaryAsync(DateTime date)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var startDate = date.Date.ToString("yyyy-MM-dd");
                var endDate = date.Date.AddDays(1).ToString("yyyy-MM-dd");

                var query = @"
                    SELECT
                        SUM(Product_OK) as TotalOK,
                        SUM(Product_NG) as TotalNG,
                        SUM(Product_Total) as TotalQuantity
                    FROM ProductionData
                    WHERE DATE(Timestamp) >= @StartDate
                    AND DATE(Timestamp) < @EndDate";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@StartDate", startDate);
                command.Parameters.AddWithValue("@EndDate", endDate);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new ProductionSummaryData
                    {
                        OkQuantity = reader["TotalOK"] != DBNull.Value ? Convert.ToInt32(reader["TotalOK"]) : 0,
                        NgQuantity = reader["TotalNG"] != DBNull.Value ? Convert.ToInt32(reader["TotalNG"]) : 0,
                        TotalQuantity = reader["TotalQuantity"] != DBNull.Value ? Convert.ToInt32(reader["TotalQuantity"]) : 0
                    };
                }
                return new ProductionSummaryData();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting daily production summary: {ex.Message}");
                return new ProductionSummaryData();
            }
        }

        public async Task<ProductionSummaryData> GetShiftProductionSummaryAsync(DateTime date, string shiftName)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // For night shift, adjust the date logic
                var queryDate = WorkShiftHelper.GetShiftPlanDate(date, shiftName);
                var dateStr = queryDate.ToString("yyyy-MM-dd");

                Console.WriteLine($"DatabaseService: GetShiftProductionSummary - Original date: {date:yyyy-MM-dd}, Shift: {shiftName}, Query date: {queryDate:yyyy-MM-dd}");

                var query = @"
                    SELECT
                        SUM(Product_OK) as TotalOK,
                        SUM(Product_NG) as TotalNG,
                        SUM(Product_Total) as TotalQuantity
                    FROM ProductionData
                    WHERE DATE(Timestamp) = @Date
                    AND WorkShift = @ShiftName";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@Date", dateStr);
                command.Parameters.AddWithValue("@ShiftName", shiftName);

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    return new ProductionSummaryData
                    {
                        OkQuantity = reader["TotalOK"] != DBNull.Value ? Convert.ToInt32(reader["TotalOK"]) : 0,
                        NgQuantity = reader["TotalNG"] != DBNull.Value ? Convert.ToInt32(reader["TotalNG"]) : 0,
                        TotalQuantity = reader["TotalQuantity"] != DBNull.Value ? Convert.ToInt32(reader["TotalQuantity"]) : 0
                    };
                }
                return new ProductionSummaryData();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting shift production summary: {ex.Message}");
                return new ProductionSummaryData();
            }
        }

        public async Task<TimeSpan> GetUsedIdleTimeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var query = @"
                    SELECT SUM(Time_Stop) as TotalIdleTime
                    FROM ProductionData
                    WHERE datetime(Timestamp) >= @StartDate
                    AND datetime(Timestamp) < @EndDate";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@StartDate", startDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@EndDate", endDate.ToString("yyyy-MM-dd HH:mm:ss"));

                var result = await command.ExecuteScalarAsync();
                if (result != null && result != DBNull.Value)
                {
                    var totalMinutes = Convert.ToDouble(result);
                    return TimeSpan.FromMinutes(totalMinutes);
                }
                return TimeSpan.Zero;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting used idle time: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        #endregion

        #region Idle Time Management

        public async Task SaveDailyIdleTimeSummaryAsync(DateTime date, TimeSpan idleTime)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Create table if not exists
                var createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS DailyIdleTimeSummary (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Date TEXT NOT NULL UNIQUE,
                        IdleTimeSeconds INTEGER NOT NULL,
                        CreatedAt TEXT NOT NULL,
                        UpdatedAt TEXT NOT NULL
                    )";

                using var createCommand = new SqliteCommand(createTableQuery, connection);
                await createCommand.ExecuteNonQueryAsync();

                // Insert or update today's record
                var upsertQuery = @"
                    INSERT OR REPLACE INTO DailyIdleTimeSummary
                    (Date, IdleTimeSeconds, CreatedAt, UpdatedAt)
                    VALUES (@Date, @IdleTimeSeconds,
                            COALESCE((SELECT CreatedAt FROM DailyIdleTimeSummary WHERE Date = @Date), @Now),
                            @Now)";

                using var upsertCommand = new SqliteCommand(upsertQuery, connection);
                upsertCommand.Parameters.AddWithValue("@Date", date.ToString("yyyy-MM-dd"));
                upsertCommand.Parameters.AddWithValue("@IdleTimeSeconds", (int)idleTime.TotalSeconds);
                upsertCommand.Parameters.AddWithValue("@Now", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                await upsertCommand.ExecuteNonQueryAsync();
                Console.WriteLine($"DatabaseService: Saved daily idle time for {date:yyyy-MM-dd}: {idleTime.TotalMinutes:F1} minutes");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error saving daily idle time: {ex.Message}");
            }
        }

        public async Task<TimeSpan> GetMonthlyIdleTimeFromDatabaseAsync()
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                var currentMonth = new DateTime(DateTime.Today.Year, DateTime.Today.Month, 1);
                var nextMonth = currentMonth.AddMonths(1);

                var query = @"
                    SELECT SUM(IdleTimeSeconds)
                    FROM DailyIdleTimeSummary
                    WHERE Date >= @StartDate AND Date < @EndDate";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@StartDate", currentMonth.ToString("yyyy-MM-dd"));
                command.Parameters.AddWithValue("@EndDate", nextMonth.ToString("yyyy-MM-dd"));

                var result = await command.ExecuteScalarAsync();
                var totalSeconds = result != null && result != DBNull.Value ? Convert.ToInt32(result) : 0;

                return TimeSpan.FromSeconds(totalSeconds);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error getting monthly idle time: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        #endregion

        #region Product Records

        public async Task SaveProductRecordAsync(ProductRecord record)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();

                // Create ProductRecords table if not exists
                await ExecuteNonQueryInternalAsync(connection, @"
                    CREATE TABLE IF NOT EXISTS ProductRecords (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ProductCode TEXT NOT NULL,
                        SequenceNumber INTEGER NOT NULL,
                        TestResult TEXT NOT NULL,
                        TestMachineId TEXT NOT NULL,
                        RecordTimestamp TEXT NOT NULL
                    )");

                var query = @"
                    INSERT INTO ProductRecords (ProductCode, SequenceNumber, TestResult, TestMachineId, RecordTimestamp)
                    VALUES (@ProductCode, @SequenceNumber, @TestResult, @TestMachineId, @RecordTimestamp)";

                using var command = new SqliteCommand(query, connection);
                command.Parameters.AddWithValue("@ProductCode", record.ProductCode);
                command.Parameters.AddWithValue("@SequenceNumber", record.SequenceNumber);
                command.Parameters.AddWithValue("@TestResult", record.TestResult);
                command.Parameters.AddWithValue("@TestMachineId", record.TestMachineId);
                command.Parameters.AddWithValue("@RecordTimestamp", record.RecordTimestamp.ToString("yyyy-MM-dd HH:mm:ss"));

                await command.ExecuteNonQueryAsync();
                Console.WriteLine($"DatabaseService: Product record saved - {record.ProductCode}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error saving product record: {ex.Message}");
            }
        }

        #endregion

        #region Generic Query Methods

        public async Task<T> ExecuteScalarAsync<T>(string query, object parameters = null)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();
                using var command = new SqliteCommand(query, connection);

                if (parameters != null)
                    AddParameters(command, parameters);

                var result = await command.ExecuteScalarAsync();
                return (T)Convert.ChangeType(result, typeof(T));
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error executing scalar query: {ex.Message}");
                return default(T);
            }
        }

        public async Task<List<T>> ExecuteQueryAsync<T>(string query, object parameters = null) where T : new()
        {
            var result = new List<T>();
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();
                using var command = new SqliteCommand(query, connection);

                if (parameters != null)
                    AddParameters(command, parameters);

                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    var item = new T();
                    // Simple property mapping - can be enhanced with reflection
                    result.Add(item);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error executing query: {ex.Message}");
            }
            return result;
        }

        public async Task<bool> ExecuteNonQueryAsync(string query, object parameters = null)
        {
            try
            {
                using var connection = new SqliteConnection(_connectionString);
                await connection.OpenAsync();
                using var command = new SqliteCommand(query, connection);

                if (parameters != null)
                    AddParameters(command, parameters);

                var result = await command.ExecuteNonQueryAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DatabaseService: Error executing non-query: {ex.Message}");
                return false;
            }
        }

        private void AddParameters(SqliteCommand command, object parameters)
        {
            var properties = parameters.GetType().GetProperties();
            foreach (var prop in properties)
            {
                command.Parameters.AddWithValue($"@{prop.Name}", prop.GetValue(parameters));
            }
        }

        #endregion

        #region Internal Helper Methods

        private async Task<bool> ExecuteNonQueryInternalAsync(SqliteConnection connection, string query)
        {
            using var command = new SqliteCommand(query, connection);
            var result = await command.ExecuteNonQueryAsync();
            return result > 0;
        }

        private async Task<T> ExecuteScalarInternalAsync<T>(SqliteConnection connection, string query)
        {
            using var command = new SqliteCommand(query, connection);
            var result = await command.ExecuteScalarAsync();
            return (T)Convert.ChangeType(result, typeof(T));
        }

        #endregion
    }
}
