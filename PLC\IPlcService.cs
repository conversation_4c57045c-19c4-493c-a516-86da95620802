﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZoomableApp.Models;

namespace ZoomableApp.PLC
{
    public class ConnectionResult
    {
        public bool IsSuccess { get; }
        public string Message { get; }
        public ConnectionResult(bool isSuccess, string message = "")
        {
            IsSuccess = isSuccess;
            Message = message;
        }
    }
    public interface IPlcService
    {
        bool IsConnected { get; }
        string PlcIpAddress { get; set; } // Hoặc các thông số kết nối khác
        int PlcPort { get; set; }

        Task<ConnectionResult> ConnectAsync();
        Task DisconnectAsync();

        // Đọc một giá trị từ một địa chỉ logic
        Task<PlcReadResult> ReadAsync(PlcDeviceAddress deviceAddress);
        // Đọc nhiều giá trị
        Task<Dictionary<PlcDeviceAddress, object>> ReadMultipleAsync(IEnumerable<PlcDeviceAddress> deviceAddresses);

        // <PERSON><PERSON> một giá trị tới một địa chỉ logic
        Task<PlcWriteResult> WriteAsync(PlcDeviceAddress deviceAddress, object value);
        // Ghi nhiều giá trị
        Task<bool> WriteMultipleAsync(Dictionary<PlcDeviceAddress, object> valuesToWrite);

        // Phương thức để lấy thông tin chi tiết của một thanh ghi (địa chỉ vật lý, kiểu)
        PlcRegisterInfo GetRegisterInfo(PlcDeviceAddress deviceAddress);
    }

    public class PlcReadResult
    {
        public bool IsSuccess { get; }
        public object Value { get; } // Giá trị đọc được (kiểu object, PlcDataMapper sẽ xử lý sau)
        public string Message { get; }

        public PlcReadResult(bool isSuccess, object value, string message = "")
        {
            IsSuccess = isSuccess;
            Value = value;
            Message = message;
        }

        // Factory methods để tạo kết quả dễ dàng hơn
        public static PlcReadResult Success(object value, string message = "Read successful") => new PlcReadResult(true, value, message);
        public static PlcReadResult Failure(string message, object value = null) => new PlcReadResult(false, value, message);
    }

    public class PlcWriteResult
    {
        public bool IsSuccess { get; }
        public string Message { get; }

        public PlcWriteResult(bool isSuccess, string message = "")
        {
            IsSuccess = isSuccess;
            Message = message;
        }

        public static PlcWriteResult Success(string message = "Write successful") => new PlcWriteResult(true, message);
        public static PlcWriteResult Failure(string message) => new PlcWriteResult(false, message);
    }
}
