﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using ZoomableApp;
using ZoomableApp.Models;

namespace ZoomableApp.SharedControls
{
    /// <summary>
    /// Interaction logic for StationControl.xaml
    /// </summary>
    public partial class StationControl : UserControl, INotifyPropertyChanged
    {
        public StationControl()
        {
            InitializeComponent();
            UpdateStationDisplayBrush();
        }

        public static readonly DependencyProperty StationNumberProperty =
            DependencyProperty.Register("StationNumber", typeof(string), typeof(StationControl), new PropertyMetadata("00"));
        public string StationNumber
        {
            get { return (string)GetValue(StationNumberProperty); }
            set { SetValue(StationNumberProperty, value); }
        }

        // --- StationType ---
        public static readonly DependencyProperty StationTypeProperty =
            DependencyProperty.Register("StationType", typeof(StationType), typeof(StationControl),
                                        new PropertyMetadata(StationType.None, OnCorePropertyChanged));
        public StationType StationType
        {
            get { return (StationType)GetValue(StationTypeProperty); }
            set { SetValue(StationTypeProperty, value); }
        }

        // --- StationLength (sẽ điều khiển Width của UserControl này từ MainlineLayout) ---
        public static readonly DependencyProperty StationLengthProperty =
            DependencyProperty.Register("StationLength", typeof(double), typeof(StationControl), new PropertyMetadata(100.0, OnStationLengthChanged));
        public double StationLength
        {
            get { return (double)GetValue(StationLengthProperty); }
            set { SetValue(StationLengthProperty, value); }
        }
        private static void OnStationLengthChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is StationControl control)
            {
                control.Width = (double)e.NewValue;
            }
        }

        // --- TestResult (cho trạm Test) ---
        public static readonly DependencyProperty TestResultProperty =
            DependencyProperty.Register("TestResult", typeof(TestResultStatus), typeof(StationControl),
                                        new PropertyMetadata(TestResultStatus.None, OnCorePropertyChanged));
        public TestResultStatus TestResult
        {
            get { return (TestResultStatus)GetValue(TestResultProperty); }
            set { SetValue(TestResultProperty, value); }
        }

        // --- ProductCode ---
        //public static readonly DependencyProperty ProductCodeProperty =
        //    DependencyProperty.Register("ProductCode", typeof(string), typeof(StationControl), new PropertyMetadata(string.Empty, OnCorePropertyChanged));
        //public string ProductCode
        //{
        //    get { return (string)GetValue(ProductCodeProperty); }
        //    set { SetValue(ProductCodeProperty, value); }
        //}

        public static readonly DependencyProperty ProductStatusAtStationProperty =
    DependencyProperty.Register("ProductStatusAtStation", typeof(TestResultStatus), typeof(StationControl),
                                new PropertyMetadata(TestResultStatus.None, OnCorePropertyChanged));

        public TestResultStatus ProductStatusAtStation
        {
            get { return (TestResultStatus)GetValue(ProductStatusAtStationProperty); }
            set { SetValue(ProductStatusAtStationProperty, value); }
        }


        // --- OperationTime ---
        public static readonly DependencyProperty OperationTimeProperty =
            DependencyProperty.Register("OperationTime", typeof(string), typeof(StationControl), new PropertyMetadata("00:00", OnCorePropertyChanged));
        public string OperationTime
        {
            get { return (string)GetValue(OperationTimeProperty); }
            set { SetValue(OperationTimeProperty, value); }
        }

        // --- DeviceStatus ---
        public static readonly DependencyProperty DeviceStatusProperty =
            DependencyProperty.Register("DeviceStatus", typeof(DeviceOperationalStatus), typeof(StationControl),
                                        new PropertyMetadata(DeviceOperationalStatus.None, OnCorePropertyChanged));
        public DeviceOperationalStatus DeviceStatus
        {
            get { return (DeviceOperationalStatus)GetValue(DeviceStatusProperty); }
            set { SetValue(DeviceStatusProperty, value); }
        }

        // --- HasProduct ---
        public static readonly DependencyProperty HasProductProperty =
            DependencyProperty.Register("HasProduct", typeof(bool), typeof(StationControl), new PropertyMetadata(false, OnCorePropertyChanged));
        public bool HasProduct
        {
            get { return (bool)GetValue(HasProductProperty); }
            set { SetValue(HasProductProperty, value); }
        }


        // --- StationDisplayBrush (Property thường với INotifyPropertyChanged) ---
        private Brush _stationDisplayBrush;
        public Brush StationDisplayBrush
        {
            get => _stationDisplayBrush;
            set
            {
                if (_stationDisplayBrush != value)
                {
                    _stationDisplayBrush = value;
                    OnPropertyChanged(nameof(StationDisplayBrush));
                }
            }
        }

        // --- Read-only properties for XAML binding convenience (driven by StationType) ---
        public bool IsTestStationType => StationType == StationType.TestRoller;
        public bool IsWorkerStationType => StationType == StationType.WorkerRoller;

        // --- Callback for core property changes to update brush and dependent properties ---
        private static void OnCorePropertyChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is StationControl control)
            {
                control.UpdateStationDisplayBrush();
                // Notify changes for read-only properties if StationType changed
                if (e.Property == StationTypeProperty)
                {
                    control.OnPropertyChanged(nameof(IsTestStationType));
                    control.OnPropertyChanged(nameof(IsWorkerStationType));
                }
                // Hiện tại, HasProduct là độc lập.
            }
        }

        public void UpdateStationDisplayBrush()
        {
            if (DeviceStatus == DeviceOperationalStatus.Error) StationDisplayBrush = Brushes.DarkRed;
            else if (DeviceStatus == DeviceOperationalStatus.Maintenance) StationDisplayBrush = Brushes.Orange;
            // If it's a Test Station and has a product, its color is determined by the product's test status
            else if (IsTestStationType && HasProduct)
            {
                switch (ProductStatusAtStation) // Use the new property
                {
                    case TestResultStatus.NG: StationDisplayBrush = Brushes.IndianRed; break;
                    case TestResultStatus.Testing: StationDisplayBrush = Brushes.LightYellow; break;
                    case TestResultStatus.OK: StationDisplayBrush = Brushes.LightGreen; break;
                    case TestResultStatus.AwaitingProduct: StationDisplayBrush = Brushes.LightSkyBlue; break; // Or same as Idle if preferred
                    default: StationDisplayBrush = Brushes.LightSkyBlue; break; // Product present, status unknown
                }
            }
            // If not a test station but has product and device is operational
            else if (HasProduct && (DeviceStatus == DeviceOperationalStatus.Running || DeviceStatus == DeviceOperationalStatus.Idle || DeviceStatus == DeviceOperationalStatus.None))
            {
                StationDisplayBrush = Brushes.LightSkyBlue; // General "product present" color
            }
            else if (DeviceStatus == DeviceOperationalStatus.Running) StationDisplayBrush = Brushes.SkyBlue;
            else if (DeviceStatus == DeviceOperationalStatus.Idle) StationDisplayBrush = Brushes.LightGray; // Idle and no product
            else if (DeviceStatus == DeviceOperationalStatus.Off) StationDisplayBrush = Brushes.DimGray;
            else StationDisplayBrush = Brushes.WhiteSmoke; // Default
        }

        // Placeholder for "Done" button click for Worker Stations
        private void WorkerDoneButton_Click_Placeholder(object sender, RoutedEventArgs e)
        {
            MessageBox.Show($"Trạm {StationNumber}: Công nhân báo xong!");
            // Logic thực tế: gửi tín hiệu, reset OperationTime, etc.
        }

        // INotifyPropertyChanged implementation
        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
