using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Mock PLC Service for testing and demo purposes
    /// Generates realistic fake data without requiring actual PLC connection
    /// </summary>
    public class MockPlcService : IPlcService
    {
        private readonly Random _random = new Random();
        private readonly Dictionary<PlcDeviceAddress, object> _mockData = new();
        private readonly string _plcId;
        private readonly System.Threading.Timer _dataUpdateTimer;
        private bool _isConnected = false;
        private readonly ILoggerService? _logger;

        public bool IsConnected => _isConnected;
        public string PlcIpAddress { get; set; } = "127.0.0.1";
        public int PlcPort { get; set; } = 4999;

        public MockPlcService(string plcId = "MOCK_PLC")
        {
            _plcId = plcId;
            InitializeMockData();
            
            // Update mock data every 2 seconds to simulate real PLC behavior
            _dataUpdateTimer = new System.Threading.Timer(UpdateMockData, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
            
            Console.WriteLine($"MockPlcService created for PLC ID: {_plcId}");
        }

        public Task<ConnectionResult> ConnectAsync()
        {
            // Simulate connection delay
            Task.Delay(500).Wait();
            
            _isConnected = true;
            Console.WriteLine($"MockPlcService: Connected to {PlcIpAddress}:{PlcPort} (Simulated)");
            return Task.FromResult(new ConnectionResult(true, "Mock connection successful"));
        }

        public Task DisconnectAsync()
        {
            _isConnected = false;
            Console.WriteLine($"MockPlcService: Disconnected from {PlcIpAddress}:{PlcPort} (Simulated)");
            return Task.CompletedTask;
        }

        public Task<PlcReadResult> ReadAsync(PlcDeviceAddress deviceAddress)
        {
            if (!_isConnected)
            {
                return Task.FromResult(PlcReadResult.Failure("Mock PLC not connected"));
            }

            try
            {
                var value = GetMockValue(deviceAddress);
                Console.WriteLine($"MockPlcService: Read {deviceAddress} = {value}");
                return Task.FromResult(PlcReadResult.Success(value, "Mock read successful"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(PlcReadResult.Failure($"Mock read error: {ex.Message}"));
            }
        }

        public Task<Dictionary<PlcDeviceAddress, object>> ReadMultipleAsync(IEnumerable<PlcDeviceAddress> deviceAddresses)
        {
            var results = new Dictionary<PlcDeviceAddress, object>();
            
            if (!_isConnected)
            {
                return Task.FromResult(results);
            }

            foreach (var address in deviceAddresses)
            {
                results[address] = GetMockValue(address);
            }

            return Task.FromResult(results);
        }

        public Task<PlcWriteResult> WriteAsync(PlcDeviceAddress deviceAddress, object value)
        {
            if (!_isConnected)
            {
                return Task.FromResult(PlcWriteResult.Failure("Mock PLC not connected"));
            }

            try
            {
                _mockData[deviceAddress] = value;
                Console.WriteLine($"MockPlcService: Write {deviceAddress} = {value} (Simulated)");
                return Task.FromResult(PlcWriteResult.Success("Mock write successful"));
            }
            catch (Exception ex)
            {
                return Task.FromResult(PlcWriteResult.Failure($"Mock write error: {ex.Message}"));
            }
        }

        public Task<bool> WriteMultipleAsync(Dictionary<PlcDeviceAddress, object> valuesToWrite)
        {
            if (!_isConnected)
            {
                return Task.FromResult(false);
            }

            foreach (var kvp in valuesToWrite)
            {
                _mockData[kvp.Key] = kvp.Value;
            }

            return Task.FromResult(true);
        }

        public PlcRegisterInfo GetRegisterInfo(PlcDeviceAddress deviceAddress)
        {
            // Return mock register info based on device address
            return deviceAddress switch
            {
                // Production counters
                PlcDeviceAddress.NumberProductOk => new PlcRegisterInfo(deviceAddress, "D1000", PlcDataType.WORD),
                PlcDeviceAddress.NumberProductNg => new PlcRegisterInfo(deviceAddress, "D1002", PlcDataType.WORD),
                PlcDeviceAddress.NumberProductTotal => new PlcRegisterInfo(deviceAddress, "D1004", PlcDataType.WORD),
                PlcDeviceAddress.NumProductPresent => new PlcRegisterInfo(deviceAddress, "D1006", PlcDataType.WORD),

                // System status
                PlcDeviceAddress.NotError => new PlcRegisterInfo(deviceAddress, "M0", PlcDataType.BIT),
                PlcDeviceAddress.Automode => new PlcRegisterInfo(deviceAddress, "M1", PlcDataType.BIT),
                PlcDeviceAddress.Manmode => new PlcRegisterInfo(deviceAddress, "M2", PlcDataType.BIT),
                PlcDeviceAddress.Runningmode => new PlcRegisterInfo(deviceAddress, "M3", PlcDataType.BIT),
                PlcDeviceAddress.Stopmode => new PlcRegisterInfo(deviceAddress, "M4", PlcDataType.BIT),

                // Product code
                PlcDeviceAddress.ProductCode_ReaderStation => new PlcRegisterInfo(deviceAddress, "D4000", PlcDataType.STRING, 10),
                PlcDeviceAddress.CurrentProductSequenceNumberWord => new PlcRegisterInfo(deviceAddress, "D4010", PlcDataType.WORD),

                // PLC8_Tester registers
                PlcDeviceAddress.TestResultFromTester => new PlcRegisterInfo(deviceAddress, "D500", PlcDataType.WORD),
                PlcDeviceAddress.SequenceNumberAtTest => new PlcRegisterInfo(deviceAddress, "D502", PlcDataType.WORD),
                PlcDeviceAddress.VoltageFromTester => new PlcRegisterInfo(deviceAddress, "D504", PlcDataType.WORD),

                // Error registers M4000-M4090
                var addr when addr.ToString().StartsWith("M40") => new PlcRegisterInfo(deviceAddress, addr.ToString(), PlcDataType.BIT),

                // Time registers
                PlcDeviceAddress.TimeStopHour => new PlcRegisterInfo(deviceAddress, "D1406", PlcDataType.WORD),
                PlcDeviceAddress.TimeStopMinute => new PlcRegisterInfo(deviceAddress, "D1407", PlcDataType.WORD),
                PlcDeviceAddress.TimeStopSecond => new PlcRegisterInfo(deviceAddress, "D1408", PlcDataType.WORD),
                var addr when addr.ToString().StartsWith("Time") => new PlcRegisterInfo(deviceAddress, "D2000", PlcDataType.WORD),

                // Default
                _ => new PlcRegisterInfo(deviceAddress, "D0", PlcDataType.WORD)
            };
        }

        private void InitializeMockData()
        {
            // Initialize production counters
            _mockData[PlcDeviceAddress.NumberProductOk] = 85;
            _mockData[PlcDeviceAddress.NumberProductNg] = 12;
            _mockData[PlcDeviceAddress.NumberProductTotal] = 100;
            _mockData[PlcDeviceAddress.NumProductPresent] = 5;

            // Initialize system status
            _mockData[PlcDeviceAddress.NotError] = true;  // 1 = no error, 0 = has error
            _mockData[PlcDeviceAddress.Automode] = true;
            _mockData[PlcDeviceAddress.Manmode] = false;
            _mockData[PlcDeviceAddress.Runningmode] = true;  // System is running
            _mockData[PlcDeviceAddress.Stopmode] = false;   // System is not stopped

            // Initialize time data
            _mockData[PlcDeviceAddress.Timemodel] = 60;
            _mockData[PlcDeviceAddress.Timecurrent] = 45;
            _mockData[PlcDeviceAddress.Timedelay] = 3;
            _mockData[PlcDeviceAddress.TimeNextStep] = 15;

            // Initialize idle time registers
            _mockData[PlcDeviceAddress.TimeStopHour] = 2;    // 2 hours of stop time
            _mockData[PlcDeviceAddress.TimeStopMinute] = 30; // 30 minutes
            _mockData[PlcDeviceAddress.TimeStopSecond] = 45; // 45 seconds

            // Initialize all station completion times (1-26)
            _mockData[PlcDeviceAddress.Timecompletest1] = 45;
            _mockData[PlcDeviceAddress.Timecompletest2] = 52;
            _mockData[PlcDeviceAddress.Timecompletest3] = 38;
            _mockData[PlcDeviceAddress.Timecompletest4] = 67;
            _mockData[PlcDeviceAddress.Timecompletest5] = 41;
            _mockData[PlcDeviceAddress.Timecompletest6] = 59;
            _mockData[PlcDeviceAddress.Timecompletest7] = 43;
            _mockData[PlcDeviceAddress.Timecompletest8] = 55;
            _mockData[PlcDeviceAddress.Timecompletest9] = 48;
            _mockData[PlcDeviceAddress.Timecompletest10] = 62;
            _mockData[PlcDeviceAddress.Timecompletest11] = 39;
            _mockData[PlcDeviceAddress.Timecompletest12] = 58;
            _mockData[PlcDeviceAddress.Timecompletest13] = 44;
            _mockData[PlcDeviceAddress.Timecompletest14] = 51;
            _mockData[PlcDeviceAddress.Timecompletest15] = 47;
            _mockData[PlcDeviceAddress.Timecompletest16] = 63;
            _mockData[PlcDeviceAddress.Timecompletest17] = 42;
            _mockData[PlcDeviceAddress.Timecompletest18] = 56;
            _mockData[PlcDeviceAddress.Timecompletest19] = 49;
            _mockData[PlcDeviceAddress.Timecompletest20] = 61;
            _mockData[PlcDeviceAddress.Timecompletest21] = 40;
            _mockData[PlcDeviceAddress.Timecompletest22] = 57;
            _mockData[PlcDeviceAddress.Timecompletest23] = 46;
            _mockData[PlcDeviceAddress.Timecompletest24] = 54;
            _mockData[PlcDeviceAddress.Timecompletest25] = 50;
            _mockData[PlcDeviceAddress.Timecompletest26] = 60;

            // Initialize all station delay times (1-26)
            _mockData[PlcDeviceAddress.Timedelayst1] = 15;
            _mockData[PlcDeviceAddress.Timedelayst2] = 22;
            _mockData[PlcDeviceAddress.Timedelayst3] = 8;
            _mockData[PlcDeviceAddress.Timedelayst4] = 35;
            _mockData[PlcDeviceAddress.Timedelayst5] = 12;
            _mockData[PlcDeviceAddress.Timedelayst6] = 28;
            _mockData[PlcDeviceAddress.Timedelayst7] = 18;
            _mockData[PlcDeviceAddress.Timedelayst8] = 31;
            _mockData[PlcDeviceAddress.Timedelayst9] = 9;
            _mockData[PlcDeviceAddress.Timedelayst10] = 24;
            _mockData[PlcDeviceAddress.Timedelayst11] = 16;
            _mockData[PlcDeviceAddress.Timedelayst12] = 33;
            _mockData[PlcDeviceAddress.Timedelayst13] = 11;
            _mockData[PlcDeviceAddress.Timedelayst14] = 27;
            _mockData[PlcDeviceAddress.Timedelayst15] = 19;
            _mockData[PlcDeviceAddress.Timedelayst16] = 36;
            _mockData[PlcDeviceAddress.Timedelayst17] = 13;
            _mockData[PlcDeviceAddress.Timedelayst18] = 29;
            _mockData[PlcDeviceAddress.Timedelayst19] = 21;
            _mockData[PlcDeviceAddress.Timedelayst20] = 38;
            _mockData[PlcDeviceAddress.Timedelayst21] = 14;
            _mockData[PlcDeviceAddress.Timedelayst22] = 26;
            _mockData[PlcDeviceAddress.Timedelayst23] = 17;
            _mockData[PlcDeviceAddress.Timedelayst24] = 32;
            _mockData[PlcDeviceAddress.Timedelayst25] = 20;
            _mockData[PlcDeviceAddress.Timedelayst26] = 25;
            _mockData[PlcDeviceAddress.Timecompletest3] = 38;

            // Initialize product code
            _mockData[PlcDeviceAddress.ProductCode_ReaderStation] = "PROD-001";
            _mockData[PlcDeviceAddress.CurrentProductSequenceNumberWord] = 1;

            // Initialize PLC8_Tester data
            _mockData[PlcDeviceAddress.TestResultFromTester] = 1; // 1 = OK, 0 = NG
            _mockData[PlcDeviceAddress.SequenceNumberAtTest] = 1;
            _mockData[PlcDeviceAddress.VoltageFromTester] = 240; // 24.0V (scaled by 10)

            // Initialize all error registers to false (no errors)
            for (int i = 0; i <= 90; i++)
            {
                var errorEnumName = $"M40{i:D2}";
                if (Enum.TryParse<PlcDeviceAddress>(errorEnumName, out var errorEnum))
                {
                    if (errorEnum == PlcDeviceAddress.M4000)
                        _mockData[errorEnum] = true; // Simulate M4000 as always having an error
                    else
                        _mockData[errorEnum] = false;
                }
            }
        }

        private object GetMockValue(PlcDeviceAddress deviceAddress)
        {
            if (_mockData.ContainsKey(deviceAddress))
            {
                return _mockData[deviceAddress];
            }

            // Generate realistic mock data based on device type
            return deviceAddress switch
            {
                // Production counters
                PlcDeviceAddress.NumberProductOk => _random.Next(80, 120),
                PlcDeviceAddress.NumberProductNg => _random.Next(5, 15),
                PlcDeviceAddress.NumberProductTotal => _random.Next(95, 135),
                PlcDeviceAddress.NumProductPresent => _random.Next(0, 10),

                // System status bits
                PlcDeviceAddress.NotError => _random.NextDouble() > 0.05, // 95% no error
                PlcDeviceAddress.Automode => _random.NextDouble() > 0.2, // 80% auto mode
                PlcDeviceAddress.Manmode => _random.NextDouble() < 0.2, // 20% manual mode
                PlcDeviceAddress.Runningmode => _random.NextDouble() > 0.3, // 70% running
                PlcDeviceAddress.Stopmode => _random.NextDouble() < 0.3, // 30% stopped

                // Time values (in seconds)
                var addr when addr.ToString().StartsWith("Time") => _random.Next(30, 120),

                // Idle time registers (realistic values)
                PlcDeviceAddress.TimeStopHour => _random.Next(0, 8),    // 0-8 hours
                PlcDeviceAddress.TimeStopMinute => _random.Next(0, 59), // 0-59 minutes
                PlcDeviceAddress.TimeStopSecond => _random.Next(0, 59), // 0-59 seconds

                // Time completion registers (30-120 seconds)
                var addr when addr.ToString().StartsWith("Timecompletest") => _random.Next(30, 120),

                // Time delay registers (5-60 seconds)
                var addr when addr.ToString().StartsWith("Timedelayst") => _random.Next(5, 60),

                // Product codes
                PlcDeviceAddress.ProductCode_ReaderStation => $"PROD-{_random.Next(1, 999):D3}",
                PlcDeviceAddress.CurrentProductSequenceNumberWord => _random.Next(1, 100),

                // PLC8_Tester data
                PlcDeviceAddress.TestResultFromTester => _random.NextDouble() < 0.8 ? 1 : 0, // 80% OK, 20% NG
                PlcDeviceAddress.SequenceNumberAtTest => _random.Next(1, 100),
                PlcDeviceAddress.VoltageFromTester => _random.Next(220, 260), // 22.0V to 26.0V (scaled by 10)

                // Error registers M4000-M4090 (simulate occasional errors)
                var addr when addr.ToString().StartsWith("M40") => _random.NextDouble() < 0.05, // 5% chance of error

                // Default values
                _ => _random.Next(0, 100)
            };
        }

        private void UpdateMockData(object state)
        {
            if (!_isConnected) return;

            try
            {
                // Simulate production progress
                if (_mockData.ContainsKey(PlcDeviceAddress.NumberProductOk))
                {
                    var currentOk = (int)_mockData[PlcDeviceAddress.NumberProductOk];
                    var currentNg = (int)_mockData[PlcDeviceAddress.NumberProductNg];

                    // Randomly increment production
                    if (_random.NextDouble() < 0.3) // 30% chance to produce
                    {
                        if (_random.NextDouble() < 0.9) // 90% OK rate
                        {
                            _mockData[PlcDeviceAddress.NumberProductOk] = currentOk + 1;
                        }
                        else
                        {
                            _mockData[PlcDeviceAddress.NumberProductNg] = currentNg + 1;
                        }

                        _mockData[PlcDeviceAddress.NumberProductTotal] = currentOk + currentNg + 1;
                    }
                }

                // Simulate time variations
                foreach (var timeAddress in _mockData.Keys.Where(k => k.ToString().StartsWith("Time")).ToList())
                {
                    var currentValue = (int)_mockData[timeAddress];
                    var variation = _random.Next(-2, 3); // ±2 variation

                    // Special handling for idle time registers to keep realistic values
                    if (timeAddress == PlcDeviceAddress.TimeStopHour)
                    {
                        _mockData[timeAddress] = Math.Max(0, Math.Min(23, currentValue + variation)); // 0-23 hours
                    }
                    else if (timeAddress == PlcDeviceAddress.TimeStopMinute || timeAddress == PlcDeviceAddress.TimeStopSecond)
                    {
                        _mockData[timeAddress] = Math.Max(0, Math.Min(59, currentValue + variation)); // 0-59 minutes/seconds
                    }
                    else
                    {
                        _mockData[timeAddress] = Math.Max(0, currentValue + variation);
                    }
                }

                // Occasionally change system status
                if (_random.NextDouble() < 0.05) // 5% chance
                {
                    _mockData[PlcDeviceAddress.Runningmode] = !((bool)_mockData[PlcDeviceAddress.Runningmode]);
                    _mockData[PlcDeviceAddress.Stopmode] = !((bool)_mockData[PlcDeviceAddress.Runningmode]);
                }

                // Simulate occasional errors for testing
                if (_random.NextDouble() < 0.02) // 2% chance to trigger an error
                {
                    // Pick a random error register to activate
                    var errorRegisters = new[] { "M4000", "M4001", "M4010", "M4020", "M4070" };
                    var randomError = errorRegisters[_random.Next(errorRegisters.Length)];

                    if (Enum.TryParse<PlcDeviceAddress>(randomError, out var errorEnum))
                    {
                        _mockData[errorEnum] = true;
                        Console.WriteLine($"MockPlcService: Simulated error {randomError} activated");

                        // Clear the error after a few seconds
                        Task.Delay(5000).ContinueWith(_ => {
                            _mockData[errorEnum] = false;
                            Console.WriteLine($"MockPlcService: Simulated error {randomError} cleared");
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"MockPlcService: Error updating mock data: {ex.Message}");
            }
        }



        public void Dispose()
        {
            _dataUpdateTimer?.Dispose();
            Console.WriteLine($"MockPlcService disposed for PLC ID: {_plcId}");
        }
    }
}
