using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel tổng quát cho tất cả dashboard charts
    /// Quản lý mock data và real data cho các charts
    /// </summary>
    public class DashboardChartsViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        // Stop Times Chart Properties
        public ObservableCollection<ISeries> StopTimesSeries { get; set; } = new();
        public ObservableCollection<Axis> StopTimesXAxes { get; set; } = new();
        public int TotalStopTimes { get; private set; }

        // Quality Chart Properties (OK/NG/Rework)
        public ObservableCollection<ISeries> QualitySeries { get; set; } = new();
        public ObservableCollection<ISeries> ShiftQualitySeries { get; set; } = new();

        // Plan vs Actual Chart Properties
        public ObservableCollection<ISeries> PlanActualSeries { get; set; } = new();
        public ObservableCollection<ISeries> ShiftPlanActualSeries { get; set; } = new();

        // Idle Hours Chart Properties
        public ObservableCollection<ISeries> IdleHoursSeries { get; set; } = new();
        public ObservableCollection<ISeries> MonthlyIdleHoursSeries { get; set; } = new();

        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }

        public DashboardChartsViewModel()
        {
            _mockDataService = new MockDashboardDataService();
            
            InitializeTimer();
            LoadAllChartsData();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await RefreshAllChartsAsync();
            _refreshTimer.Start();
        }

        private async void LoadAllChartsData()
        {
            await RefreshAllChartsAsync();
        }

        private async Task RefreshAllChartsAsync()
        {
            await Task.Run(() =>
            {
                LoadStopTimesChart();
                LoadQualityCharts();
                LoadPlanActualCharts();
                LoadIdleHoursCharts();
                
                LastUpdated = DateTime.Now.ToString("HH:mm:ss");
            });
        }

        private void LoadStopTimesChart()
        {
            var data = _mockDataService.GetMockStopTimesData(5);

            StopTimesSeries.Clear();
            StopTimesXAxes.Clear();

            StopTimesSeries.Add(new ColumnSeries<int>
            {
                Values = data.Select(d => d.StopCount).ToArray(),
                Fill = new SolidColorPaint(SKColors.CornflowerBlue),
                Name = "Số lần dừng"
            });

            StopTimesXAxes.Add(new Axis
            {
                Labels = data.Select(d => d.Station).ToArray(),
                TextSize = 12,
                LabelsPaint = new SolidColorPaint(SKColors.White)
            });

            TotalStopTimes = data.Sum(d => d.StopCount);
            OnPropertyChanged(nameof(TotalStopTimes));
        }

        private void LoadQualityCharts()
        {
            // Daily Quality Chart
            var dailyQuality = _mockDataService.GetMockQualityData(false);
            QualitySeries.Clear();
            QualitySeries.Add(new PieSeries<int>
            {
                Values = new[] { dailyQuality.OK },
                Name = "OK",
                Fill = new SolidColorPaint(SKColors.Green)
            });
            QualitySeries.Add(new PieSeries<int>
            {
                Values = new[] { dailyQuality.NG },
                Name = "NG",
                Fill = new SolidColorPaint(SKColors.Red)
            });
            QualitySeries.Add(new PieSeries<int>
            {
                Values = new[] { dailyQuality.Rework },
                Name = "Rework",
                Fill = new SolidColorPaint(SKColors.Orange)
            });

            // Shift Quality Chart
            var shiftQuality = _mockDataService.GetMockQualityData(true);
            ShiftQualitySeries.Clear();
            ShiftQualitySeries.Add(new PieSeries<int>
            {
                Values = new[] { shiftQuality.OK },
                Name = "OK",
                Fill = new SolidColorPaint(SKColors.Green)
            });
            ShiftQualitySeries.Add(new PieSeries<int>
            {
                Values = new[] { shiftQuality.NG },
                Name = "NG",
                Fill = new SolidColorPaint(SKColors.Red)
            });
            ShiftQualitySeries.Add(new PieSeries<int>
            {
                Values = new[] { shiftQuality.Rework },
                Name = "Rework",
                Fill = new SolidColorPaint(SKColors.Orange)
            });
        }

        private void LoadPlanActualCharts()
        {
            // Daily Plan vs Actual
            var dailyPlan = _mockDataService.GetMockPlanActualData(false);
            PlanActualSeries.Clear();
            PlanActualSeries.Add(new ColumnSeries<int>
            {
                Values = new[] { dailyPlan.Plan },
                Name = "Kế hoạch",
                Fill = new SolidColorPaint(SKColors.LightBlue)
            });
            PlanActualSeries.Add(new ColumnSeries<int>
            {
                Values = new[] { dailyPlan.Actual },
                Name = "Thực tế",
                Fill = new SolidColorPaint(SKColors.Blue)
            });

            // Shift Plan vs Actual
            var shiftPlan = _mockDataService.GetMockPlanActualData(true);
            ShiftPlanActualSeries.Clear();
            ShiftPlanActualSeries.Add(new ColumnSeries<int>
            {
                Values = new[] { shiftPlan.Plan },
                Name = "Kế hoạch",
                Fill = new SolidColorPaint(SKColors.LightGreen)
            });
            ShiftPlanActualSeries.Add(new ColumnSeries<int>
            {
                Values = new[] { shiftPlan.Actual },
                Name = "Thực tế",
                Fill = new SolidColorPaint(SKColors.Green)
            });
        }

        private void LoadIdleHoursCharts()
        {
            // Daily Idle Hours
            var dailyIdle = _mockDataService.GetMockIdleHoursData(true);
            IdleHoursSeries.Clear();
            IdleHoursSeries.Add(new PieSeries<double>
            {
                Values = new[] { dailyIdle.IdleHours },
                Name = "Thời gian nghỉ",
                Fill = new SolidColorPaint(SKColors.Red)
            });
            IdleHoursSeries.Add(new PieSeries<double>
            {
                Values = new[] { dailyIdle.TotalHours - dailyIdle.IdleHours },
                Name = "Thời gian hoạt động",
                Fill = new SolidColorPaint(SKColors.Green)
            });

            // Monthly Idle Hours
            var monthlyIdle = _mockDataService.GetMockIdleHoursData(false);
            MonthlyIdleHoursSeries.Clear();
            MonthlyIdleHoursSeries.Add(new PieSeries<double>
            {
                Values = new[] { monthlyIdle.IdleHours },
                Name = "Thời gian nghỉ",
                Fill = new SolidColorPaint(SKColors.Orange)
            });
            MonthlyIdleHoursSeries.Add(new PieSeries<double>
            {
                Values = new[] { monthlyIdle.TotalHours - monthlyIdle.IdleHours },
                Name = "Thời gian hoạt động",
                Fill = new SolidColorPaint(SKColors.Blue)
            });
        }

        /// <summary>
        /// Lấy dashboard summary data
        /// </summary>
        public DashboardSummary GetDashboardSummary()
        {
            return _mockDataService.GetMockDashboardSummary();
        }

        /// <summary>
        /// Force refresh tất cả charts
        /// </summary>
        public async Task ForceRefreshAsync()
        {
            await RefreshAllChartsAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
