﻿using HslCommunication;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ZoomableApp.Models;
using ZoomableApp.Services;

namespace ZoomableApp.PLC
{
    public class PlcErrorEventArgs : EventArgs
    {
        public string PlcId { get; }
        public string ErrorMessage { get; }
        public PlcErrorEventArgs(string plcId, string errorMessage)
        {
            PlcId = plcId;
            ErrorMessage = errorMessage;
        }
    }
    public class PlcConnectionManager
    {
        private readonly ConcurrentDictionary<string, IPlcService> _plcServices;
        private readonly ConcurrentDictionary<string, PlcConnectionInfo> _plcConnectionInfos;
        private readonly PlcMode _plcMode;

        public event EventHandler<PlcErrorEventArgs> PlcConnectionFailed;
        public event EventHandler<PlcErrorEventArgs> PlcOperationFailed;

        public PlcConnectionManager(IEnumerable<PlcConnectionInfo> initialConfigs, PlcMode plcMode = PlcMode.Real)
        {
            _plcServices = new ConcurrentDictionary<string, IPlcService>();
            _plcConnectionInfos = new ConcurrentDictionary<string, PlcConnectionInfo>();
            _plcMode = plcMode;

            if (initialConfigs != null)
            {
                foreach (var config in initialConfigs)
                {
                    // Tạo một bản sao của config để tránh các vấn đề tham chiếu không mong muốn
                    var storedConfig = new PlcConnectionInfo
                    {
                        Id = config.Id,
                        Name = config.Name,
                        IpAddress = config.IpAddress,
                        Port = config.Port,
                        AutoConnectOnStartup = config.AutoConnectOnStartup,
                        IsConnected = false, // Trạng thái ban đầu
                        ConnectionStatus = "Not Connected"
                    };
                    _plcConnectionInfos.TryAdd(storedConfig.Id, storedConfig);

                    // TẠO IPlcService ngay tại đây sử dụng Factory
                    var plcServiceInstance = PlcServiceFactory.CreatePlcService(storedConfig, _plcMode);

                    // Gán thông tin bổ sung nếu là MitsubishiPlcService
                    if (plcServiceInstance is MitsubishiPlcService mitsubishiService)
                    {
                        mitsubishiService.OwnerManager = this;
                        mitsubishiService.ServicePlcId = storedConfig.Id;
                    }
                    _plcServices.TryAdd(storedConfig.Id, plcServiceInstance);
                    Console.WriteLine($"PlcConnectionManager: Initialized service for PLC ID '{storedConfig.Id}' ({storedConfig.Name})");
                }
            }
        }

        public async Task ConnectAllAutoConnectPlcsAsync()
        {
            var connectTasks = new List<Task>();
            foreach (var config in _plcConnectionInfos.Values)
            {
                if (config.AutoConnectOnStartup) // Chỉ kết nối những PLC có cờ AutoConnect
                {
                    Console.WriteLine($"PlcConnectionManager: Auto-connecting PLC ID '{config.Id}' ({config.Name}) at {config.IpAddress}:{config.Port}");
                    connectTasks.Add(ConnectPlcAsync(config.Id)); // ConnectPlcAsync đã có logic cập nhật config.ConnectionStatus
                }
            }
            if (connectTasks.Any())
            {
                await Task.WhenAll(connectTasks);
                Console.WriteLine("PlcConnectionManager: Finished attempting to auto-connect all configured PLCs.");
            }
            else
            {
                Console.WriteLine("PlcConnectionManager: No PLCs configured for auto-connect.");
            }
        }

        public IEnumerable<PlcConnectionInfo> GetAllPlcConnectionInfos()
        {
            return _plcConnectionInfos.Values.Select(ci => new PlcConnectionInfo
            {
                Id = ci.Id,
                Name = ci.Name,
                IpAddress = ci.IpAddress,
                Port = ci.Port,
                AutoConnectOnStartup = ci.AutoConnectOnStartup,
                IsConnected = ci.IsConnected,
                ConnectionStatus = ci.ConnectionStatus
            }).ToList();
        }


        public IPlcService GetPlcService(string plcId)
        {
            _plcServices.TryGetValue(plcId, out var service);
            return service;
        }

        public PlcConnectionInfo GetPlcConnectionInfo(string plcId)
        {
            _plcConnectionInfos.TryGetValue(plcId, out var info);
            return info;
        }

        /// <summary>
        /// Kiểm tra xem PLC có đang kết nối không
        /// </summary>
        public bool IsPlcConnected(string plcId)
        {
            if (_plcServices.TryGetValue(plcId, out var service))
            {
                return service.IsConnected;
            }
            return false;
        }


        public async Task<bool> ConnectPlcAsync(string plcId)
        {
            if (!_plcConnectionInfos.TryGetValue(plcId, out var config))
            {
                Console.WriteLine($"PlcConnectionManager: Config for PLC ID '{plcId}' not found.");
                PlcConnectionFailed?.Invoke(this, new PlcErrorEventArgs(plcId, "Configuration not found.")); // Kích hoạt event ra ngoài
                return false;
            }

            if (!_plcServices.TryGetValue(plcId, out var serviceInstance))
            {
                // Điều này không nên xảy ra nếu constructor đã tạo service
                Console.WriteLine($"PlcConnectionManager: Service instance for PLC ID '{plcId}' not found. Recreating.");
                serviceInstance = PlcServiceFactory.CreatePlcService(config, _plcMode);
                _plcServices.TryAdd(plcId, serviceInstance); // Thêm nếu chưa có
            }

            // Nếu service đã được tạo và đang kết nối, không cần làm gì (hoặc ngắt kết nối cũ nếu muốn ép kết nối lại)
            if (serviceInstance.IsConnected)
            {
                Console.WriteLine($"PlcConnectionManager: PLC ID '{plcId}' is already connected.");
                // Cập nhật lại config.IsConnected nếu nó chưa đúng
                config.IsConnected = true;
                config.ConnectionStatus = "Connected";
                return true;
            }

            // Cập nhật IP, Port cho service instance phòng trường hợp config được sửa đổi (ít xảy ra với load từ file)
            serviceInstance.PlcIpAddress = config.IpAddress;
            serviceInstance.PlcPort = config.Port;

            var connectAttemptResult = await serviceInstance.ConnectAsync();
            bool success = connectAttemptResult.IsSuccess;

            // Cập nhật trạng thái trong PlcConnectionInfo đã lưu trữ
            config.IsConnected = success;
            config.ConnectionStatus = success ? "Connected" : $"Failed: {connectAttemptResult.Message}";

            if (!success)
            {
                PlcConnectionFailed?.Invoke(this, new PlcErrorEventArgs(plcId, $"Failed to connect to {config.Name} ({config.IpAddress}:{config.Port}). Reason: {connectAttemptResult.Message}"));
            }
            return success;
        }

        public async Task DisconnectPlcAsync(string plcId)
        {
            if (_plcServices.TryGetValue(plcId, out var serviceToDisconnect))
            {
                if (serviceToDisconnect.IsConnected) // Chỉ ngắt nếu đang kết nối
                {
                    await serviceToDisconnect.DisconnectAsync();
                }
            }

            if (_plcConnectionInfos.TryGetValue(plcId, out var config))
            {
                config.IsConnected = false;
                config.ConnectionStatus = "Disconnected";
            }
        }

        public async Task DisconnectAllAsync()
        {
            var disconnectTasks = new List<Task>();
            foreach (var plcId in _plcServices.Keys.ToList())
            {
                disconnectTasks.Add(DisconnectPlcAsync(plcId));
            }
            if (disconnectTasks.Any())
            {
                await Task.WhenAll(disconnectTasks);
            }
        }

        internal void ReportPlcOperationError(string plcId, string operation, string errorMessage)
        {
            PlcOperationFailed?.Invoke(this, new PlcErrorEventArgs(plcId, $"Operation '{operation}' on {plcId} failed: {errorMessage}"));
        }
    }
}
