﻿using ZoomableApp.Models;
using ZoomableApp.Services;

namespace ZoomableApp
{
    /// <summary>
    /// Interaction logic for LoginWindow.xaml
    /// </summary>
    public partial class LoginWindow : Window
    {
        private readonly UserService _userService;
        private readonly List<WorkShift> _workShifts;
        private readonly IRfidReaderService _rfidReaderService;

        public LoginWindow()
        {
            InitializeComponent();
            _userService = ServiceContainer.GetService<UserService>();
            _workShifts = WorkShift.GetAvailableShifts();
            _rfidReaderService = new OmnikeyRfidReaderService();

            _rfidReaderService.CardScanned += RfidReaderService_CardScanned;

            Loaded += async (_, __) => await InitializeRfidServiceAsync();
            Closed += (_, __) => _rfidReaderService.Dispose();

            InitializeShiftComboBoxes();
            SetInitialFocus();
        }

        private void InitializeShiftComboBoxes()
        {
            RfidShiftComboBox.ItemsSource = _workShifts;
            RfidShiftComboBox.DisplayMemberPath = "DisplayText";
            RfidShiftComboBox.SelectedIndex = 0; // Chọn ca đầu tiên mặc định

            AccountShiftComboBox.ItemsSource = _workShifts;
            AccountShiftComboBox.DisplayMemberPath = "DisplayText";
            AccountShiftComboBox.SelectedIndex = 0; // Chọn ca đầu tiên mặc định
        }

        private void SetInitialFocus()
        {
            // Focus vào tab RFID và textbox mã thẻ mặc định
            LoginTabControl.SelectedIndex = 0;
            RfidCodeTextBox.Focus();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            _ = ProcessLoginAsync();
        }

        // Xử lý sự kiện KeyDown trên toàn bộ Window
        private void Window_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                _ = ProcessLoginAsync();
                e.Handled = true;
            }
        }

        private async Task ProcessLoginAsync()
        {
            try
            {
                ShowStatusMessage("Đang xác thực...", false);
                LoginButton.IsEnabled = false;

                User? user = null;
                WorkShift? selectedShift = null;

                // Kiểm tra tab nào đang được chọn
                if (LoginTabControl.SelectedIndex == 0) // RFID Tab
                {
                    user = await ProcessRfidLoginAsync();
                    selectedShift = RfidShiftComboBox.SelectedItem as WorkShift;
                }
                else // Account Tab
                {
                    user = await ProcessAccountLoginAsync();
                    selectedShift = AccountShiftComboBox.SelectedItem as WorkShift;
                }

                if (user != null && selectedShift != null)
                {
                    UserSession.Login(user, selectedShift);
                    ShowStatusMessage("Đăng nhập thành công! Đang mở ứng dụng...", false);

                    await Task.Delay(500); // Hiển thị thông báo một chút

                    MainWindow mainWindow = new MainWindow();
                    mainWindow.Show();
                    this.Close();
                }
                else
                {
                    ShowStatusMessage("Thông tin đăng nhập không chính xác. Vui lòng thử lại.", true);
                    ResetFocus();
                }
            }
            catch (Exception ex)
            {
                ShowStatusMessage($"Lỗi: {ex.Message}", true);
            }
            finally
            {
                LoginButton.IsEnabled = true;
            }
        }

        private async Task<User?> ProcessRfidLoginAsync()
        {
            string rfidCode = RfidCodeTextBox.Text.Trim();

            if (string.IsNullOrWhiteSpace(rfidCode))
            {
                ShowStatusMessage("Vui lòng quét thẻ RFID của bạn.", true);
                return null;
            }

            return await _userService.AuthenticateRfidAsync(rfidCode);
        }

        private async Task<User?> ProcessAccountLoginAsync()
        {
            string username = UsernameTextBox.Text.Trim();
            string password = PasswordBox.Password;

            Console.WriteLine($"Login attempt - Username: '{username}', Password: '{password}'");

            if (string.IsNullOrWhiteSpace(username))
            {
                ShowStatusMessage("Vui lòng nhập tên đăng nhập.", true);
                return null;
            }

            if (string.IsNullOrWhiteSpace(password))
            {
                ShowStatusMessage("Vui lòng nhập mật khẩu.", true);
                return null;
            }

            var result = await _userService.AuthenticateUserAsync(username, password);
            Console.WriteLine($"Authentication result: {(result != null ? "SUCCESS" : "FAILED")}");

            return result;
        }

        private void ResetFocus()
        {
            if (LoginTabControl.SelectedIndex == 0) // RFID Tab
            {
                RfidCodeTextBox.Focus();
                RfidCodeTextBox.SelectAll();
            }
            else // Account Tab
            {
                UsernameTextBox.Focus();
                UsernameTextBox.SelectAll();
            }
        }

        private void ShowStatusMessage(string message, bool isError)
        {
            StatusTextBlock.Text = message;
            StatusTextBlock.Visibility = Visibility.Visible;

            if (isError)
            {
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(231, 76, 60)); // Red
                StatusTextBlock.Background = new SolidColorBrush(Color.FromRgb(255, 235, 235)); // Light red
            }
            else
            {
                StatusTextBlock.Foreground = new SolidColorBrush(Color.FromRgb(39, 174, 96)); // Green
                StatusTextBlock.Background = new SolidColorBrush(Color.FromRgb(235, 255, 235)); // Light green
            }
        }

        private async Task InitializeRfidServiceAsync()
        {
            await _rfidReaderService.StartListeningAsync();
            // Always show RFID warning since we can't reliably detect RFID availability
            ShowRfidWarning();
        }

        private void ShowRfidWarning()
        {
            RfidWarningTextBlock.Visibility = Visibility.Visible;
        }

        private async void RfidReaderService_CardScanned(string code)
        {
            await Dispatcher.InvokeAsync(() =>
            {
                RfidCodeTextBox.Text = code;
            });

            await Dispatcher.InvokeAsync(async () =>
            {
                if (LoginTabControl.SelectedIndex == 0)
                {
                    await ProcessLoginAsync();
                }
            });
        }
    }
}
