using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using ZoomableApp.Services;
using ZoomableApp.Models;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel cho Shift Quality Chart (Panel 4 - Right)
    /// Hiển thị OK/NG/Rework ca hiện tại dưới dạng pie chart
    /// </summary>
    public class ShiftQualityChartViewModel : INotifyPropertyChanged
    {
        private readonly MockDashboardDataService _mockDataService;
        private System.Timers.Timer _refreshTimer;
        private string _lastUpdated = "";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<ISeries> Series { get; set; } = new();
        
        public string LastUpdated
        {
            get => _lastUpdated;
            set
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }
        private int _okQuantity;
        public int OkQuantity
        {
            get => _okQuantity;
            set
            {
                if (_okQuantity != value)
                {
                    _okQuantity = value;
                    OnPropertyChanged(nameof(OkQuantity));
                }
            }
        }
        private int _ngQuantity;
        public int NgQuantity
        {
            get => _ngQuantity;
            set
            {
                if (_ngQuantity != value)
                {
                    _ngQuantity = value;
                    OnPropertyChanged(nameof(NgQuantity));
                }
            }
        }
        private int _reworkQuantity;
        public int ReworkQuantity
        {
            get => _reworkQuantity;
            set
            {
                if (_reworkQuantity != value)
                {
                    _reworkQuantity = value;
                    OnPropertyChanged(nameof(ReworkQuantity));
                }
            }
        }
        private int _totalQuantity;
        public int TotalQuantity
        {
            get => _totalQuantity;
            set
            {
                if (_totalQuantity != value)
                {
                    _totalQuantity = value;
                    OnPropertyChanged(nameof(TotalQuantity));
                }
            }
        }

        private double _qualityRate;
        public double QualityRate
        {
            get => _qualityRate;
            set
            {
                if (_qualityRate != value)
                {
                    _qualityRate = value;
                    OnPropertyChanged(nameof(QualityRate));
                }
            }
        }
        public string CurrentShift { get; private set; } = "";

        public ShiftQualityChartViewModel()
        {
            _mockDataService = new MockDashboardDataService();
            CurrentShift = GetCurrentShiftName();
            InitializeTimer();
            LoadDataAsync();
        }

        private void InitializeTimer()
        {
            var interval = ConfigurationService.GetDashboardRefreshInterval();
            _refreshTimer = new System.Timers.Timer(interval);
            _refreshTimer.Elapsed += async (s, e) => await LoadDataAsync();
            _refreshTimer.Start();
        }

        private async Task LoadDataAsync()
        {
            await Task.Run(() =>
            {
                try
                {
                    // Kiểm tra PLC mode
                    var plcMode = ConfigurationService.GetPlcMode();
                    
                    if (plcMode == PlcMode.Mock)
                    {
                        LoadMockData();
                    }
                    else
                    {
                        // TODO: Load real data from PLC/Database
                        // Fallback to mock data for now
                        LoadMockData();
                    }

                    LastUpdated = DateTime.Now.ToString("HH:mm:ss");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"ShiftQualityChart: Error loading data: {ex.Message}");
                    LoadMockData(); // Fallback
                }
            });
        }

        private void LoadMockData()
        {
            var qualityData = _mockDataService.GetMockQualityData(true); // Current shift data
            
            OkQuantity = qualityData.OK;
            NgQuantity = qualityData.NG;
            ReworkQuantity = qualityData.Rework;
            TotalQuantity = OkQuantity + NgQuantity + ReworkQuantity;
            QualityRate = TotalQuantity > 0 ? Math.Round((double)OkQuantity / TotalQuantity * 100, 1) : 0;

            Series.Clear();

            // Tạo pie chart cho OK/NG/Rework
            if (OkQuantity > 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { OkQuantity },
                    Name = $"OK ({OkQuantity})",
                    Fill = new SolidColorPaint(SKColors.Green),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });
            }

            if (NgQuantity > 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { NgQuantity },
                    Name = $"NG ({NgQuantity})",
                    Fill = new SolidColorPaint(SKColors.Red),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });
            }

            if (ReworkQuantity > 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { ReworkQuantity },
                    Name = $"Rework ({ReworkQuantity})",
                    Fill = new SolidColorPaint(SKColors.Orange),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => $"{point.PrimaryValue}"
                });
            }

            // Nếu không có data, hiển thị placeholder
            if (TotalQuantity == 0)
            {
                Series.Add(new PieSeries<int>
                {
                    Values = new[] { 1 },
                    Name = "Chưa có dữ liệu",
                    Fill = new SolidColorPaint(SKColors.Gray),
                    Stroke = new SolidColorPaint(SKColors.White) { StrokeThickness = 2 },
                    DataLabelsPaint = new SolidColorPaint(SKColors.White),
                    DataLabelsSize = 12,
                    DataLabelsPosition = LiveChartsCore.Measure.PolarLabelsPosition.Middle,
                    DataLabelsFormatter = point => "No Data"
                });
            }

            // Update properties for binding
            OnPropertyChanged(nameof(OkQuantity));
            OnPropertyChanged(nameof(NgQuantity));
            OnPropertyChanged(nameof(ReworkQuantity));
            OnPropertyChanged(nameof(TotalQuantity));
            OnPropertyChanged(nameof(QualityRate));

            Console.WriteLine($"ShiftQuality: OK={OkQuantity}, NG={NgQuantity}, Rework={ReworkQuantity}, Rate={QualityRate:F1}%");
        }

        private string GetCurrentShiftName()
        {
            var hour = DateTime.Now.Hour;
            
            if (hour >= 6 && hour < 14)
                return "Ca sáng (06:00-14:00)";
            else if (hour >= 14 && hour < 22)
                return "Ca chiều (14:00-22:00)";
            else if (hour >= 8 && hour < 17)
                return "Ca hành chính (08:00-17:00)";
            else
                return "Ca đêm (22:00-06:00)";
        }

        /// <summary>
        /// Force refresh data
        /// </summary>
        public async Task RefreshAsync()
        {
            await LoadDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public void Dispose()
        {
            _refreshTimer?.Stop();
            _refreshTimer?.Dispose();
        }
    }
}
