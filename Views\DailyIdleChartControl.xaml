﻿<UserControl x:Class="ZoomableApp.Views.DailyIdleChartControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.WPF;assembly=LiveChartsCore.SkiaSharpView.WPF"
             xmlns:local="clr-namespace:ZoomableApp.Views"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border CornerRadius="8" Padding="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10" HorizontalAlignment="Left">
                <TextBlock Text="📊 Thời gian dừng hàng ngày" FontSize="18" FontWeight="Bold" Foreground="White"/>
                <TextBlock Text="{Binding LastUpdated, StringFormat=' (Updated: {0})'}" FontSize="12" Foreground="#BDC3C7" VerticalAlignment="Bottom" Margin="5,0,0,2"/>
            </StackPanel>

            <!-- Chart -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <!-- Legends -->
                <StackPanel Grid.Column="0" Orientation="Vertical" HorizontalAlignment="Center" Margin="8,0,0,0" MinHeight="140" VerticalAlignment="Center">
                    <TextBlock Text="Đã dừng: " Foreground="White" FontSize="16" FontWeight="Bold" MinHeight="35"/>
                    <TextBlock Text="{Binding ActualIdleHours, StringFormat='{}{0:F1}h'}" Foreground="#E74C3C" FontSize="14" FontWeight="Bold" MinHeight="35"/>
                    <TextBlock Text="Còn lại: " Foreground="White" FontSize="16" FontWeight="Bold" MinHeight="35"/>
                    <TextBlock Text="{Binding RemainingIdleHours, StringFormat='{}{0:F1}h'}" Foreground="#348F50" FontSize="14" FontWeight="Bold" MinHeight="35"/>
                </StackPanel>
                    
                    <!-- <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,2">
                        <TextBlock Text="📈 Idle Usage: " Foreground="#BDC3C7" FontSize="14"/>
                        <TextBlock Text="{Binding IdleUtilizationRate, StringFormat='{}{0:F1}%'}" Foreground="#3498DB" FontSize="14" FontWeight="Bold"/>
                    </StackPanel> -->
                <!-- Pie Chart -->
                <lvc:PieChart Grid.Column="1"
                              x:Name="DailyIdleChart"
                              Width="Auto"
                              Height="Auto"
                              Series="{Binding Series}"
                              LegendPosition="Bottom"
                              InitialRotation="-225"
                              MaxAngle="270"
                              Background="Transparent"/>
            </Grid>
        </Grid>
    </Border>
</UserControl>
