using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using ZoomableApp.Services;
using ZoomableApp.ViewModels;

namespace ZoomableApp.Views
{
    public partial class MainlineContentView : UserControl
    {
        private bool _isDragging = false;
        private Point _lastMousePosition;
        private MainWindow _mainWindow;
        private MainlineViewModel _viewModel;
        private ILoggerService _logger;

        public MainlineContentView()
        {
            InitializeComponent();

            try
            {
                _logger = ServiceContainer.GetService<ILoggerService>();
                _logger?.LogInfo("[MainlineContentView] Initializing Mainline Content View");

                // Initialize ViewModel
                _viewModel = new MainlineViewModel();
                DataContext = _viewModel;
                StopTimesChartControl.DataContext = _viewModel.StopChartViewModel;

                // Ensure child controls maintain their own DataContext
                this.Loaded += MainlineContentView_Loaded;

                // Get the MainWindow reference for button events
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    _mainWindow = mainWindow;
                    _logger?.LogInfo("[MainlineContentView] MainWindow reference set successfully");
                }
                else
                {
                    _logger?.LogWarning("[MainlineContentView] Could not get MainWindow reference");
                }

                // Load the Mainline layout into the canvas
                LoadMainlineLayout();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[MainlineContentView] Error initializing: {ex.Message}");
                _logger?.LogError($"[MainlineContentView] Error initializing: {ex.Message}", ex);
            }
        }

        #region Zoom/Pan Event Handlers

        private void ViewportBorder_MouseWheel(object sender, MouseWheelEventArgs e)
        {
            try
            {
                var delta = e.Delta > 0 ? 1.1 : 0.9;
                ViewScaleTransform.ScaleX *= delta;
                ViewScaleTransform.ScaleY *= delta;

                // Limit zoom levels
                if (ViewScaleTransform.ScaleX < 0.1)
                {
                    ViewScaleTransform.ScaleX = 0.1;
                    ViewScaleTransform.ScaleY = 0.1;
                }
                else if (ViewScaleTransform.ScaleX > 5.0)
                {
                    ViewScaleTransform.ScaleX = 5.0;
                    ViewScaleTransform.ScaleY = 5.0;
                }

                _logger?.LogDebug($"[MainlineContentView] Zoom level: {ViewScaleTransform.ScaleX:F2}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error in mouse wheel: {ex.Message}");
            }
        }

        private void ViewportBorder_MouseDown(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (e.LeftButton == MouseButtonState.Pressed)
                {
                    _isDragging = true;
                    _lastMousePosition = e.GetPosition(ViewportBorder);
                    ViewportBorder.CaptureMouse();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error in mouse down: {ex.Message}");
            }
        }

        private void ViewportBorder_MouseMove(object sender, MouseEventArgs e)
        {
            try
            {
                if (_isDragging && e.LeftButton == MouseButtonState.Pressed)
                {
                    var currentPosition = e.GetPosition(ViewportBorder);
                    var deltaX = currentPosition.X - _lastMousePosition.X;
                    var deltaY = currentPosition.Y - _lastMousePosition.Y;

                    ViewTranslateTransform.X += deltaX;
                    ViewTranslateTransform.Y += deltaY;

                    _lastMousePosition = currentPosition;
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error in mouse move: {ex.Message}");
            }
        }

        private void ViewportBorder_MouseUp(object sender, MouseButtonEventArgs e)
        {
            try
            {
                if (_isDragging)
                {
                    _isDragging = false;
                    ViewportBorder.ReleaseMouseCapture();
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error in mouse up: {ex.Message}");
            }
        }

        #endregion

        #region Event Handlers

        private void MainlineContentView_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Ensure DailyPlanChart maintains its own DataContext
                if (DailyPlanChart != null)
                {
                    Console.WriteLine("[MainlineContentView] Ensuring DailyPlanChart DataContext is properly set");
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error in Loaded event: {ex.Message}", ex);
            }
        }

        #endregion

        #region Button Event Handlers

        private void ResetButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ViewScaleTransform.ScaleX = 0.5;
                ViewScaleTransform.ScaleY = 0.5;
                ViewTranslateTransform.X = 0;
                ViewTranslateTransform.Y = 0;
                _logger?.LogInfo("[MainlineContentView] View reset to default");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error resetting view: {ex.Message}");
            }
        }

        private void RefreshPlanButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delegate to MainWindow's button handler
                //_mainWindow?.RefreshPlanButton_Click(sender, e);
                _logger?.LogInfo("[MainlineContentView] Plan refresh requested");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error refreshing plan: {ex.Message}");
            }
        }

        private void MarkCompleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delegate to MainWindow's button handler
                //_mainWindow?.MarkCompleteButton_Click(sender, e);
                _logger?.LogInfo("[MainlineContentView] Mark complete requested");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error marking complete: {ex.Message}");
            }
        }

        private void StartNextButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delegate to MainWindow's button handler
                //_mainWindow?.StartNextButton_Click(sender, e);
                _logger?.LogInfo("[MainlineContentView] Start next requested");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error starting next: {ex.Message}");
            }
        }

        #endregion

        #region Layout Loading

        private void LoadMainlineLayout()
        {
            try
            {
                // Clear existing content
                ZoomPanCanvas.Children.Clear();

                // Get the MainlineLayout instance from MainWindow instead of creating a new one
                if (_mainWindow != null)
                {
                    var mainlineLayoutInstance = _mainWindow.GetMainlineLayoutInstance();
                    if (mainlineLayoutInstance != null)
                    {
                        // Use the existing instance that already has PLC manager set
                        ZoomPanCanvas.Children.Add(mainlineLayoutInstance);

                        // Position the layout
                        Canvas.SetLeft(mainlineLayoutInstance, 50);
                        Canvas.SetTop(mainlineLayoutInstance, 50);

                        _logger?.LogInfo("[MainlineContentView] Using existing MainlineLayout instance from MainWindow");
                    }
                    else
                    {
                        // Fallback: create new instance and set PLC manager
                        var mainlineLayout = new Layouts.MainlineLayout();
                        var plcManager = _mainWindow.GetPlcManager();
                        if (plcManager != null)
                        {
                            mainlineLayout.SetPlcManager(plcManager);
                            _logger?.LogInfo("[MainlineContentView] PLC manager set for new Mainline layout");
                        }
                        else
                        {
                            _logger?.LogWarning("[MainlineContentView] PLC manager is null, creating layout without PLC manager");
                        }

                        // Add to canvas
                        ZoomPanCanvas.Children.Add(mainlineLayout);

                        // Position the layout
                        Canvas.SetLeft(mainlineLayout, 50);
                        Canvas.SetTop(mainlineLayout, 50);

                        _logger?.LogInfo("[MainlineContentView] Created new MainlineLayout instance as fallback");
                    }
                }
                else
                {
                    _logger?.LogError("[MainlineContentView] MainWindow reference is null, cannot load layout");
                }

                _logger?.LogInfo("[MainlineContentView] Mainline layout loaded successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[MainlineContentView] Error loading Mainline layout: {ex.Message}", ex);
                Console.WriteLine($"[MainlineContentView] Error loading Mainline layout: {ex.Message}");
            }
        }

        #endregion
    }
}
