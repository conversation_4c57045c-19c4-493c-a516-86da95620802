using System.ComponentModel;

namespace ZoomableApp.Models
{
    /// <summary>
    /// Represents fault information from PLC system
    /// </summary>
    public class PlcFaultInfo : INotifyPropertyChanged
    {
        private string _faultCode = string.Empty;
        private List<int> _stations = new List<int>();
        private string _message = string.Empty;
        private bool _isActive;
        private DateTime _timestamp;

        /// <summary>
        /// Fault code from PLC (e.g., "01", "02", "E101", "Z3")
        /// </summary>
        public string FaultCode
        {
            get => _faultCode;
            set
            {
                if (_faultCode != value)
                {
                    _faultCode = value;
                    OnPropertyChanged(nameof(FaultCode));
                }
            }
        }

        /// <summary>
        /// List of station numbers affected by this fault
        /// </summary>
        public List<int> Stations
        {
            get => _stations;
            set
            {
                if (_stations != value)
                {
                    _stations = value;
                    OnPropertyChanged(nameof(Stations));
                    OnPropertyChanged(nameof(StationsText));
                    OnPropertyChanged(nameof(DisplayText));
                }
            }
        }

        /// <summary>
        /// Fault message description (e.g., "Sensor lỗi", "Quá nhiệt")
        /// </summary>
        public string Message
        {
            get => _message;
            set
            {
                if (_message != value)
                {
                    _message = value;
                    OnPropertyChanged(nameof(Message));
                }
            }
        }

        /// <summary>
        /// Whether this fault is currently active
        /// </summary>
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        /// <summary>
        /// Timestamp when fault was detected
        /// </summary>
        public DateTime Timestamp
        {
            get => _timestamp;
            set
            {
                if (_timestamp != value)
                {
                    _timestamp = value;
                    OnPropertyChanged(nameof(Timestamp));
                }
            }
        }

        /// <summary>
        /// Formatted text showing affected stations
        /// </summary>
        public string StationsText => Stations.Count > 0 ? $"Trạm {string.Join(", ", Stations)}" : "";

        /// <summary>
        /// Display text for UI binding
        /// </summary>
        public string DisplayText => IsActive ? $"{StationsText}: {Message}" : "Không có lỗi";

        /// <summary>
        /// Color for UI display based on fault status
        /// </summary>
        public string StatusColor => IsActive ? "#FF6B6B" : "#4ECDC4";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public PlcFaultInfo()
        {
            _timestamp = DateTime.Now;
        }

        public PlcFaultInfo(string faultCode, List<int> stations, string message, bool isActive = false)
        {
            _faultCode = faultCode;
            _stations = stations;
            _message = message;
            _isActive = isActive;
            _timestamp = DateTime.Now;
        }
    }
}
