﻿using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using System;
using System.Windows;
using System.Windows.Controls;
using ZoomableApp.ViewModels;
using ZoomableApp.PLC;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for ShiftQualityChartControl.xaml
    /// </summary>
    public partial class ShiftQualityChartControl : UserControl
    {
        private ShiftQualityChartViewModel _viewModel;

        public ShiftQualityChartControl()
        {
            InitializeComponent();
            ShiftQualityChart.LegendTextPaint = new SolidColorPaint(SKColors.White);

            // Delay initialization until the control is loaded and has proper parent context
            this.Loaded += ShiftQualityChartControl_Loaded;
        }

        private void ShiftQualityChartControl_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                // Delay initialization to ensure parent DataContext is set first
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    InitializeViewModel();
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ShiftQualityChartControl] Error in Loaded event: {ex.Message}");
            }
        }

        private void InitializeViewModel()
        {
            try
            {
                PlanViewModel planViewModel = null;
                PlcConnectionManager plcManager = null;

                // Try to get dependencies from MainWindow first (most reliable)
                var mainWindow = System.Windows.Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    planViewModel = mainWindow.GetPlanViewModel();
                    plcManager = mainWindow.GetPlcManager();
                    Console.WriteLine("[ShiftQualityChartControl] Got dependencies from MainWindow");
                }

                // Fallback: Try to get from parent ContentView's ViewModel
                if (planViewModel == null || plcManager == null)
                {
                    var parentContentView = FindParentContentView();
                    if (parentContentView != null)
                    {
                        // Get from ContentView's ViewModel (MainlineViewModel or InspectionViewModel)
                        if (parentContentView.DataContext is MainlineViewModel mainlineVM)
                        {
                            planViewModel = planViewModel ?? mainlineVM.GetPlanViewModel();
                            plcManager = plcManager ?? mainlineVM.GetPlcManager();
                            Console.WriteLine("[ShiftQualityChartControl] Got dependencies from MainlineViewModel (fallback)");
                        }
                        else if (parentContentView.DataContext is InspectionViewModel inspectionVM)
                        {
                            planViewModel = planViewModel ?? inspectionVM.GetPlanViewModel();
                            plcManager = plcManager ?? inspectionVM.GetPlcManager();
                            Console.WriteLine("[ShiftQualityChartControl] Got dependencies from InspectionViewModel (fallback)");
                        }
                    }
                }

                _viewModel = new ShiftQualityChartViewModel();

                // Force set DataContext to our ViewModel (override any inherited DataContext)
                this.DataContext = _viewModel;

                Console.WriteLine($"[ShiftQualityChartControl] ViewModel initialized - DataContext set to: {this.DataContext?.GetType().Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[ShiftQualityChartControl] Error initializing ViewModel: {ex.Message}");
            }
        }

        private FrameworkElement FindParentContentView()
        {
            var parent = this.Parent;
            while (parent != null)
            {
                if (parent is Views.MainlineContentView || parent is Views.InspectionContentView)
                {
                    return parent as FrameworkElement;
                }

                if (parent is FrameworkElement fe)
                {
                    parent = fe.Parent;
                }
                else
                {
                    break;
                }
            }
            return null;
        }
    }
}
