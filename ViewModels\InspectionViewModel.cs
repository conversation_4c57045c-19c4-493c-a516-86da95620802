using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using ZoomableApp.Services;
using ZoomableApp.PLC;
using ZoomableApp.Models;
using ZoomableApp.ViewModels;

namespace ZoomableApp.ViewModels
{
    /// <summary>
    /// ViewModel for Inspection application
    /// Contains all properties needed for InspectionContentView bindings
    /// </summary>
    public class InspectionViewModel : INotifyPropertyChanged
    {
        private PlcConnectionManager _plcManager;
        private ILoggerService _logger;

        // System status properties
        private string _systemStatus = "Đang khởi tạo...";
        private string _systemTotalOK = "--";
        private string _systemTotalNG = "--";
        private bool _isPlcConnected = false;

        // Idle time properties
        private string _todayTotalIdleTime = "0:00";
        private string _todayRemainingIdleTime = "2:00";
        private string _monthTotalIdleTime = "0:00";
        private string _monthRemainingIdleTime = "40:00";

        // Plan properties
        private string _todayDateText = DateTime.Now.ToString("dd/MM/yyyy");
        private string _currentProductText = "Chưa có sản phẩm";
        private string _editableModelName = "";
        private bool _isEditingModel = false;
        private ObservableCollection<DailyPlanItem> _dailyPlanItems = new ObservableCollection<DailyPlanItem>();
        private DailyPlanItem _selectedItem;

        public event PropertyChangedEventHandler PropertyChanged;

        #region Properties

        public string SystemStatus
        {
            get => _systemStatus;
            set
            {
                if (_systemStatus != value)
                {
                    _systemStatus = value;
                    OnPropertyChanged(nameof(SystemStatus));
                }
            }
        }

        public string SystemTotalOK
        {
            get => _systemTotalOK;
            set
            {
                if (_systemTotalOK != value)
                {
                    _systemTotalOK = value;
                    OnPropertyChanged(nameof(SystemTotalOK));
                }
            }
        }

        public string SystemTotalNG
        {
            get => _systemTotalNG;
            set
            {
                if (_systemTotalNG != value)
                {
                    _systemTotalNG = value;
                    OnPropertyChanged(nameof(SystemTotalNG));
                }
            }
        }

        public string TodayTotalIdleTime
        {
            get => _todayTotalIdleTime;
            set
            {
                if (_todayTotalIdleTime != value)
                {
                    _todayTotalIdleTime = value;
                    OnPropertyChanged(nameof(TodayTotalIdleTime));
                }
            }
        }

        public string TodayRemainingIdleTime
        {
            get => _todayRemainingIdleTime;
            set
            {
                if (_todayRemainingIdleTime != value)
                {
                    _todayRemainingIdleTime = value;
                    OnPropertyChanged(nameof(TodayRemainingIdleTime));
                }
            }
        }

        public string MonthTotalIdleTime
        {
            get => _monthTotalIdleTime;
            set
            {
                if (_monthTotalIdleTime != value)
                {
                    _monthTotalIdleTime = value;
                    OnPropertyChanged(nameof(MonthTotalIdleTime));
                }
            }
        }

        public string MonthRemainingIdleTime
        {
            get => _monthRemainingIdleTime;
            set
            {
                if (_monthRemainingIdleTime != value)
                {
                    _monthRemainingIdleTime = value;
                    OnPropertyChanged(nameof(MonthRemainingIdleTime));
                }
            }
        }

        public string TodayDateText
        {
            get => _todayDateText;
            set
            {
                if (_todayDateText != value)
                {
                    _todayDateText = value;
                    OnPropertyChanged(nameof(TodayDateText));
                }
            }
        }

        public string CurrentProductText
        {
            get => _currentProductText;
            set
            {
                if (_currentProductText != value)
                {
                    _currentProductText = value;
                    OnPropertyChanged(nameof(CurrentProductText));
                }
            }
        }

        public string EditableModelName
        {
            get => _editableModelName;
            set
            {
                if (_editableModelName != value)
                {
                    _editableModelName = value;
                    OnPropertyChanged(nameof(EditableModelName));
                }
            }
        }

        public bool IsEditingModel
        {
            get => _isEditingModel;
            set
            {
                if (_isEditingModel != value)
                {
                    _isEditingModel = value;
                    OnPropertyChanged(nameof(IsEditingModel));
                }
            }
        }

        public ObservableCollection<DailyPlanItem> DailyPlanItems
        {
            get => _dailyPlanItems;
            set
            {
                if (_dailyPlanItems != value)
                {
                    _dailyPlanItems = value;
                    OnPropertyChanged(nameof(DailyPlanItems));
                }
            }
        }

        public DailyPlanItem SelectedItem
        {
            get => _selectedItem;
            set
            {
                if (_selectedItem != value)
                {
                    _selectedItem = value;
                    OnPropertyChanged(nameof(SelectedItem));
                }
            }
        }

        public bool IsPlcConnected
        {
            get => _isPlcConnected;
            set
            {
                if (_isPlcConnected != value)
                {
                    _isPlcConnected = value;
                    OnPropertyChanged(nameof(IsPlcConnected));
                }
            }
        }

        // Layout property - always "Inspection" for this ViewModel
        public string CurrentLayout => "Inspection";

        #endregion

        #region Commands

        public ICommand StartNextCommand { get; private set; }
        public ICommand StartEditModelCommand { get; private set; }
        public ICommand SaveModelCommand { get; private set; }
        public ICommand CancelEditModelCommand { get; private set; }
        public ICommand RefreshCommand { get; private set; }
        public ICommand MarkCompleteCommand { get; private set; }

        #endregion

        public InspectionViewModel()
        {
            _logger = ServiceContainer.GetService<ILoggerService>();
            _logger?.LogInfo("[InspectionVM] Initializing Inspection ViewModel");

            // Initialize commands
            InitializeCommands();

            // Initialize services - same as MainWindow
            InitializeServices();

            // Load daily plan data
            LoadDailyPlan();
        }

        private void InitializeCommands()
        {
            StartNextCommand = new RelayCommand<object>(ExecuteStartNext, CanExecuteStartNext);
            StartEditModelCommand = new RelayCommand<object>(ExecuteStartEditModel);
            SaveModelCommand = new RelayCommand<object>(ExecuteSaveModel, CanExecuteSaveModel);
            CancelEditModelCommand = new RelayCommand<object>(ExecuteCancelEditModel);
            RefreshCommand = new RelayCommand<object>(ExecuteRefresh);
            MarkCompleteCommand = new RelayCommand<object>(ExecuteMarkComplete, CanExecuteMarkComplete);
        }

        private void InitializeServices()
        {
            try
            {
                var plcMode = ConfigurationService.GetPlcMode();
                _logger?.LogInfo($"[InspectionVM] Initializing with PLC mode: {plcMode}");

                // Initialize PLC manager - same as MainWindow
                var plcConfigs = ConfigLoader.LoadPlcConfigs();
                _plcManager = new PlcConnectionManager(plcConfigs, plcMode);

                _logger?.LogInfo("[InspectionVM] Services initialized successfully");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionVM] Error initializing services: {ex.Message}", ex);
            }
        }

        // Public method to get PlanViewModel for dashboard charts
        public PlanViewModel GetPlanViewModel()
        {
            // Get the actual PlanViewModel from ServiceContainer that has the loaded data
            var planViewModel = ServiceContainer.GetService<PlanViewModel>();
            if (planViewModel != null)
            {
                _logger?.LogInfo("[InspectionVM] Returning PlanViewModel from ServiceContainer");
                return planViewModel;
            }

            // Fallback: Try to get from MainWindow
            var mainWindow = System.Windows.Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                var mainWindowPlanVM = mainWindow.GetPlanViewModel();
                if (mainWindowPlanVM != null)
                {
                    _logger?.LogInfo("[InspectionVM] Returning PlanViewModel from MainWindow");
                    return mainWindowPlanVM;
                }
            }

            _logger?.LogWarning("[InspectionVM] No PlanViewModel found, returning null");
            return null;
        }

        // Public method to get PlcManager for dashboard charts
        public PlcConnectionManager GetPlcManager()
        {
            return _plcManager;
        }

        #region Command Methods

        private void ExecuteStartNext(object parameter)
        {
            // Implement start next logic
            _logger?.LogInfo("[InspectionVM] Start Next command executed");
        }

        private bool CanExecuteStartNext(object parameter)
        {
            return SelectedItem != null && SelectedItem.Status == PlanItemStatus.NotStarted;
        }

        private void ExecuteStartEditModel(object parameter)
        {
            IsEditingModel = true;
            EditableModelName = CurrentProductText;
        }

        private void ExecuteSaveModel(object parameter)
        {
            CurrentProductText = EditableModelName;
            IsEditingModel = false;
            _logger?.LogInfo($"[InspectionVM] Model saved: {CurrentProductText}");
        }

        private bool CanExecuteSaveModel(object parameter)
        {
            return IsEditingModel && !string.IsNullOrWhiteSpace(EditableModelName);
        }

        private void ExecuteCancelEditModel(object parameter)
        {
            IsEditingModel = false;
            EditableModelName = "";
        }

        private void ExecuteRefresh(object parameter)
        {
            // Implement refresh logic
            _logger?.LogInfo("[InspectionVM] Refresh command executed");
        }

        private void ExecuteMarkComplete(object parameter)
        {
            // Implement mark complete logic
            _logger?.LogInfo("[InspectionVM] Mark Complete command executed");
        }

        private bool CanExecuteMarkComplete(object parameter)
        {
            return SelectedItem != null && SelectedItem.Status == PlanItemStatus.InProgress;
        }

        #endregion

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void LoadDailyPlan()
        {
            try
            {
                _logger?.LogInfo("[InspectionVM] Loading daily plan data");

                // Get PlanViewModel from ServiceContainer
                var planViewModel = ServiceContainer.GetService<PlanViewModel>();
                if (planViewModel == null)
                {
                    _logger?.LogWarning("[InspectionVM] PlanViewModel not found in ServiceContainer");
                    return;
                }

                // Refresh PlanViewModel data first
                if (planViewModel.RefreshCommand.CanExecute(null))
                {
                    planViewModel.RefreshCommand.Execute(null);
                }

                // Load from PlanViewModel
                LoadDailyPlanFromPlanViewModel(planViewModel);
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionVM] Error loading daily plan: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        private void LoadDailyPlanFromPlanViewModel(PlanViewModel planViewModel)
        {
            try
            {
                var dailyData = planViewModel.DailyPlanData;
                if (dailyData == null || dailyData.Rows.Count == 0)
                {
                    _logger?.LogInfo("[InspectionVM] No daily data available from PlanViewModel");
                    DailyPlanItems.Clear();
                    CurrentProductText = "Chưa có sản phẩm";
                    return;
                }

                DailyPlanItems.Clear();

                foreach (System.Data.DataRow row in dailyData.Rows)
                {
                    var item = new DailyPlanItem
                    {
                        No = row["No"]?.ToString() ?? "",
                        Type = row["Type"]?.ToString() ?? "",
                        ModelName = row["Model name"]?.ToString() ?? "",
                        Market = row["Market"]?.ToString() ?? "",
                        Quantity = row["Q'ty"]?.ToString() ?? "",
                        StartTime = row["Start time"]?.ToString() ?? "",
                        StopTime = row["Stop time"]?.ToString() ?? "",
                        Status = PlanItemStatus.NotStarted
                    };

                    DailyPlanItems.Add(item);
                }

                // Update current product if we have items
                if (DailyPlanItems.Count > 0)
                {
                    // Set first item as current working
                    var firstItem = DailyPlanItems.First();
                    firstItem.Status = PlanItemStatus.InProgress;
                    SelectedItem = firstItem;
                    CurrentProductText = firstItem.ModelName;
                }
                else
                {
                    CurrentProductText = "Chưa có sản phẩm";
                }

                _logger?.LogInfo($"[InspectionVM] Loaded {DailyPlanItems.Count} plan items");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"[InspectionVM] Error loading daily plan from PlanViewModel: {ex.Message}");
                DailyPlanItems.Clear();
                CurrentProductText = "Chưa có sản phẩm";
            }
        }

        public async void Dispose()
        {
            if (_plcManager != null)
            {
                await _plcManager.DisconnectAllAsync();
            }
            _logger?.LogInfo("[InspectionVM] Inspection ViewModel disposed");
        }
    }
}