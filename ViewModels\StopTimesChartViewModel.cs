﻿using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;
using LiveChartsCore;
using LiveChartsCore.SkiaSharpView;
using LiveChartsCore.SkiaSharpView.Painting;
using SkiaSharp;
using Microsoft.Data.Sqlite;
using System.IO;
using ZoomableApp.Services;
using ZoomableApp.Models;
using ZoomableApp.PLC;
using System.ComponentModel;

public class StopTimesChartViewModel : INotifyPropertyChanged
{
    public ObservableCollection<ISeries> Series { get; set; }
    public ObservableCollection<Axis> XAxes { get; set; }
    public event PropertyChangedEventHandler? PropertyChanged;
    protected virtual void OnPropertyChanged(string propertyName)
        => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

    private int _totalStopTimes;
    private string _lastUpdated;

    private System.Timers.Timer _refreshTimer;
    private readonly MockDashboardDataService _mockDataService = new MockDashboardDataService();
    private readonly ILoggerService _logger;

    public int TotalStopTimes
    {
        get => _totalStopTimes;
        set
        {
            if (_totalStopTimes != value)
            {
                _totalStopTimes = value;
                OnPropertyChanged(nameof(TotalStopTimes));
            }
        }
    }
    public string LastUpdated
    {
        get => _lastUpdated;
        set
        {
            if (_lastUpdated != value)
            {
                _lastUpdated = value;
                OnPropertyChanged(nameof(LastUpdated));
            }
        }
    }
    public StopTimesChartViewModel()
    {
        // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
        if (ServiceContainer.IsRegistered<ILoggerService>())
        {
            _logger = ServiceContainer.GetService<ILoggerService>();
        }

        Series = new ObservableCollection<ISeries>();
        XAxes = new ObservableCollection<Axis>();

        Task.Run(async () =>
        {
            await ServiceContainer.WaitForPlcConnectionAsync();
            _logger?.LogInfo("[StopChart] PLC connections ready, starting timer");
            InitializeTimer();
            await LoadDataAsync(); // Tải dữ liệu lần đầu sau khi kết nối
        });
    }

    private void InitializeTimer()
    {
        var interval = ConfigurationService.GetDashboardRefreshInterval();
        _refreshTimer = new System.Timers.Timer(interval);
        _refreshTimer.Elapsed += async (s, e) =>
        {
            var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
            if (plcManager?.IsPlcConnected("PLC_MainLine") == true)
            {
                _logger?.LogInfo("[StopChart] PLC_MainLine connected, loading data...");
                await LoadDataAsync();
            }
            else
            {
                _logger?.LogWarning("[StopChart] PLC_MainLine not connected, skipping data load");
            }
        };
        _refreshTimer.Start();
    }

    private async void LoadData()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        var data = await Task.Run(() => GetTopStopTimes(10)); // lấy top 10 máy

        Series.Clear();
        XAxes.Clear();

        // Tạo gradient color cho columns
        var colors = new[]
        {
            SKColors.Red,        // Highest stop count
            SKColors.Orange,
            SKColors.Yellow,
            SKColors.LightGreen,
            SKColors.Green       // Lowest stop count
        };

        Series.Add(new ColumnSeries<int>
        {
            Values = data.Select(d => d.StopCount).ToArray(),
            Fill = new SolidColorPaint(SKColors.CornflowerBlue),
            Name = "Số lần dừng",

            DataLabelsPaint = new SolidColorPaint(SKColors.White),
            DataLabelsPosition = LiveChartsCore.Measure.DataLabelsPosition.Top,
            DataLabelsSize = 14
        });

        XAxes.Add(new Axis
        {
            Labels = data.Select(d => d.Station).ToArray(),
            TextSize = 12,
            LabelsPaint = new SolidColorPaint(SKColors.White)
        });

        TotalStopTimes = data.Sum(d => d.StopCount);
        LastUpdated = DateTime.Now.ToString("HH:mm:ss");
    }

    

    private (string Station, int StopCount)[] GetTopStopTimes(int topN)
    {
        var result = new List<(string Station, int StopCount)>();

        try
        {
            // Kiểm tra PLC mode hiện tại
            var plcMode = ConfigurationService.GetPlcMode();

            if (plcMode == PlcMode.Mock)
            {
                // Sử dụng MockDashboardDataService
                return _mockDataService.GetMockStopTimesData(topN);
            }

            // Đọc từ PLC registers cho Real mode
            var plcManager = ServiceContainer.GetService<PlcConnectionManager>();
            if (plcManager != null)
            {
                var plcService = plcManager.GetPlcService("PLC_MainLine");
                if (plcService != null)
                {
                    // Đọc số lần dừng từ các register Numberstopst1 đến Numberstopst26
                    var stopData = new List<(string Station, int StopCount)>();

                    for (int i = 1; i <= 26; i++)
                    {
                        try
                        {
                            var stopRegisterName = $"Numberstopst{i}";
                            if (Enum.TryParse<PlcDeviceAddress>(stopRegisterName, out var stopRegister))
                            {
                                var readResult = plcService.ReadAsync(stopRegister).Result;
                                if (readResult.IsSuccess)
                                {
                                    var stopCount = Convert.ToInt32(readResult.Value);
                                    stopData.Add(($"ST{i}", stopCount));
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"Error reading stop count for ST{i}: {ex.Message}");
                        }
                    }

                    // Sắp xếp theo số lần dừng giảm dần và lấy top N
                    result = stopData
                        .OrderByDescending(x => x.StopCount)
                        .Take(topN)
                        .ToList();

                    Console.WriteLine($"StopTimesChart: Read {result.Count} stop times from PLC");
                }
                else
                {
                    Console.WriteLine("StopTimesChart: PLC service not available");
                }
            }
            else
            {
                Console.WriteLine("StopTimesChart: PLC not connected");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"StopTimesChart: Error loading data from PLC: {ex.Message}");
            _logger?.LogError($"StopTimesChart: Error loading data from PLC: {ex.Message}", ex);
        }

        // Nếu không có dữ liệu từ PLC, sử dụng mock data service
        if (result.Count == 0)
        {
            Console.WriteLine("StopTimesChart: No PLC data available, using mock data");
            return _mockDataService.GetMockStopTimesData(topN);
        }

        return result.ToArray();
    }
}
