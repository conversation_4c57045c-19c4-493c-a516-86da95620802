using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace ZoomableApp.Converters
{
    /// <summary>
    /// Converter để làm tối màu cho hiệu ứng 3D
    /// </summary>
    public class ColorDarkenConverter : IValueConverter
    {
        public static readonly ColorDarkenConverter Instance = new ColorDarkenConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is Color color)
            {
                return DarkenColor(color, 0.2); // Làm tối 20%
            }
            
            if (value is SolidColorBrush brush)
            {
                return DarkenColor(brush.Color, 0.2);
            }

            return value;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }

        private Color DarkenColor(Color color, double factor)
        {
            return Color.FromArgb(
                color.A,
                (byte)(color.R * (1 - factor)),
                (byte)(color.G * (1 - factor)),
                (byte)(color.B * (1 - factor))
            );
        }
    }
}
