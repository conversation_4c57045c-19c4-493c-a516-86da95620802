using System;
using PCSC;
using PCSC.Monitoring;
using PCSC.Iso7816;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Dịch vụ đọc thẻ RFID cho đầu đọc HID Omnikey 5422/5122 sử dụng PC/SC.
    /// </summary>
    public class OmnikeyRfidReaderService : IRfidReaderService
    {
        private readonly ISCardContext _context;
        private readonly ISCardMonitor _monitor;
        private readonly ILoggerService _logger;
        private bool _started;

        public event Action<string>? CardScanned;

        public OmnikeyRfidReaderService()
        {
            try
            {
                _context = ContextFactory.Instance.Establish(SCardScope.System);
                _monitor = MonitorFactory.Instance.Create(SCardScope.System);

                // Kiểm tra xem ServiceContainer đã được khởi tạo chưa
                if (ServiceContainer.IsRegistered<ILoggerService>())
                {
                    _logger = ServiceContainer.GetService<ILoggerService>();
                }
                else
                {
                    // Fallback nếu ServiceContainer chưa được khởi tạo
                    Console.WriteLine("[OmnikeyRfidReaderService] ServiceContainer not initialized yet, using Console.WriteLine");
                }

                _monitor.CardInserted += OnCardInserted;
            }
            catch (PCSC.Exceptions.NoServiceException ex)
            {
                Console.WriteLine($"[OmnikeyRfidReaderService] RFID Service not available: {ex.Message}");
                // Sẽ được xử lý trong StartListeningAsync
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[OmnikeyRfidReaderService] Initialization error: {ex.Message}");
            }
        }

        private void OnCardInserted(object? sender, CardStatusEventArgs args)
        {
            ReadCard(args.ReaderName);
        }

        private void ReadCard(string readerName)
        {
            try
            {
                using var isoReader = new IsoReader(_context, readerName, SCardShareMode.Shared, SCardProtocol.Any, false);
                var apdu = new CommandApdu(IsoCase.Case2Short, isoReader.ActiveProtocol)
                {
                    CLA = 0xFF,
                    INS = 0xCA,
                    P1 = 0x00,
                    P2 = 0x00,
                    Le = 0
                };
                var response = isoReader.Transmit(apdu);
                if (response.HasData)
                {
                    var uid = BitConverter.ToString(response.GetData()).Replace("-", string.Empty);
                    CardScanned?.Invoke(uid);
                }
            }
            catch (Exception ex)
            {
                if (_logger != null)
                {
                    _logger.LogError($"RFID read error: {ex.Message}", ex);
                }
                else
                {
                    Console.WriteLine($"RFID read error: {ex.Message}");
                }
            }
        }

        public async Task StartListeningAsync()
        {
            if (_started) return;

            try
            {
                if (_context == null || _monitor == null)
                {
                    Console.WriteLine("[OmnikeyRfidReaderService] RFID context/monitor not available");
                    return;
                }

                var readers = _context.GetReaders();
                if (readers is { Length: > 0 })
                {
                    _monitor.Start(readers);
                    _started = true;
                    _logger?.LogInfo("RFID reader service started successfully");
                }
                else
                {
                    Console.WriteLine("[OmnikeyRfidReaderService] No RFID readers found");
                }
            }
            catch (PCSC.Exceptions.NoServiceException ex)
            {
                Console.WriteLine($"[OmnikeyRfidReaderService] RFID Service not available: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"RFID service start error: {ex.Message}", ex);
                Console.WriteLine($"[OmnikeyRfidReaderService] RFID service start error: {ex.Message}");
            }

            await Task.CompletedTask;
        }

        // ShowRfidWarning method removed as requested

        public void StopListening()
        {
            if (_started)
            {
                _monitor.Cancel();
                _started = false;
            }
        }

        public void Dispose()
        {
            StopListening();
            _monitor.Dispose();
            _context.Dispose();
        }
    }
}
