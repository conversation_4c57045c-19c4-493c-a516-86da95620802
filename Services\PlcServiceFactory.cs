using System;
using System.Diagnostics;
using ZoomableApp.Models;
using ZoomableApp.PLC;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Factory class for creating PLC service instances based on mode
    /// Supports both real and mock PLC services
    /// </summary>
    public static class PlcServiceFactory
    {
        /// <summary>
        /// Creates a PLC service instance based on the specified mode
        /// </summary>
        /// <param name="plcId">Unique identifier for the PLC</param>
        /// <param name="mode">Operating mode (Real or Mock)</param>
        /// <returns>IPlcService implementation</returns>
        public static IPlcService CreatePlcService(string plcId, PlcMode mode)
        {
            Console.WriteLine($"PlcServiceFactory: Creating {mode} PLC service for ID: {plcId}");
            
            return mode switch
            {
                PlcMode.Mock => new MockPlcService(plcId),
                PlcMode.Real => new MitsubishiPlcService(plcId),
                _ => throw new ArgumentException($"Unsupported PLC mode: {mode}", nameof(mode))
            };
        }

        /// <summary>
        /// Creates a PLC service with connection info
        /// </summary>
        /// <param name="connectionInfo">PLC connection configuration</param>
        /// <param name="mode">Operating mode</param>
        /// <returns>Configured IPlcService implementation</returns>
        public static IPlcService CreatePlcService(PlcConnectionInfo connectionInfo, PlcMode mode)
        {
            var service = CreatePlcService(connectionInfo.Id, mode);
            
            // Configure connection parameters
            service.PlcIpAddress = connectionInfo.IpAddress;
            service.PlcPort = connectionInfo.Port;
            
            Console.WriteLine($"PlcServiceFactory: Configured {mode} service for {connectionInfo.Name} ({connectionInfo.IpAddress}:{connectionInfo.Port})");
            
            return service;
        }

        /// <summary>
        /// Gets the display name for a PLC mode
        /// </summary>
        /// <param name="mode">PLC mode</param>
        /// <returns>Human-readable mode name</returns>
        public static string GetModeDisplayName(PlcMode mode)
        {
            return mode switch
            {
                PlcMode.Real => "Thực tế (Real PLC)",
                PlcMode.Mock => "Mô phỏng (Mock/Demo)",
                _ => "Không xác định"
            };
        }

        /// <summary>
        /// Determines if a mode requires physical PLC hardware
        /// </summary>
        /// <param name="mode">PLC mode to check</param>
        /// <returns>True if physical hardware is required</returns>
        public static bool RequiresHardware(PlcMode mode)
        {
            return mode == PlcMode.Real;
        }
    }
}
