﻿<UserControl x:Class="ZoomableApp.SharedControls.StationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp.SharedControls"
             xmlns:localConverters="clr-namespace:ZoomableApp.Converters"
             xmlns:localEnums="clr-namespace:ZoomableApp.Models"
             mc:Ignorable="d" 
             d:DesignHeight="200" x:Name="ucRoot"
             BorderBrush="Black" BorderThickness="1"
             Height="140">
    <UserControl.Resources>
        <localConverters:BooleanToVisibilityConverter x:Key="BoolToVis"/>
        <localConverters:EnumToVisibilityConverter x:Key="EnumToVisConv"/>
        <!-- Thêm các converters khác nếu cần -->
    </UserControl.Resources>

    <Grid Background="{Binding ElementName=ucRoot, Path=StationDisplayBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- Station Number -->
            <RowDefinition Height="*"/>
            <!-- Product Info / Device Status Icon -->
            <RowDefinition Height="Auto"/>
            <!-- Specific Controls (Worker Button, Rework) -->
        </Grid.RowDefinitions>

        <!-- Station Number (Luôn hiển thị) -->
        <Border Background="#50000000" Grid.Row="0">
            <TextBlock Text="{Binding ElementName=ucRoot, Path=StationNumber}"
                       HorizontalAlignment="Center" VerticalAlignment="Center"
                       FontSize="10" FontWeight="Bold" Foreground="White" Padding="2,0"/>
        </Border>

        <!-- Product Info & Device Status Icon Area -->
        <Grid Grid.Row="1" Margin="2">
            <!-- Product Visual (đơn giản là 1 hình chữ nhật) -->
            <Rectangle x:Name="ProductVisual"
                       Fill="DimGray" Opacity="0.7"
                       Width="30" Height="20"
                       HorizontalAlignment="Center" VerticalAlignment="Center"
                       Visibility="{Binding ElementName=ucRoot, Path=HasProduct, Converter={StaticResource BoolToVis}}">
                <Rectangle.RenderTransform>
                    <TranslateTransform X="0" Y="0"/>
                </Rectangle.RenderTransform>
                <!-- Animation cho chuyển động sản phẩm sẽ được áp dụng ở đây sau -->
            </Rectangle>

            <!-- Product Info Text (Hiển thị khi có sản phẩm) -->
            <StackPanel VerticalAlignment="Bottom" HorizontalAlignment="Center" Margin="0,0,0,1"
                        Visibility="{Binding ElementName=ucRoot, Path=HasProduct, Converter={StaticResource BoolToVis}}">
                <TextBlock Text="{Binding ElementName=ucRoot, Path=TestResult}"
                           FontSize="8" HorizontalAlignment="Center"
                           Visibility="{Binding ElementName=ucRoot, Path=IsTestStationType, Converter={StaticResource BoolToVis}}"/>
                <TextBlock Panel.ZIndex="999" Text="{Binding ElementName=ucRoot, Path=OperationTime}"
                           FontSize="8" HorizontalAlignment="Center"
                           Visibility="{Binding ElementName=ucRoot, Path=IsWorkerStationType, Converter={StaticResource BoolToVis}}"/>
            </StackPanel>

            <!-- Device Status Icon (Ví dụ: một Ellipse nhỏ ở góc) -->
            <Ellipse Width="8" Height="8" HorizontalAlignment="Right" VerticalAlignment="Top" Margin="3"
                     ToolTip="{Binding ElementName=ucRoot, Path=DeviceStatus}">
                <Ellipse.Style>
                    <Style TargetType="Ellipse">
                        <Setter Property="Fill" Value="Transparent"/>
                        <!-- Mặc định -->
                        <Style.Triggers>
                            <DataTrigger Binding="{Binding ElementName=ucRoot, Path=DeviceStatus}" Value="{x:Static localEnums:DeviceOperationalStatus.Idle}">
                                <Setter Property="Fill" Value="LightGray"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ucRoot, Path=DeviceStatus}" Value="{x:Static localEnums:DeviceOperationalStatus.Running}">
                                <Setter Property="Fill" Value="LimeGreen"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ucRoot, Path=DeviceStatus}" Value="{x:Static localEnums:DeviceOperationalStatus.Error}">
                                <Setter Property="Fill" Value="Red"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ucRoot, Path=DeviceStatus}" Value="{x:Static localEnums:DeviceOperationalStatus.Maintenance}">
                                <Setter Property="Fill" Value="Orange"/>
                            </DataTrigger>
                            <DataTrigger Binding="{Binding ElementName=ucRoot, Path=DeviceStatus}" Value="{x:Static localEnums:DeviceOperationalStatus.Off}">
                                <Setter Property="Fill" Value="DarkSlateGray"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Ellipse.Style>
            </Ellipse>
        </Grid>

        <!-- Specific Controls Area (Hàng dưới cùng) -->
        <Grid Grid.Row="2">
            <!-- Nút bấm cho Worker Station -->
            <Button Content="Xong" FontSize="9" Padding="3,1" Margin="2"
                    HorizontalAlignment="Center" VerticalAlignment="Center"
                    Visibility="{Binding ElementName=ucRoot, Path=IsWorkerStationType, Converter={StaticResource BoolToVis}}"
                    Click="WorkerDoneButton_Click_Placeholder"/>

            <!-- Nhãn Rework (cho trạm ReworkRoller) -->
            <Border Background="#CCFF6347" CornerRadius="3" Padding="3,0" Margin="2"
                    HorizontalAlignment="Center" VerticalAlignment="Center"
                    Visibility="{Binding ElementName=ucRoot, Path=StationType, ConverterParameter={x:Static localEnums:StationType.ReworkRoller},
                       Converter={StaticResource EnumToVisibilityConverter}}">
                <!-- Cần EnumToVisibilityConverter (hoặc 1 converter so sánh giá trị enum) -->
                <TextBlock Text="REWORK" FontSize="8" Foreground="White" FontWeight="SemiBold"/>
            </Border>
        </Grid>
    </Grid>
</UserControl>
