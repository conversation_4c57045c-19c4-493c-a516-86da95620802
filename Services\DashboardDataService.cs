using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using ZoomableApp.Models;

namespace ZoomableApp.Services
{
    /// <summary>
    /// Dashboard data service - uses DatabaseService for all database operations
    /// </summary>
    public class DashboardDataService
    {
        private readonly IDatabaseService _databaseService;
        private readonly string _planFilePath;

        public DashboardDataService(IDatabaseService databaseService)
        {
            _databaseService = databaseService;
            _planFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data", "plan.xlsx");

            // Load idle time Excel file path from settings
            var excelSettings = ConfigLoader.LoadExcelSettings();
            var idleFilePath = !string.IsNullOrEmpty(excelSettings.IdlePlansFilePath)
                ? excelSettings.IdlePlansFilePath
                : "C:\\Project-Dat\\PANA\\idleplans.xlsx";
        }

        /// <summary>
        /// Get daily production summary using DatabaseService
        /// </summary>
        public async Task<ProductionSummaryData> GetDailyProductionSummaryAsync(DateTime date)
        {
            try
            {
                var summary = await _databaseService.GetDailyProductionSummaryAsync(date);

                // Get plan quantity from Excel
                summary.PlanQuantity = await GetPlanQuantityAsync(date);

                return summary;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting daily production summary: {ex.Message}");
                return new ProductionSummaryData();
            }
        }

        /// <summary>
        /// Get shift production summary using DatabaseService
        /// </summary>
        public async Task<ProductionSummaryData> GetShiftProductionSummaryAsync(DateTime date, string shiftName)
        {
            try
            {
                var summary = await _databaseService.GetShiftProductionSummaryAsync(date, shiftName);

                // Get plan quantity for this shift
                summary.PlanQuantity = await GetShiftPlanQuantityAsync(date, shiftName);

                return summary;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting shift production summary: {ex.Message}");
                return new ProductionSummaryData();
            }
        }

        private TimeSpan GetIdleTimeFromWorksheet(object worksheet, string type)
        {
            // Tạm thời return giá trị mặc định, sẽ implement Excel reading sau
            return type == "Daily" ? TimeSpan.FromHours(2) : TimeSpan.FromHours(40);
        }

        private async Task<TimeSpan> GetUsedIdleTimeAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                return await _databaseService.GetUsedIdleTimeAsync(startDate, endDate);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting used idle time: {ex.Message}");
                return TimeSpan.Zero;
            }
        }

        private async Task<int> GetPlanQuantityAsync(DateTime date)
        {
            // Tạm thời return giá trị mẫu, sẽ implement Excel reading sau
            await Task.Delay(1);
            return 100; // Giá trị mẫu
        }

        private async Task<int> GetShiftPlanQuantityAsync(DateTime date, string shiftName)
        {
            // For night shift, use the correct plan date
            var planDate = WorkShiftHelper.GetShiftPlanDate(date, shiftName);
            var dailyPlan = await GetPlanQuantityAsync(planDate);

            Console.WriteLine($"DashboardDataService: GetShiftPlanQuantity - Original date: {date:yyyy-MM-dd}, Shift: {shiftName}, Plan date: {planDate:yyyy-MM-dd}");

            // Tạm thời chia đều plan quantity cho các ca
            return dailyPlan / 3; // Giả sử có 3 ca
        }

        /// <summary>
        /// Get idle time data from Excel file
        /// </summary>
        public async Task<IdleTimeData> GetIdleTimeDataAsync()
        {
            try
            {
                await Task.Delay(1); // Async placeholder

                // Use IdlePlanService to get plan data
                var idlePlanService = new IdlePlanService();
                var dailyPlan = idlePlanService.GetDailyPlanIdleTime(DateTime.Now);
                var monthlyPlan = idlePlanService.GetMonthlyPlanIdleTime();

                var idleTimeData = new IdleTimeData
                {
                    DailyAllowedIdleTime = TimeSpan.FromHours(dailyPlan),
                    MonthlyAllowedIdleTime = TimeSpan.FromHours(monthlyPlan),
                    DailyUsedIdleTime = TimeSpan.Zero, // Will be updated from PLC
                    MonthlyUsedIdleTime = TimeSpan.Zero // Will be updated from database
                };

                Console.WriteLine($"DashboardDataService: Loaded idle time data - Daily plan: {dailyPlan:F1}h, Monthly plan: {monthlyPlan:F1}h");
                return idleTimeData;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"DashboardDataService: Error loading idle time data: {ex.Message}");
                return new IdleTimeData(); // Return default values
            }
        }
    }
}
