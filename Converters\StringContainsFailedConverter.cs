﻿using System;
using System.Globalization;
using System.Windows.Data;

namespace ZoomableApp.Converters
{
    public class StringContainsFailedConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string str)
            {
                return str.IndexOf("Failed", StringComparison.OrdinalIgnoreCase) >= 0;
            }
            return false;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
