using System;
using System.Windows.Controls;
using ZoomableApp.ViewModels;
using ZoomableApp.Services;

namespace ZoomableApp.Views
{
    /// <summary>
    /// Interaction logic for ReportPage.xaml
    /// </summary>
    public partial class ReportPage : UserControl
    {
        public ReportPage()
        {
            try
            {
                InitializeComponent();

                // Get DatabaseService from ServiceContainer
                var databaseService = ServiceContainer.GetService<IDatabaseService>();
                DataContext = new ReportViewModel(databaseService);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in ReportPage constructor: {ex.Message}");
                // Fallback: Initialize with error ViewModel
                InitializeComponent();
                DataContext = new ReportViewModel(); // No-parameter constructor shows config error
            }
        }

        /// <summary>
        /// Constructor with dependency injection
        /// </summary>
        public ReportPage(IDatabaseService databaseService)
        {
            InitializeComponent();
            DataContext = new ReportViewModel(databaseService);
        }
    }
}
