﻿<UserControl x:Class="ZoomableApp.Layouts.InspectionLayout"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:ZoomableApp.Layouts"
             xmlns:shared="clr-namespace:ZoomableApp.SharedControls"
             mc:Ignorable="d" 
             d:DesignHeight="200" d:DesignWidth="3600"
             Background="LightBlue">
    <Grid>
        <!-- Layer for Stations -->
        <StackPanel x:Name="StationStackPanel" Orientation="Horizontal" VerticalAlignment="Top"
                    Background="Transparent">
            <!-- StationControls will be added here from code-behind -->
        </StackPanel>

        <!-- Layer for Products (Animated) -->
        <Canvas x:Name="ProductLayerCanvas"
                VerticalAlignment="Top"
                Height="{Binding ElementName=StationStackPanel, Path=ActualHeight}"
                Width="{Binding ElementName=StationStackPanel, Path=ActualWidth}"
                Background="Transparent" />
    </Grid>
</UserControl>
